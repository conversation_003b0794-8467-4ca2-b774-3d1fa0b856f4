<template>
  <view class="order-stats">
    <view class="stats-item" v-for="(item, index) in statsData" :key="index">
      <view class="stats-icon">
        <image :src="item.icon" mode="aspectFit" />
        <view class="stats-badge" v-if="item.count > 0">{{ item.count }}</view>
      </view>
      <view class="stats-label">{{ item.label }}</view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'OrderStats',
  props: {
    // 统计数据
    statsData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {}
  }
}
</script>

<style lang="scss" scoped>
.order-stats {
  display: flex;
  justify-content: space-around;
  align-items: center;
  background: #FFFFFF;
  border-radius: 20rpx;
  padding: 40rpx 20rpx;
  margin: 30rpx;
  box-shadow: 0rpx 4rpx 20rpx 0rpx rgba(0, 0, 0, 0.05);
}

.stats-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.stats-icon {
  position: relative;
  margin-bottom: 20rpx;
  
  image {
    width: 60rpx;
    height: 60rpx;
  }
  
  .stats-badge {
    position: absolute;
    top: -10rpx;
    right: -10rpx;
    background: #FF4757;
    color: #FFFFFF;
    font-size: 20rpx;
    font-weight: 500;
    padding: 4rpx 8rpx;
    border-radius: 20rpx;
    min-width: 30rpx;
    text-align: center;
    line-height: 1;
  }
}

.stats-label {
  font-size: 24rpx;
  color: #666666;
  text-align: center;
  line-height: 1.2;
}
</style>
