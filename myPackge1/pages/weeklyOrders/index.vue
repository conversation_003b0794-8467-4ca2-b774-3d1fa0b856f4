<template>
    <view>
		<CustomNavbar  :bgColor="'#08BA7E'" :titleColor="'#FFFFFF'" />
        <view class="header">
            <view class="searchs_section">
                <view class="search_box"> 
                    <view class="search">
                        <u-search v-model="searchText" @search="handleSearch" placeholder="搜索" bgColor="#FFFFFF"
                            placeholderColor="#A5B2AC" :use-action-icon="true" searchIconSize="30rpx"
                            searchIcon="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/statistical/sousuo.png"
                            :show-action="false">
                        </u-search>
                    </view>
                    <view class="fifter">
                        <img src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/xiaoshouhetong/shaixuan.png" alt="" @click="fifterClick" />
                    </view>
                </view>
            </view>
        </view>

        <scroll-view class="main" scroll-y :scroll-with-animation="true"
            @scrolltolower="scrollToLower" refresher-enabled :refresher-triggered="refresherState"
            @refresherrefresh="bindrefresherrefresh" :scroll-top="scrollTop">
            <block>
                <section v-for="(item,index) in list" :key="index">
                    <view class="list-section">
                        <view class="items" style="width:100%'">
                            <view class="item_title" @click="info(item)">
                                <view class="title" >{{ item.demandOrderCode }}</view>
                            </view>
                            <view class="item_content" @click="info(item)">
                                <p class="item">需求方公司名称：<text class="text">{{ item.companyName }}</text></p>
                                <p class="item">交货时间： <text class="text">{{ item.deliveryStartTime }} - {{ item.deliveryEndTime }}</text></p>
                                <p class="item">活畜品种：<text class="text">{{ item.varietiesName }}-{{ item.categoryName }}</text></p>
                                <p class="item">活牛单价：<text class="text">{{ item.unitPrice }} 元/kg</text></p>
                            </view>
                            <view class="list_btn_items">
                                <view class="btn_section">
                                    <view class="btn_item" @click="sale(item)" v-if="$hasPermi('nmb:saleOrder:list')">
                                        <img class="icon" src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/zhoudingdan/dingdan.png" alt="">
                                        <text>销售订单</text>
                                    </view>
                                    <view class="btn_item" @click="sale(item)" v-else-if="$hasPermi('nmb:customer:saleOrder:list')">
                                        <img class="icon" src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/zhoudingdan/dingdan.png" alt="">
                                        <text>采购订单</text>
                                    </view>
                                    <view class="btn_item" @click="purchase(item)" v-if="$hasPermi('nmb:purchaseOrder:list')">
                                        <img class="icon" src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/zhoudingdan/niutou3x.png" alt="">
                                        <text>采购计划</text>
                                    </view>
                                    <view class="btn_item" @click="settlement(item)" v-if="$hasPermi('nmb:financeOrder:colse:list')">
                                        <img class="icon" src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/zhoudingdan/yulebao.png" alt="">
                                        <text>订单结算</text>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </view>
                </section>
            </block>
            <nullList  v-if="isEmpty"/>
            <div v-if="!empty" :style="'height:'+ (isIphonex ? 152 : 118) + 'rpx'"></div>
        </scroll-view>
		<view class="container-footer" :style="'padding:'+(isIphonex ? '40rpx 60rpx;' : '15rpx 60rpx;') +'background-color: #fff;'" v-if="$hasPermi('nmb:demandOrder:add')">
			<u-button hover-class='none' :custom-style="{  
				'background-color': '#40CA8F',
				'color':'white'
			}" shape="circle" @click="addOrder">新增周订单</u-button>
		</view>
        <FilterSearch :pickerFilterShow="pickerFilterShow" @canel="close" @close="close" @submitForm="submitForm" />
    </view>
</template>

<script>

import FilterSearch from "@/components/filterSupervise/index.vue"
import { demandOrderPage } from '@/api/pages/demandOrder'
import CustomNavbar from '@/components/uni-custom-navbar/CustomNavbar.vue'

export default {
    components: {
        FilterSearch,
        CustomNavbar
    },
    name: '',
    data() {
        return {
            refresherState: false,
            noMore: false,
            isEmpty: false,
            scrollTop: 0,
            isIphonex: getApp().globalData.systemInfo.isIphonex,
            pickerFilterShow: false,
            list: [],
            pageNum: 1,
            pageSize: 10,
            searchText: '',
            filters: {
                pageNum: 1,
                pageSize: 10
            },
            saleContractId: '',
            goType: ''
        }
    },
    onLoad(opation) {
        // 接收路由参数
        this.saleContractId = opation.saleContractId
        this.goType = opation.goType
    },
    onShow() {
        this.getList()
    },
    methods: {
        getList(val) { 
            uni.showLoading({
                title: '加载中',
				icon: 'none'
			})
            let params ={}
            if(val) {
                if(this.searchText) {
                    val.searchValue = this.searchText;
                    this.filters.searchValue = this.searchText;
                }else {
                    delete val.searchValue
                    delete this.filters.searchValue
                }
                params =  this.searchText ? val : {...this.filters,...val}
            }
            
            demandOrderPage({
                pageNum: 1,
                pageSize: 10,
                ...params,
                saleContractId: this.saleContractId
            }).then(response=>{
                if(response.code === 200 ) {
                    let pendingApproval =response.result?.list || [];
                    let total = response.result?.total || 0;
                    if(this.pageNum >=2) {
                        this.list = this.list.concat(pendingApproval);
                        this.list.length >= total ? this.noMore = true : this.noMore = false;
                    }else {
                        if(total >=1) {
                            this.isEmpty = false;
                            this.list = pendingApproval;
                            this.list.length >= total ? this.noMore = true : this.noMore = false;
                        }else {
                            this.isEmpty = true
                        }
                        
                    }
                }
            })
            uni.hideLoading()
        },
        // 搜索
        fifterClick() {
            this.searchText = ''
            this.pickerFilterShow = true
		},
        handleSearch() {
            let params = this.getPamams();
            this.pageNum = 1;
            params.pageNum= this.pageNum
            params.searchValue = this.searchText;
            this.filters.pageNum = this.pageNum;
            this.filters.searchValue = this.searchText;
            this.getList(params);
        },
        submitForm(val) {
            this.pageNum = 1
            val.pageNum = this.pageNum;
            val.pageSize= this.pageSize;
            this.filters = val;
            this.list = [];
            this.getList(val);
            this.pickerFilterShow = false;
		},
        close() {
            this.pickerFilterShow = false;
        },
        scrollToLower() {
			if (this.noMore) return;
            this.pageNum++;
            this.isParams();
		},
        bindrefresherrefresh() {
			this.refresherState = true;
			this.pageNum = 1;
			this.noMore = false;
            this.isParams();
			setTimeout(() => {
				this.refresherState = false;
                this.$toast('刷新成功')
			}, 1000);
		},
        getPamams() {
			let params = {
				pageNum: this.pageNum,
				pageSize: this.pageSize
			}
			return params
		},
        isParams() {
            let params = this.getPamams();
            this.getList(params);
        },
        details(item) {
            console.log(item)
        },
        addOrder() {
            if (this.goType == 1) {
                uni.navigateTo({
                    url: '/myPackge1/pages/weeklyOrders/form'
                })
            } else {
                uni.navigateTo({
                    url: '/myPackge1/pages/weeklyOrders/form?saleContractId=' + this.saleContractId
                })
            }
        },
        purchase(item) {
            uni.navigateTo({
                url: '/myPackge1/pages/purchase/index?demandOrderId=' + item.demandOrderId
            })
        },
        sale(item) {
            uni.navigateTo({
                url: '/myPackge1/pages/sale/index?demandOrderId=' + item.demandOrderId
            })
        },
        settlement(item) {
            uni.navigateTo({
                url: '/myPackge1/pages/settlement/index?demandOrderId=' + item.demandOrderId
            })
        },
        info(item) {
            uni.navigateTo({
                url: '/myPackge1/pages/weeklyOrders/detail?demandOrderId=' + item.demandOrderId
            })
        }
    },
}
</script>

<style scoped lang="scss">
@import '@/common/css/listItem.scss';
.header{
    width: 750rpx;
    height: 727rpx;
    display: flex;
    /* 导航栏空间 */
    box-sizing: border-box;
    position: relative;
    background: url(https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/header_bg/zhoudingdan.png) no-repeat 100% 100%;
    background-size: 100% 100%;
    position: relative;
}
.searchs_section{
    margin-top: 349rpx;
}
.main{
    margin-top: -262rpx;
}
</style>