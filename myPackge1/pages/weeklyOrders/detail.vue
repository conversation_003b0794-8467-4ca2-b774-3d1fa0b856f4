<template>
    <view>
        <scroll-view class="main" scroll-y>
            <block>
                <section>
                    <view class="list-section" >
                        <view class="middle" style="width:100%'">
                            <view class="top-explain">
                                <view class="title" >{{ dataInfo.demandOrderCode }}</view>
                            </view>
                            <view class="list-item">
                                <p>需求方公司名称：{{ dataInfo.companyName }}</p>
                                <p>交货时间：{{ dataInfo.deliveryStartTime }} - {{ dataInfo.deliveryEndTime }}</p>
                                <p>活畜品种：{{ dataInfo.varietiesName }}-{{ dataInfo.categoryName }} <span v-if="dataInfo.livestockNum">  x {{ dataInfo.livestockNum }}</span></p>
                                <p>活畜规格：{{ dataInfo.ageRange }}， {{ dataInfo.weightRange }}</p>
                                <p>订单要求：{{ dataInfo.remark }}</p>
                            </view>
                        </view>
                    </view>
                </section>
                <section>
                    <view class="list-section" >
                        <view class="middle" style="width:100%'">
                            <view class="top-explain">
                                <view class="title" >交易价格</view>
                            </view>
                            <view class="list-item">
                                <p style="font-size: 28rpx;">{{ dataInfo.unitPrice }} 元/kg</p>
                            </view>
                        </view>
                    </view>
                </section>
                <section>
                    <view class="list-section" >
                        <view class="middle" style="width:100%'">
                            <view class="top-explain">
                                <view class="title" >收货地址</view>
                            </view>
                            <view class="list-item">
                                <p>{{ dataInfo.customerContactName }} {{ dataInfo.customerContactPhone }}</p>
                                <p>{{ dataInfo.deliveryProvince }} {{ dataInfo.deliveryCity }} {{ dataInfo.deliveryDetailAddress }}</p>
                            </view>
                        </view>
                    </view>
                </section>
            </block>
        </scroll-view>
    </view>
</template>
<script>
import { demandOrderInfo } from '@/api/pages/demandOrder'
export default {
    name: '',
    data() {
        return {
            dataInfo: {},
            demandOrderId: ''
        }
    },
    onLoad(opation) {
        // 接收路由参数
        this.demandOrderId = opation.demandOrderId
        this.getInfo()
    },
    methods: {
        getInfo() { 
            uni.showLoading({
                title: '加载中',
				icon: 'none'
			})
            demandOrderInfo({
                demandOrderId: this.demandOrderId
            }).then(response=>{
                if(response.code === 200 ) {
                    this.dataInfo =response.result || {};
                }
            })
            uni.hideLoading()
        },
    },
}
</script>

<style scoped lang="scss">
@import "@/common/css/superviseHome.scss";
.list-item p{
    white-space: normal !important;
    display: flex;
    justify-content: space-between;
    font-size: 26rpx;
    color: #999;
    line-height: 43rpx;
    span{
        width: 20%;
        text-align: right;
        color: #333;
        font-size: 26rpx;
        font-weight: 400;
    }
}
.title{
    list-style: 50rpx;
    font-weight: 500;
    font-size: 32rpx;
    color: #222222;
    // padding: 20rpx;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0 !important;
    .btn-box{
        display: flex;
        align-items: center;
    }
    text{
        font-weight: 400;
        font-size: 28rpx;
        color: #EA501E;
        margin-right: 20rpx;
    }
}
.list-section{
    flex-direction: column;
}
.tabs{
    display: flex;
    margin: 20rpx;
    background: #F7F8F7;
    border-radius: 10rpx;
    height: 64rpx;
    padding: 6rpx;
    box-sizing: border-box;
    .tabs-item{
        flex: 1;
        text-align: center;
        font-size: 26rpx;
        color: #999999;
        padding: 16rpx 20rpx;
        margin: 0 8rpx;
        border-radius: 20rpx;
        width: 118rpx;
        height: 52rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 500;
        .icon{
            font-size: 24rpx;
            margin-left: 5rpx;
        }
    }
    .current{
        background-color: #1AAF77;
        color: #FFFFFF;
    }
}
</style>