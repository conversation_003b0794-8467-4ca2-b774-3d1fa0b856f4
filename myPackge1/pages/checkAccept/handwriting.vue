<template>
    <view>
        <Handwriting @submit="submit"></Handwriting>
    </view>
</template>

<script>
import Handwriting from '@/components/Handwriting/Handwriting.vue'
import { saveSign } from '@/api/pages/acceptance'
export default {

    data() {
        return {
            purchaseOrderId: '',
            signType: '',
            pageType: ''
        }
    },
    components: {
        Handwriting
    },
    onLoad(opation) {
        this.signType = opation.signType
        this.pageType = opation.pageType
        this.purchaseOrderId = opation.purchaseOrderId
    },
    methods: {
        submit(value) {
            saveSign({
                purchaseOrderId: this.purchaseOrderId,
                signType: this.signType,
                signUrl: value.url
            }).then(res => {
                if (res.code == 200) {
                    if (this.pageType) {
                        uni.navigateBack({
                            delta: 6
                        })
                    } else {
                        uni.navigateBack({
                            delta: 1
                        })
                    }
                }
            })
        }
    }
}
</script>

<style>

</style>