<template>
  <view>
    <scroll-view class="main" scroll-y :scroll-with-animation="true" refresher-enabled>
      <section>
        <view class="list-section">
          <view class="middle" style="width: 100%">
            <view class="top-explain">
              <view class="title">牛源验收</view>
            </view>
            <view class="num-explain explain" v-for="(item,index) in dataInfo.cowOptionList" :key="index">
              <view style="display: flex; justify-content: space-between;">
                <view class="text" @click="selectItem">{{item.title}}：</view>
                <p>{{ item.showValue }}</p>
              </view>
            </view>
          </view>
        </view>
        <view class="list-section">
          <view class="middle" style="width: 100%">
            <view class="top-explain">
              <view class="title">运输验收</view>
            </view>
            <view class="num-explain explain" v-for="(item,index) in dataInfo.transportOptionList" :key="index">
              <view style="display: flex; justify-content: space-between;">
                <view class="text">{{item.title}}：</view>
                <p>{{ item.showValue }}</p>
              </view>
            </view>
          </view>
        </view>
        <view class="list-section">
          <view class="middle" style="width: 100%">
            <view class="top-explain">
              <view class="title">品质验收</view>
            </view>
            <view class="num-explain explain" v-for="(item,index) in dataInfo.qualityOptionList" :key="index">
              <view style="display: flex; justify-content: space-between;">
                <view class="text">{{item.title}}：</view>
                <p>{{ item.showValue }}</p>
              </view>
            </view>
          </view>
        </view>
        <view class="list-section">
          <view class="middle" style="width: 100%">
            <view class="top-explain">
              <view class="title">发票验收</view>
            </view>
            <view class="num-explain explain" v-for="(item,index) in dataInfo.invoiceOptionList" :key="index">
              <view style="display: flex; justify-content: space-between;">
                <view class="text">{{item.title}}：</view>
                <p>{{ item.showValue }}</p>
              </view>
            </view>
          </view>
        </view>
        <view class="list-section">
          <view class="middle" style="width: 100%">
            <view class="top-explain">
              <view class="title">验收结论</view>
            </view>
            <view class="num-explain explain">
              <view style="display: flex; justify-content: space-between;">
                <view class="text">验收结果：</view>
                <p>{{dataInfo.acceptanceCount}}</p>
              </view>
            </view>
          </view>
        </view>
        <view class="list-section">
          <view class="middle" style="width: 100%">
            <view class="top-explain">
              <view class="title">签字确认</view>
            </view>
            <view class="num-explain explain">
              <view style="display: flex; justify-content: space-between;">
                <view class="label">验收员：</view>
                <view class="img-box">
                  <img :src="dataInfo.acceptorSignUrl" alt=""/>
                </view>
              </view>
              <view style="display: flex; justify-content: space-between;" v-if="dataInfo.supervisorSignUrl">
                <view class="label">资方：</view>
                <view class="img-box">
                  <img :src="dataInfo.supervisorSignUrl" alt=""/>
                </view>
              </view>
            </view>
          </view>
        </view>
      </section>
    </scroll-view>
    <view class="sign" @click="addIntent(1)" v-if="!dataInfo.acceptorSignUrl">签字</view>
    <view class="sign" @click="addIntent(2)" v-if="!dataInfo.supervisorSignUrl && dataInfo.acceptorSignUrl">签字</view>
    <VideoPopup :videoUrl="videoUrl" :pickerFilterShow="showVideo" @closeModel="showVideo = false"></VideoPopup>
  </view>
</template>

<script>
import { info } from '@/api/pages/acceptance'
import VideoPopup from '../arrive/videoPopup1.vue'
export default {
  components: {
      VideoPopup
  },
  data() {
    return {
      dataInfo: {},
      videoUrl: '',
      showVideo: false,
    }
  },
    onLoad(opation) {
        this.purchaseOrderId = opation.purchaseOrderId
        this.getInfo()
  },
  methods: {
    addIntent(signType) {
      uni.navigateTo({
          url: `/myPackge1/pages/checkAccept/handwriting?signType=${signType}&purchaseOrderId=${this.purchaseOrderId}`
      })
    },
    getInfo() {
      info({
        purchaseOrderId: this.purchaseOrderId
      }).then(res => {
        console.log(res)
        this.dataInfo = res.result || {}
      })
    },
    selectItem(item){
        if (item.videoType) {
            this.videoUrl = item.videoUrl
            this.showVideo = true
        }
    }
  },
}
</script>

<style lang="scss" scoped>
@import '@/common/css/superviseHome.scss';
.explain {
  font-size: 26rpx !important;
  display: block !important;
  color: #999999;
  width: 100%;
  font-family: PingFang SC-Medium;
  line-height: 45rpx;
  p {
    white-space: nowrap;
    margin-bottom: 25rpx;
    color: #141313;
    padding-right: 30rpx;
  }
  text {
    color: #333;
  }
  .text {
    width: 240rpx;
    p{
      line-height: 30rpx;
      margin-bottom: 0;
    }
  }
  .label{
    width: 160rpx;
  }
.img-box {
    flex: 1;
    img {
      width: 140rpx;
      height: 100rpx;
      border-radius: 20rpx;
      margin-right: 20rpx;
    }
    video {
      width: 140rpx;
      height: 100rpx;
      border-radius: 20rpx;
      margin-right: 20rpx;
    }
  }
}
.item {
  width: 100%;
  display: flex;
  justify-items: center;
  padding: 30rpx 0 0 0;
  text-align: center;
  border-top: 2rpx solid #f2f2f2;
  margin-top: 20rpx;
  .icon {
    width: 50rpx;
    height: 50rpx;
  }

  text {
    font-size: 28rpx;
    font-family: PingFang SC-Medium, PingFang SC;
    font-weight: 400;
    color: #40CA8F;
    padding-left: 10rpx;
    height: 50rpx;
    line-height: 50rpx;
  }

  &:nth-child(4n) {
    width: 17%;
  }
}
.list-section{
  margin-bottom: 20rpx;
}
.sign{
  width: 100rpx;
  height: 100rpx;
  position: fixed;
  right: 49rpx;
  bottom: 199rpx;
  background: #40CA8F;
  border-radius: 50%;
  font-size: 32rpx;
  color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
}

// 为第二个签字按钮添加不同的位置，避免重叠
.sign:last-of-type {
  bottom: 80rpx;
}
