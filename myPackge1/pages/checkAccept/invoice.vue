<template>
	<view>
        <scroll-view scroll-y :scroll-with-animation="true" class="main">
            <view class="container">
                <u-form :model="form" ref="uForm" :error-type="errorType" label-width="auto" label-position="top">
					<u-form-item label="购牛费用发票" required="true" prop="purchaseInvoiceStatus" >
                        <u-radio-group v-model="form.purchaseInvoiceStatus" shape="circle">
                            <u-radio name="1">有</u-radio>
                            <u-radio name="0">无</u-radio>
                        </u-radio-group>
                    </u-form-item>
					<u-form-item label="服务费用发票" required="true" prop="serviceInvoiceStatus" >
                        <u-radio-group v-model="form.serviceInvoiceStatus" shape="circle">
                            <u-radio name="1">有</u-radio>
                            <u-radio name="0">无</u-radio>
                        </u-radio-group>
					</u-form-item>
					<u-form-item label="运输发票费用" required="true" prop="transportInvoiceStatus" >
                        <u-radio-group v-model="form.transportInvoiceStatus" shape="circle">
                            <u-radio name="1">有</u-radio>
                            <u-radio name="0">无</u-radio>
                        </u-radio-group>
					</u-form-item>
                    <u-form-item v-for="(item, index) in invoiceList" :key="index" :label="item.title" required="true" :prop="item.value">
                        <view class="job-type-content">
                            <p v-for="(child, i) in item.children" :key="i"
                                :class="child.active ? 'policy-type-active' : ''"
                                @click="handleActive(index, i)">
                                <span>{{ child.title }}</span>
                                <span>（{{ child.showValue }}）</span>
                            </p>
                        </view>
                    </u-form-item>
                </u-form>
            </view>
            <div :style="'height:'+ (isIphonex ? 152 : 118) + 'rpx'"></div>
        </scroll-view>
		<view class="container-footer" :style="'padding:'+(isIphonex ? '40rpx 60rpx' : '15rpx 60rpx')">
			<u-button hover-class='none' :custom-style="{
				'background-color': '#ccc',
				'color':'white'
			}" shape="circle" @click="upStep">上一步</u-button>
			<u-button hover-class='none' :custom-style="{
				'background-color': '#40CA8F',
				'color':'white'
			}" shape="circle" @click="nextStep">下一步</u-button>
		</view>
	</view>
</template>

<script>
import {
    getStorage, setStorage
} from '@/common/utils/storage.js'
import  { acceptConfig } from '@/api/pages/checkAccept'
	export default {
		data() {
			return {
				form: {
                    purchaseInvoiceStatus: '', //购牛费用发票 1 有 0 无
                    serviceInvoiceStatus: '', //服务费用发票 1 有 0 无
                    transportInvoiceStatus: '', //运输费用发票 1 有 0 无
				},
                isIphonex: getApp().globalData.systemInfo.isIphonex,
                errorType: ['message'],
                rules: {
                    purchaseInvoiceStatus: { required: true, message: '请选择购牛费用发票', trigger: 'blur'},
                    serviceInvoiceStatus: { required: true, message: '请选择服务费用发票', trigger: 'blur'},
                    transportInvoiceStatus: { required: true, message: '请选择运输费用发票', trigger: 'blur'},
                },
                invoiceList: [],
                arrKey: []
			}
		},
        onReady() {
            this.$refs.uForm.setRules(this.rules)
        },
        onLoad(opation) {
            this.purchaseOrderId = opation.purchaseOrderId
            const invoiceJson = getStorage('invoiceStorage' + this.purchaseOrderId)
            invoiceJson ? this.form = invoiceJson : {}
            this.getConfig()
        },
        methods: {
            getConfig() {
                acceptConfig({}).then(res => {
                    this.invoiceList = res.result.invoice?.children
                    this.invoiceList.forEach(item => {
                        const formValue = this.form[item.value]
                        item.children.forEach(i => {
                            if ((formValue || formValue == 0) && formValue === i.value) {
                                i.active = true
                            } else {
                                i.active = false
                            }
                        })
                        if (!formValue && formValue !== 0) {
                            this.form[item.value] = ''
                        }
                        this.rules[item.value] = {
                            required: true,
                            message: `请选择${item.title}`,
                            trigger: 'blur'
                        }
                        this.arrKey.push(item.value)
                    })
                })
            },
            handleActive(index, i) {
                const currentC = [...this.invoiceList[index].children]
                currentC.forEach(item => item.active = false)
                currentC[i].active = true
                this.$set(this.invoiceList[index], 'children', currentC)

                const fieldName = this.invoiceList[index].value
                this.form[fieldName] = currentC[i].value
                this.resetField(fieldName)
            },
            resetField(value) {
                this.$refs.uForm.fields.forEach(e => {
                    if (e.prop == value) {
                        e.resetField()
                    }
                })
            },
            nextStep() {
                const errorArr = []
                Object.keys(this.form).forEach(key => {
                    // 判断字段为空（null/undefined/空字符串）且值不等于 0（严格不等于）
                    if (!this.form[key] && this.form[key] !== 0) {
                        errorArr.push(this.rules[key].message)
                    }
                })
                if (errorArr.length > 0) {
                    uni.showToast({
                        title: errorArr[0],
                        icon: 'none'
                    })
                    return
                }
                const invoiceJson = {}
                this.arrKey.forEach(item => {
                    invoiceJson[item] = this.form[item]
                })
                setStorage('invoiceJson' + this.purchaseOrderId, invoiceJson)
                setStorage('invoiceStorage' + this.purchaseOrderId, this.form)
                uni.navigateTo({
                    url: '/myPackge1/pages/checkAccept/result?purchaseOrderId=' + this.purchaseOrderId
                })
            },
            upStep() {
                uni.navigateBack({
                    delta:1
                })
            }
		}
	}
</script>

<style lang="less" scoped>

	.regulatory-area {
		padding: 29rpx;
		.regulatory-area-title {
			font-size: 28rpx;
			color: #333;
			font-weight: 400;
			padding-bottom: 24rpx;
			font-family: PingFang SC-Bold, PingFang SC;
		}
	}
    .container {
        margin: 30rpx;
        background: #fff;
        box-sizing: border-box;
        padding: 30rpx 32rpx 40rpx;
        border-radius: 30rpx;

		.time-view {
			display: flex;
			justify-content: space-between;
			line-height: 50rpx;

			.start-time {
				width: 50%;
				border-bottom: 1px solid #F4F4F4;
				text-align: center;
				color: #999;
			}

		}
        /deep/ .u-form-item {
            padding: 20rpx 20rpx !important;
        }
        .tips {
            font-size: 26rpx;
            color: #ccc;
        }


        .voucher {
            padding-bottom: 40rpx;
            font-size: 28rpx;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #999999;
        }

        .all-img {
            image {
            width: 154rpx;
            height: 154rpx;
            background: #d8d8d8;
            border-radius: 16rpx;
            margin-right: 20rpx;
            }
        }

        .manyMode {
            background: transparent;
            height: auto;
        }

        .uploadImage {
            display: flex;
            flex-wrap: wrap;
            // justify-content: space-between;
            // align-content: space-between;
            position: relative;

            .itemAlready {
            width: 140rpx;
            height: 140rpx;
            border-radius: 8rpx;
            position: relative;
            margin: 0 20rpx 10rpx 0rpx;

            image {
                width: 100%;
                height: 100%;
                border-radius: 8rpx;
            }

            .closeIcon {
                width: 32rpx;
                height: 32rpx;
                background-image: url('../../../static/modalImg/error.png');
                position: absolute;
                background-size: cover;
                top: -10rpx;
                right: -10rpx;
            }
        }

        .item {
        width: 140rpx;
        height: 140rpx;
        border-radius: 8rpx;
        position: relative;
        border: 2rpx dashed #d8d8d8;

        .uploadIcon {
            width: 100%;
            height: 120rpx;
            display: flex;
            justify-content: center;
            align-items: flex-end;
            font-size: 24rpx;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #d8d8d8;
            background: url('https://fengtaohui.oss-cn-beijing.aliyuncs.com/uploads/image/20230105/6400955ab08a2f639e9790b815c418db63b666a9d7e40.png') no-repeat;
            background-size: 20rpx 20rpx;
            background-position: center 30rpx;
            position: absolute;
            top: 0;
            left: 0;
            bottom: 0;
            right: 0;
            margin: auto;
            z-index: 5;
        }
        }
    }
}

	uni-picker-view {
		display: block;
	}

	uni-picker-view .uni-picker-view-wrapper {
		display: flex;
		align-items: center;
		position: relative;
		overflow: hidden;
		height: 100%;
		background-color: white;
	}

	uni-picker-view[hidden] {
		display: none;
	}

	picker-view {
		width: 100%;
		// height: 600upx;
		height: 600rpx;
		margin-top: 20upx;
	}

	.item {
		line-height: 100upx;
		text-align: center;
	}

	.popup-view {
		.picker-btn {
			display: flex;
			justify-content: space-between;
			padding: 30rpx 40rpx;

			.left {
				color: #999;
				font-size: 28rpx;
				font-family: PingFang SC-Medium;
			}

			.middle {
				font-size: 32rpx;
				color: #222;
				font-family: PingFang SC-Heavy;
			}

			.right {
				color: #40CA8F;
				font-size: 32rpx;
				font-family: PingFang SC-Medium;
			}
		}
	}
    .scroll-view{
        background-color: #fff;
    }
    .submit{
        width: 100%;
        height: 80rpx;
        color: #fff;
        text-align: center;
        line-height: 80rpx;
        font-size: 32rpx;
        padding: 0 20rpx;
        margin-top: 40rpx;
        view{
            width: 100%;
            height: 80rpx;
            border-radius: 40rpx;
            background-color: #40CA8F;
        }
    }

	.container-footer {
		width: 100%;
		position: fixed;
		bottom: 0;
		background-color: #f7f7f7;
		color: white;
		z-index: 100;
        display: flex;
        justify-content: space-between;
        u-button{
            width: 43%;
        }
    }
    
    .job-type-content {
        display: flex;
        flex-wrap: wrap;
        p {
            font-size: 24rpx;
            color: #999;
            background-color: #F4F4F4;
            padding: 0px 25rpx;
            border-radius: 60rpx 60rpx 60rpx 60rpx;
            margin: 15rpx 30rpx 15rpx 0;
            border: 2rpx solid transparent;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            line-height: 38rpx;
        }

        .job-type-active {
            border: 2rpx solid #40CA8F;
            color: #40CA8F;
            background-color: white;
        }

        .policy-type-active {
            border: 2rpx solid #40CA8F;
            color: #40CA8F;
            background-color: white;
        }

        .ear-type-active {
            border: 2rpx solid #40CA8F;
            color: #40CA8F;
            background-color: white;
        }
    }
</style>