<template>
	<view>
        <scroll-view scroll-y :scroll-with-animation="true" class="main">
            <view class="container">
                <u-form :model="form" ref="uForm" :error-type="errorType" label-width="auto" label-position="top">
					<u-form-item label="验收结果" required="true">
						{{ acceptanceResults }}
					</u-form-item>
					<u-form-item label="验收员" >
						{{ dataInfo.acceptorName }}
					</u-form-item>
					<u-form-item label="资方">
						{{ dataInfo.supervisorCompanyName || '' }}
					</u-form-item>
                </u-form>
            </view>
            <div :style="'height:'+ (isIphonex ? 152 : 118) + 'rpx'"></div>
        </scroll-view>
		<view class="container-footer" :style="'padding:'+(isIphonex ? '40rpx 60rpx' : '15rpx 60rpx')">
			<u-button hover-class='none' :custom-style="{
				'background-color': '#ccc',
				'color':'white'
			}" shape="circle" @click="upStep">上一步</u-button>
			<u-button hover-class='none' :custom-style="{
				'background-color': '#40CA8F',
				'color':'white'
			}" shape="circle" @click="sign">确认并签字</u-button>
		</view>
	</view>
</template>

<script>
import {
    getStorage
} from '@/common/utils/storage.js'
import { purchaseOrderInfo1 } from '@/api/pages/purchaseOrder'
import { saveAcceptance } from '@/api/pages/acceptance'
export default {
    data() {
        return {
            dataInfo: {},
            acceptanceResults: '',
            cowSourceJson: {},
            transportJson: {},
            qualityJson: {},
            invoiceJson: {},
        }
    },
    onLoad(opation) {
        this.purchaseOrderId = opation.purchaseOrderId
        this.getInfo()
        this.getRes()
    },
    methods: {
        getInfo() {
            purchaseOrderInfo1({
                purchaseOrderId: this.purchaseOrderId
            }).then(res=> {
                this.dataInfo = res.result || {}
            })
        },
        getRes() {
            this.cowSourceJson = getStorage('cowSourceJson' + this.purchaseOrderId)
            this.transportJson = getStorage('transportJson' + this.purchaseOrderId)
            this.qualityJson = getStorage('qualityJson' + this.purchaseOrderId)
            this.invoiceJson = getStorage('invoiceJson' + this.purchaseOrderId)
            const res = {
                ... this.cowSourceJson,
                ...this.transportJson,
                ...this.qualityJson,
                ...this.invoiceJson,
            }
            let acceptanceResults = 0
            Object.keys(res).forEach(key=> {
                if (res[key]) {
                    console.log(res[key])
                    acceptanceResults += Number(res[key])
                }
            })
            this.acceptanceResults = acceptanceResults > 100 ? 100 : acceptanceResults.toFixed(0)
        },
        upStep() {
            uni.navigateBack({
                delta:1
            })
        },
        sign() {
            const invoiceStorage = getStorage('invoiceStorage' + this.purchaseOrderId)
            const params = {
                purchaseOrderId: this.purchaseOrderId,
                cowSourceJson: JSON.stringify(this.cowSourceJson),
                transportJson: JSON.stringify(this.transportJson),
                qualityJson: JSON.stringify(this.qualityJson),
                invoiceJson: JSON.stringify(this.invoiceJson),
                purchaseInvoiceStatus: invoiceStorage.purchaseInvoiceStatus,
                serviceInvoiceStatus: invoiceStorage.serviceInvoiceStatus,
                transportInvoiceStatus: invoiceStorage.transportInvoiceStatus,
            }
            saveAcceptance(params).then(res=> {
                if (res.code == 200) {
                    uni.navigateTo({
                        url: '/myPackge1/pages/checkAccept/handwriting?signType=1&pageType=1&purchaseOrderId=' + this.purchaseOrderId
                    })
                }
            })
        }
    }
}
</script>

<style lang="less" scoped>

	.regulatory-area {
		padding: 29rpx;
		.regulatory-area-title {
			font-size: 28rpx;
			color: #333;
			font-weight: 400;
			padding-bottom: 24rpx;
			font-family: PingFang SC-Bold, PingFang SC;
		}
	}
    .container {
        margin: 30rpx;
        background: #fff;
        box-sizing: border-box;
        padding: 30rpx 32rpx 40rpx;
        border-radius: 30rpx;

		.time-view {
			display: flex;
			justify-content: space-between;
			line-height: 50rpx;

			.start-time {
				width: 50%;
				border-bottom: 1px solid #F4F4F4;
				text-align: center;
				color: #999;
			}

		}
        /deep/ .u-form-item {
            padding: 20rpx 20rpx !important;
        }
        .tips {
            font-size: 26rpx;
            color: #ccc;
        }


        .voucher {
            padding-bottom: 40rpx;
            font-size: 28rpx;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #999999;
        }

        .all-img {
            image {
            width: 154rpx;
            height: 154rpx;
            background: #d8d8d8;
            border-radius: 16rpx;
            margin-right: 20rpx;
            }
        }

        .manyMode {
            background: transparent;
            height: auto;
        }

        .uploadImage {
            display: flex;
            flex-wrap: wrap;
            // justify-content: space-between;
            // align-content: space-between;
            position: relative;

            .itemAlready {
            width: 140rpx;
            height: 140rpx;
            border-radius: 8rpx;
            position: relative;
            margin: 0 20rpx 10rpx 0rpx;

            image {
                width: 100%;
                height: 100%;
                border-radius: 8rpx;
            }

            .closeIcon {
                width: 32rpx;
                height: 32rpx;
                background-image: url('../../../static/modalImg/error.png');
                position: absolute;
                background-size: cover;
                top: -10rpx;
                right: -10rpx;
            }
        }

        .item {
        width: 140rpx;
        height: 140rpx;
        border-radius: 8rpx;
        position: relative;
        border: 2rpx dashed #d8d8d8;

        .uploadIcon {
            width: 100%;
            height: 120rpx;
            display: flex;
            justify-content: center;
            align-items: flex-end;
            font-size: 24rpx;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #d8d8d8;
            background: url('https://fengtaohui.oss-cn-beijing.aliyuncs.com/uploads/image/20230105/6400955ab08a2f639e9790b815c418db63b666a9d7e40.png') no-repeat;
            background-size: 20rpx 20rpx;
            background-position: center 30rpx;
            position: absolute;
            top: 0;
            left: 0;
            bottom: 0;
            right: 0;
            margin: auto;
            z-index: 5;
        }
        }
    }
}

	uni-picker-view {
		display: block;
	}

	uni-picker-view .uni-picker-view-wrapper {
		display: flex;
		align-items: center;
		position: relative;
		overflow: hidden;
		height: 100%;
		background-color: white;
	}

	uni-picker-view[hidden] {
		display: none;
	}

	picker-view {
		width: 100%;
		// height: 600upx;
		height: 600rpx;
		margin-top: 20upx;
	}

	.item {
		line-height: 100upx;
		text-align: center;
	}

	.popup-view {
		.picker-btn {
			display: flex;
			justify-content: space-between;
			padding: 30rpx 40rpx;

			.left {
				color: #999;
				font-size: 28rpx;
				font-family: PingFang SC-Medium;
			}

			.middle {
				font-size: 32rpx;
				color: #222;
				font-family: PingFang SC-Heavy;
			}

			.right {
				color: #40CA8F;
				font-size: 32rpx;
				font-family: PingFang SC-Medium;
			}
		}
	}
    .scroll-view{
        background-color: #fff;
    }
    .submit{
        width: 100%;
        height: 80rpx;
        color: #fff;
        text-align: center;
        line-height: 80rpx;
        font-size: 32rpx;
        padding: 0 20rpx;
        margin-top: 40rpx;
        view{
            width: 100%;
            height: 80rpx;
            border-radius: 40rpx;
            background-color: #40CA8F;
        }
    }

	.container-footer {
		width: 100%;
		position: fixed;
		bottom: 0;
		background-color: #f7f7f7;
		color: white;
		z-index: 100;
        display: flex;
        justify-content: space-between;
        u-button{
            width: 43%;
        }
    }
    
    .job-type-content {
        display: flex;
        flex-wrap: wrap;
        p {
            font-size: 24rpx;
            color: #999;
            background-color: #F4F4F4;
            padding: 0px 25rpx;
            border-radius: 60rpx 60rpx 60rpx 60rpx;
            margin: 15rpx 30rpx 15rpx 0;
            border: 2rpx solid transparent;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            line-height: 38rpx;
        }

        .job-type-active {
            border: 2rpx solid #40CA8F;
            color: #40CA8F;
            background-color: white;
        }

        .policy-type-active {
            border: 2rpx solid #40CA8F;
            color: #40CA8F;
            background-color: white;
        }

        .ear-type-active {
            border: 2rpx solid #40CA8F;
            color: #40CA8F;
            background-color: white;
        }
    }
</style>