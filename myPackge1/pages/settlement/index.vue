<template>
    <view>
        <scroll-view class="main"  scroll-y :scroll-with-animation="true"
            @scrolltolower="scrollToLower" refresher-enabled :refresher-triggered="refresherState"
            @refresherrefresh="bindrefresherrefresh" :scroll-top="scrollTop">
            <template v-if="dataList && dataList.length > 0">
                <section v-for="(item, index) in dataList" :key="index">
                    <view class="list-section" >
                        <view class="middle" style="width:100%'">
                            <view class="top-explain" @click="detail(item)">
                                <view class="title" >{{ item.purchaseOrderCode }}</view>
                            </view>
                            <view class="list-item" @click="detail(item)">
                                <p v-if="item.settlementStatus == 60">采购活牛支出：<span>{{item.purchaseAmount }}元</span></p>
                                <p v-if="item.settlementStatus == 60">运输支出：<span>{{item.transportAmount }}元</span></p>
                                <p v-if="item.settlementStatus == 60">服务费支出：<span>{{item.serviceAmount }}元</span></p>
                                <p v-if="item.settlementStatus == 60">其他支出：<span>{{item.otherAmount }}元</span></p>
                                <p v-if="item.settlementStatus == 60">销售活牛收入：<span>{{item.finalAmount }}元</span></p>
                                <p v-if="item.settlementStatus == 60">结算：<span>{{item.profit }}元</span></p>
                                <p v-if="item.settlementStatus == 59">收入：<span>---</span></p>
                                <p v-if="item.settlementStatus == 59">支出：<span>---</span></p>
                                <p v-if="item.settlementStatus == 59">结算：<span>---</span></p>
                            </view>
                            <view class="list-item-btn" v-if="item.settlementStatus == 59 && $hasPermi('nmb:financeOrder:close:add')">
                                <view class="item" @click="settlementFn(item)">
                                    <img class="icon" src="../../icon/consumption.png" alt="">
                                    <text>待结算</text>
                                </view>
                            </view>
                        </view>
                    </view>
                </section>
				
				
				
            </template>
			<template v-else>
				<u-empty text="暂无周订单结算数据" mode="list" :icon-size='150'></u-empty>
			</template>
        </scroll-view>
        <u-modal v-model="showRepayment" content="结算后不能再列支其他费用，请确认是否结算" show-cancel-button @confirm="handleRepayment" @cancel="showRepayment = false"></u-modal>
    </view>
</template>

<script>
import { settlementOrderPage, settlement, isSettlement } from '@/api/pages/settlementOrder'
export default {
    name: '',
    data() {
        return {
            refresherState: false,
            noMore: false,
            scrollTop: 0,
            pageNum: 1,
            pageSize: 10,
            filters: {
                pageNum: 1,
                pageSize: 10
            },
            dataList: [],
            purchaseOrderId: '',
            showRepayment: false
        }
    },
    onLoad(opation) {
        // 接收路由参数
        this.demandOrderId = opation.demandOrderId
        this.getList()
    },
    methods: {
        getList() { 
            let params =  {...this.filters }
            settlementOrderPage({
                pageNum: 1,
                pageSize: 10,
                ...params,
                demandOrderId: this.demandOrderId
            }).then(response=>{
                if(response.code === 200 ) {
                    let list =response.result?.list || [];
                    let total = response.result?.total || 0;
                    if(this.pageNum >=2) {
                        this.dataList = this.dataList.concat(list);
                        this.dataList.length >= total ? this.noMore = true : this.noMore = false;
                    }else {
                        if(total >=1) {
                            this.isEmpty = false;
                            this.dataList = list;
                            this.dataList.length >= total ? this.noMore = true : this.noMore = false;
                        }else {
                            this.isEmpty = true
                        }
                        
                    }
                }
            })
            uni.hideLoading()
        },
        scrollToLower() {
			if (this.noMore) return;
            this.pageNum++;
            this.isParams();
		},
        bindrefresherrefresh() {
			this.refresherState = true;
			this.pageNum = 1;
			this.noMore = false;
            if(this.tabCurrent==0) {
                this.getNumber();
                this.isViewRecor();
            }
            this.isParams();
			setTimeout(() => {
				this.refresherState = false;
                this.$toast('刷新成功')
			}, 1000);
		},
        getPamams() {
			let params = {
				pageNum: this.pageNum,
				pageSize: this.pageSize
			}
			return params
		},
        isParams() {
            let params = this.getPamams();
            this.getList(params);
        },
        detail(item) {
            uni.navigateTo({
                url: '/myPackge1/pages/settlement/detail?purchaseOrderId=' + item.purchaseOrderId
            })
        },
        settlementFn(item) {
            isSettlement({
                purchaseOrderId: item.purchaseOrderId
            }).then(res => {
                if (res.code == 200) {
                    this.showRepayment = true
                    this.purchaseOrderId = item.purchaseOrderId
                }
            })
        },
        handleRepayment() {
            settlement({
                purchaseOrderId: this.purchaseOrderId
            }).then(res => {
                if (res.code == 200) {
                    this.showRepayment = false
                    this.getList()
                }
            })
        }
    },
}
</script>

<style scoped lang="scss">
@import "@/common/css/superviseHome.scss";
.list-item-btn {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-top: 20rpx;
    .item{
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 28rpx;
        font-family: PingFang SC-Medium, PingFang SC;
        font-weight: 400;
        color: #40CA8F;
        padding-left: 10rpx;
        line-height: 50rpx;
        margin-left: 30rpx;
        img {
            width: 45rpx;
            height: 45rpx;
            margin-right: 10rpx;
        }
    }
}
</style>