<template>
    <view style="padding-bottom: 40rpx;">
        <scroll-view class="main" scroll-y :scroll-with-animation="true" refresher-enabled scroll-top="scrollTop">
            <block>
                <section v-for="(item, index) in dataInfoList" :key="index">
                    <view class="list-section" >
                        <view class="middle" style="width:100%';">
                            <view class="list-item">
                                <p>资金类型：<span>{{ settlementType[item.settlementType] }}</span></p>
                                <p v-if="item.settlementType == 1">收款人：<span>{{ item.farmersName }}</span></p>
                                <p v-if="item.settlementType == 2">收款人：<span>{{ item.driverName }}（{{item.driverPhone }}）</span></p>
                                <p v-if="item.settlementType == 3">收款人：<span>{{ item.brokerName }}</span></p>
                                <p v-if="item.settlementType == 4">收款人：<span>{{ item.remark }}</span></p>
                                <p v-if="item.settlementType == 5">收款人：<span>{{ item.customerCompanyName }}</span></p>
                                <p>付款时间：<span>{{ item.settlementTime }}</span></p>
                                <p>金额：<span>{{ item.finalAmount }}元</span></p>
                            </view>
                        </view>
                    </view>
                </section>
            </block>
        </scroll-view>
    </view>
</template>

<script>

import { settlementOrderInfo } from '@/api/pages/settlementOrder'
export default {
    name: '',
    data() {
        return {
            scrollTop: 0,
            dataInfoList: [],
            settlementType: {
                1: '购牛款',
                2: '运输费',
                3: '服务费',
                4: '其它费用',
                5: '售牛款'
            }
        }
    },
    onLoad(opation) {
        // 接收路由参数
        this.purchaseOrderId = opation.purchaseOrderId
        this.getInfo()
    },
    methods: {
        getInfo(val) { 
            uni.showLoading({
                title: '加载中',
				icon: 'none'
			})
            settlementOrderInfo({
                purchaseOrderId: this.purchaseOrderId
            }).then(response=>{
                if(response.code === 200 ) {
                    this.dataInfoList =response.result;
                }
            })
            uni.hideLoading()
        },
    },
}
</script>

<style scoped lang="scss">
@import "@/common/css/superviseHome.scss";
.list-item p{
    white-space: normal !important;
    display: flex;
    justify-content: space-between;
    font-size: 26rpx;
    color: #999;
    line-height: 43rpx;
    span{
        width: 500rpx;
        text-align: right;
        color: #333;
        font-size: 26rpx;
        font-weight: 400;
    }
}
.title{
    list-style: 50rpx;
    font-weight: 500;
    font-size: 32rpx;
    color: #222222;
    padding: 20rpx;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .btn-box{
        display: flex;
        align-items: center;
    }
    text{
        font-weight: 400;
        font-size: 28rpx;
        color: #EA501E;
        margin-right: 20rpx;
    }
}
.list-section{
    flex-direction: column;
}
.tabs{
    display: flex;
    margin: 20rpx;
    background: #F7F8F7;
    border-radius: 10rpx;
    height: 64rpx;
    padding: 6rpx;
    box-sizing: border-box;
    .tabs-item{
        flex: 1;
        text-align: center;
        font-size: 26rpx;
        color: #999999;
        padding: 16rpx 20rpx;
        margin: 0 8rpx;
        border-radius: 20rpx;
        width: 118rpx;
        height: 52rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 500;
        .icon{
            font-size: 24rpx;
            margin-left: 5rpx;
        }
    }
    .current{
        background-color: #1AAF77;
        color: #FFFFFF;
    }
}
</style>