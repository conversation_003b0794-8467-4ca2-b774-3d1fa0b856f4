<template>
    <view>
		<CustomNavbar  :bgColor="'#08BA7E'" :titleColor="'#FFFFFF'" />
        <view class="header">
            <view class="fifter">
                <img src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/xiaoshouhetong/shaixuan.png"
                    alt="" @click="fifterClick" />
            </view>
        </view>
        <!--  统计 -->
        <view class="stats-container">
            <view class="stats-item">
                <view class="stats-icon">
                    <image src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/caiwu/dingdan.png" mode="aspectFit" />
                    <view class="stats-badge" v-if="paramsData.financialOrderNumber > 0">{{ paramsData.financialOrderNumber || 0 }}</view>
                </view>
                <view class="stats-label">订单个数合计</view>
            </view>
            <view class="stats-item">
                <view class="stats-icon">
                    <image src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/caiwu/daifukuan.png" mode="aspectFit" />
                    <view class="stats-badge" v-if="paramsData.pendingPaymentOrderNumber > 0">{{ paramsData.pendingPaymentOrderNumber || 0 }}</view>
                </view>
                <view class="stats-label">待付款订单</view>
            </view>
            <view class="stats-item">
                <view class="stats-icon">
                    <image src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/caiwu/daishoukuan.png" mode="aspectFit" />
                    <view class="stats-badge" v-if="paramsData.pendingReceiptOrderNumber > 0">{{ paramsData.pendingReceiptOrderNumber || 0 }}</view>
                </view>
                <view class="stats-label">待收款订单</view>
            </view>
        </view>
        <scroll-view class="main" scroll-y :scroll-with-animation="true" @scrolltolower="scrollToLower"
            refresher-enabled :refresher-triggered="refresherState" @refresherrefresh="bindrefresherrefresh"
            :scroll-top="scrollTop">
            <template v-if="list && list.length > 0">
                <section v-for="(item,index) in list" :key="index" @click="details(item)">
                    <view class="list-section">
                        <view class="items">
                            <view class="item_title">
                                <view class="title">{{ item.purchaseOrderCode }}</view>
                                <text class="status">{{ item.financialOrderStatus ? '已处理' : '待处理' }}</text>
                            </view>
                            <view class="item_content">
                                <p class="item">客户公司：<text class="text">{{ item.customerName }}</text></p>
                                <p class="item">项目经理：<text class="text">{{ item.projectManagerName }}</text></p>
                                <p class="item">牛经纪：<text class="text">{{ item.brokerName }}</text></p>
                                <p class="item">运输信息：<text class="text">{{ item.driverName || '--' }} {{ item.driverName ? item.licensePlateNumber || '' : '' }}</text></p>
                                <p class="item">运输方向：<text class="text">{{ item.startEndPlace }}</text></p>
                                <p class="item">发车日期：<text class="text">{{ item.goTime || '--' }}</text></p>
                                <p class="item">抵达日期：<text class="text">{{ item.arriveTime || '--' }}</text></p>
                                <p class="item">采购状态：<text class="text">{{ item.showPurchaseOrderStatus || '--' }}</text></p>
                                <p class="item">销售状态：<text class="text">{{ item.showSaleOrderStatus || '--' }}</text></p>
                            </view>
                            <view class="list_btn_items">
                                <view class="btn_section">
                                    <view class="btn_item" @click.stop="repayment(item)"
                                        v-if="$hasPermi('nmb:financeOrder:costFee:list') && item.financialOrderStatus != 1">
                                        <img class="icon" src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/caiwu/feiyong.png" alt="">
                                        <text>费用管理</text>
                                    </view>
                                    <view class="btn_item" @click.stop="confirmFn(item)"
                                        v-if="$hasPermi('nmb:financeOrder:receive') && item.saleOrderStatus == 50 && item.financialOrderStatus == 0">
                                        <img class="icon" src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/caiwu/feiyong.png" alt="">
                                        <text>确认收款</text>
                                    </view>
                                    <view class="btn_item" @click.stop="settlement(item)"
                                        v-if="$hasPermi('nmb:financeOrder:colse:list') && item.saleOrderStatus == 59 && item.financialOrderStatus == 0">
                                        <img class="icon" src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/caiwu/feiyong.png" alt="">
                                        <text>发起结算</text>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </view>
                </section>
            </template>
            <template v-else>
                <u-empty text="暂无数据" mode="data" :icon-size='150'></u-empty>
            </template>
            <div v-if="!isEmpty" :style="'height:'+ (isIphonex ? 152 : 118) + 'rpx'"></div>
        </scroll-view>
        <FilterList :pickerFilterShow="pickerFilterShow" @canel="close" @close="close" @submitForm="submitForm" />
        <u-modal v-model="showConfirm" content="确认后无法撤回，请确认是否收到款项" show-cancel-button @confirm="handleSubmit"
            @cancel="showConfirm = false" confirmColor="#40CA8F"></u-modal>
    </view>
</template>

<script>

import FilterList from "./filterList/index.vue"
import { financialOrderPage } from '@/api/pages/finance'
import { receipt } from "@/api/pages/sale.js" 
import CustomNavbar from '@/components/uni-custom-navbar/CustomNavbar.vue'

export default {
    components: {
        FilterList,
        CustomNavbar
    },
    name: '',
    data() {
        return {
            refresherState: false,
            noMore: false,
            isEmpty: false,
            scrollTop: 0,
            isIphonex: getApp().globalData.systemInfo.isIphonex,
            pickerFilterShow: false,
            list: [],
            pageNum: 1,
            pageSize: 10,
            searchText: '',
            filters: {
                pageNum: 1,
                pageSize: 10
            },
            demandOrderId: '',
            paramsData: {},
            showConfirm: false,
            goType: '',
            currentItem: {}
        }
    },
    onLoad(opation) {
        // 接收路由参数
        this.demandOrderId = opation.demandOrderId
        this.goType = opation.goType
    },
    onShow() {
        this.getList()
    },
    methods: {
        getList(val) { 
            uni.showLoading({
                title: '加载中',
				icon: 'none'
			})
            let params ={}
            if(val) {
                if(this.searchText) {
                    val.searchValue = this.searchText;
                    this.filters.searchValue = this.searchText;
                }else {
                    delete val.searchValue
                    delete this.filters.searchValue
                }
                params =  this.searchText ? val : {...this.filters,...val}
            }
            financialOrderPage({
                pageNum: 1,
                pageSize: 10,
                ...params,
                demandOrderId: this.demandOrderId
            }).then(response=>{
                if(response.code === 200 ) {
                    let list =response.result?.list || [];
                    let total = response.result?.total || 0;
                    this.paramsData = response.result.params
                    if(this.pageNum >=2) {
                        this.list = this.list.concat(list);
                        this.list.length >= total ? this.noMore = true : this.noMore = false;
                    }else {
                        if(total >=1) {
                            this.isEmpty = false;
                            this.list = list;
                            this.list.length >= total ? this.noMore = true : this.noMore = false;
                        }else {
                            this.isEmpty = true
                        }
                    }
                }
            })
            uni.hideLoading()
        },
        // 搜索
        fifterClick() {
            this.searchText = ''
            this.pickerFilterShow = true
		},
        handleSearch() {
            let params = this.getPamams();
            this.pageNum = 1;
            params.pageNum= this.pageNum
            params.searchValue = this.searchText;
            this.filters.pageNum = this.pageNum;
            this.filters.searchValue = this.searchText;
            this.getList(params);
        },
        submitForm(val) {
            this.pageNum = 1
            val.pageNum = this.pageNum;
            val.pageSize= this.pageSize;
            this.filters = val;
            this.list = [];
            this.getList(val);
            this.pickerFilterShow = false;
		},
        close() {
            this.pickerFilterShow = false;
        },
        scrollToLower() {
			if (this.noMore) return;
            this.pageNum++;
            this.isParams();
		},
        bindrefresherrefresh() {
			this.refresherState = true;
			this.pageNum = 1;
			this.noMore = false;
            this.isParams();
			setTimeout(() => {
				this.refresherState = false;
                this.$toast('刷新成功')
			}, 1000);
		},
        getPamams() {
			let params = {
				pageNum: this.pageNum,
				pageSize: this.pageSize
			}
			return params
		},
        isParams() {
            let params = this.getPamams();
            this.getList(params);
        },
        details(item) {
            uni.navigateTo({
                url: '/myPackge1/pages/finance/detail?purchaseOrderId='+ item.purchaseOrderId
            })
        },
        repayment(item) {
            uni.navigateTo({
                url: '/myPackge1/pages/payment/index?purchaseOrderId=' + item.purchaseOrderId
            })
        },
        settlement(item) {
            uni.navigateTo({
                url: '/myPackge1/pages/settlement/index?demandOrderId=' + item.demandOrderId
            })
        },
        confirmFn(item) {
            this.showConfirm = true
            this.currentItem = item
        },
        handleSubmit() {
            receipt({
                purchaseOrderId: this.currentItem.purchaseOrderId
            }).then(res => {
                if (res.code == 200) {
                    this.showRepayment = false
                    this.getList()
                }
            })
        },
    },
}
</script>

<style scoped lang="scss">
@import "@/common/css/superviseHome.scss";
@import '@/common/css/listItem.scss';

.header {
    width: 750rpx;
    height: 727rpx;
    display: flex;
    padding-top: 120rpx;
    /* 导航栏空间 */
    box-sizing: border-box;
    position: relative;
    background: url(https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/header_bg/caiwu.png) no-repeat 100% 100%;
    background-size: 100% 100%;
    position: relative;
}

.fifter {
    position: absolute;
    top: 195rpx;
    right: 30rpx;

    img {
        width: 34rpx;
        height: 32.5rpx;
    }
}
.items{
    padding: 0 !important;
    margin-bottom: 0 !important;
    width: 100%;
}
.list-section{
    margin: 0 30rpx !important;
}
._section{
    padding: 0 !important;
    margin-bottom: 30rpx;
}

// 按钮样式调整
.btn_item {
    margin-right: 30rpx;

    &:last-child {
        margin-right: 0;
    }

    img {
        width: 28rpx !important;
        height: 28rpx !important;
    }
}
	.container-footer {
		width: 100%;
		position: fixed;
		bottom: 0;
		background-color: white;
		color: white;
	}
// 统计组件样式
.stats-container {
    display: flex;
    justify-content: space-around;
    align-items: center;
    background: #FFFFFF;
    border-radius: 20rpx;
    padding: 40rpx 20rpx;
    margin: 30rpx;
    box-shadow: 0rpx 4rpx 20rpx 0rpx rgba(0, 0, 0, 0.05);
    margin-top: -370rpx;
    z-index: 99;
}

.stats-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
}

.stats-icon {
    position: relative;
    margin-bottom: 20rpx;

    image {
        width: 60rpx;
        height: 60rpx;
    }

    .stats-badge {
        position: absolute;
        top: -15rpx;
        right: -35rpx;
        background: linear-gradient( 270deg, #6DD570 0%, #1CC370 100%);
        color: #FFFFFF;
        font-size: 24rpx;
        font-weight: 500;
        padding: 4rpx 8rpx;
        border-radius: 20rpx;
        width: 56rpx;
        height: 36rpx;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}

.stats-label {
    font-size: 24rpx;
    color: #333;
    text-align: center;
    line-height: 1.2;
    font-weight: 400;
}
</style>