<template>
	<view>
		<scroll-view class="main" scroll-y :scroll-with-animation="true">
			<view class="container">
				<u-form :model="form" ref="uForm" :error-type="errorType" label-width="auto" label-position="top" :label-style="labelStyle">
					<u-form-item label="费用类型">
						<text>{{ settlementHash[settlementType] }}</text>
					</u-form-item>
					<u-form-item label="应付金额" required="true" prop="finalAmount">
						<u-input :custom-style="customStyle" :placeholder-style="placeholderStyle" v-model="form.finalAmount" type="number" :disabled="settlementType == 1 ? true : false" placeholder="请输入应付金额" @input="setFinalAmount" />
					</u-form-item>
					<u-form-item label="扣款金额">
						<u-input :custom-style="customStyle" :placeholder-style="placeholderStyle" v-model="form.deductAmount" type="number" placeholder="请输入扣款金额" @input="setFinalAmount"  />
					</u-form-item>
					<u-form-item label="实付金额">
						<u-input :custom-style="customStyle" :placeholder-style="placeholderStyle" v-model="finalAmount" disabled placeholder="请输入实付金额" />
					</u-form-item>
					<u-form-item label="备注" prop="remark">
						<u-input :custom-style="customStyle" :placeholder-style="placeholderStyle" v-model="form.remark" placeholder="请输入备注" />
					</u-form-item>
					<u-form-item label="账户名称" prop="payeeAccountName">
						<u-input :custom-style="customStyle" :placeholder-style="placeholderStyle" v-model="form.payeeAccountName" placeholder="请输入账户名称" />
					</u-form-item>
					<u-form-item label="收款银行" prop="payeeBankName">
						<u-input :custom-style="customStyle" :placeholder-style="placeholderStyle" v-model="form.payeeBankName" placeholder="请输入收款银行" />
					</u-form-item>
					<u-form-item label="收款账号" prop="payeeBankNo">
						<u-input :custom-style="customStyle" :placeholder-style="placeholderStyle" v-model="form.payeeBankNo" placeholder="请输入收款账号" />
					</u-form-item>
					<u-form-item prop="contractUrl" label="发票等相关凭证"  :required="true">
						<u-input placeholder=" " v-model="contractUrlName" disabled  @click="previewFiles(form.invoiceUrl)"/>
						<img slot="right" style="width: 40rpx; height: 40rpx" @click="uploadPdf()" src="../../../myPackge2/icon/pdf.png" alt="" />
					</u-form-item>
					<!-- <view class="uploadImage" v-if="form.invoiceUrl">
						<view class="itemAlready">
							<image mode="scaleToFill" :src="form.invoiceUrl" />
						</view>
					</view> -->
					<!-- <u-form-item label="发票等相关凭证" required="true" prop="invoiceUrl" :border-bottom="false" style="overflow: visible">
						<view class=" :custom-style="customStyle" :placeholder-style="placeholderStyle"uploadImage">
							<view class="itemAlready" v-if="certificate">
								<image mode="scaleToFill" :src="form.invoiceUrl" @click="previewImage(form.invoiceUrl)" />
								<view class="closeIcon" @click="deleteImage(index)"></view>
							</view>
							<view class="item" v-if="!certificate">
								<view class="uploadIcon" @click="unloadImage()">点击上传</view>
							</view>
						</view>
					</u-form-item> -->
				</u-form>
			</view>
			<div :style="'height:' + (isIphonex ? 172 : 148) + 'rpx'"></div>
		</scroll-view>
		<view class="add-btn" @click="addPayment">提交</view>
	</view>
</template>
<script>
import { uploadFiles } from '@/api/obsUpload/index'
import { purchaseOrder, saveSettlement, saveOtherSettlement } from '@/api/pages/settlement'
export default {
data() {
	return {
		customStyle: { fontSize: '26rpx' },
		labelStyle: { color: '#333', fontSize: '26rpx' },
		placeholderStyle: 'color:#999;font-size: 26rpx;',
		isIphonex: getApp().globalData.systemInfo.isIphonex,
		form: {
			settlementId: '',
			finalAmount: '',
			payeeAccountName: '',
			payeeBankName: '',
			payeeBankNo: '',
			invoiceUrl: '',
			remark: ''
		},
		certificate: false,
		errorType: ['message'],
		rules: {
			finalAmount: [
				{
					required: true,
					message: '请输入应付金额',
					trigger: 'blur',
				},
			],
			invoiceUrl: [
				{
					required: true,
					message: '请上传发票等相关凭证',
					trigger: ['change', 'blur'],
				},
			],
		},
		settlementType: '',
		purchaseOrderId: '',
		settlementId: '',
		settlementHash: {
			1: '采购货款',
			2: '运输费',
			3: '服务费',
			4: '其他费用'
		},
		finalAmount: '',
		fileType: ["pdf"],
		contractUrlName: ''
	}
},
onLoad(opation) {
	this.settlementType = opation.settlementType
	this.purchaseOrderId = opation.purchaseOrderId
	this.settlementId = opation.settlementId
	this.form.finalAmount = opation.finalAmount
},
onReady() {
	this.$refs.uForm.setRules(this.rules)
},
methods: {
	deleteImage() {
		this.form.contentImages = ''
		this.certificate = false
		this.$forceUpdate()
	},
	unloadImage() {
		const that = this
		uni.chooseImage({
			count: 1, //默认9
			sizeType: ['original', 'compressed'], //可以指定是原图还是压缩图，默认二者都有
			sourceType: ['album', 'camera'], //从相册选择
			name: 'file',
			success: function (res) {
				uploadFiles({
					filePath: res.tempFilePaths[0],
				}).then((data) => {
					that.form.invoiceUrl = data
					that.certificate = true
					that.form.invoiceUrl != '' ? that.resetField('invoiceUrl') : ''
				})
			},
			fail(e) {},
		})
	},
	resetField(value) {
		this.$refs.uForm.fields.forEach((e) => {
			if (e.prop == value) {
			e.resetField()
			}
		})
	},
	previewImage(url) {
		uni.previewImage({
			urls: [url],
		})
	},
    // 上传pdf
    uploadPdf(type, maxCount){
		let that = this; 
		uni.chooseMessageFile({
			type: 'file',
			count: 1,
			success: function (res) {
			let resFile = res.tempFiles[0]
			console.log(resFile)
			if(resFile.type=='file') {
				let index = resFile.name.lastIndexOf('.');
						let name = resFile.name.substring(index + 1);
				console.log('name', name)
				if(!that.fileType.includes(name)){
				uni.showToast({
					icon: 'none',
					title: '请上传pdf文件',
				})
				return
				} else {
				uploadFiles({
					filePath: resFile.path,
					name: resFile.name,
					}).then((data) => {
						console.log(data)
						that.form.invoiceUrl = data
						that.contractUrlName = resFile.name
					})
				}
			} else {
				uni.showToast({
					icon: 'none',
					title: '请上传pdf文件',
				})
			}
			},
		})
    },
	previewFiles(url){
        wx.downloadFile({
			url: url,
			success: function(res) {
				const filePath = res.tempFilePath
				wx.openDocument({
					filePath: filePath,
					fileType: 'pdf',
					success: function(res) {},
				})
			},
		})
	},
	setFinalAmount() {
		if (this.form.deductAmount > this.form.finalAmount) {
			uni.showToast({
				title: '扣款金额不能大于应付金额',
				icon: 'none'
			})
			this.form.deductAmount = ''
			return
		}
		if (!this.form.deductAmount || !this.form.finalAmount) {
			this.finalAmount = ''
			return
		}
		this.finalAmount = (this.form.finalAmount - this.form.deductAmount).toFixed(2)
	},
	resetField(value) {
		this.$refs.uForm.fields.forEach(e => {
			if (e.prop == value) {
				e.resetField()
			}
		})
	},
	addPayment() {
		this.$refs.uForm.validate((valid) => {
			if (valid) {
				switch(this.settlementType) {
					case '1':
						this.purchaseOrderFn()
					break;
					case '2':
					case '3':
						this.saveSettlementFn()
					break;
					case '4':
						this.saveOtherSettlementFn()
					break;
				}
			}
		})
	},
	purchaseOrderFn() {
		purchaseOrder({
			...this.form,
			settlementId: this.settlementId,
			purchaseOrderId: this.purchaseOrderId
		}).then(res => {
			if (res.code == 200) {
				uni.showToast({
					title: '操作成功',
					icon: 'none'
				})
				uni.navigateBack({
					delta:1
				})
			}
		})
	},
	saveSettlementFn() {
		saveSettlement({
			...this.form,
			settlementId: this.settlementId
		}).then(res => {
			if (res.code == 200) {
				uni.showToast({
					title: '支付成功',
					icon: 'none'
				})
				uni.navigateBack({
					delta:1
				})
			}
		})
	},
	saveOtherSettlementFn() {
		console.log(this.settlementType)
		saveOtherSettlement({
			...this.form,
			purchaseOrderId: this.purchaseOrderId
		}).then(res => {
			if (res.code == 200) {
				uni.showToast({
					title: '支付成功',
					icon: 'none'
				})
				uni.navigateBack({
					delta:1
				})
			}
		})
	},
},
}
</script>

<style lang="less" scoped>
.container {
	margin: 30rpx;
	background: #fff;
	box-sizing: border-box;
	padding: 30rpx 32rpx 40rpx;
	border-radius: 30rpx;

	/deep/ .u-form-item {
		padding: 20rpx 20rpx !important;
	}

	.tips {
		font-size: 28rpx;
		color: #c0c3ca;
	}

	.voucher {
		padding-bottom: 40rpx;
		font-size: 28rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #999999;
	}

	.all-img {
		image {
		width: 154rpx;
		height: 154rpx;
		background: #d8d8d8;
		border-radius: 16rpx;
		margin-right: 20rpx;
		}
	}

	.manyMode {
		background: transparent;
		height: auto;
	}

	.uploadImage {
		display: flex;
		flex-wrap: wrap;
		// justify-content: space-between;
		// align-content: space-between;
		position: relative;

		.itemAlready {
		width: 140rpx;
		height: 140rpx;
		border-radius: 8rpx;
		position: relative;
		margin: 0 20rpx 10rpx 0rpx;

		image {
			width: 100%;
			height: 100%;
			border-radius: 8rpx;
		}

		.closeIcon {
			width: 32rpx;
			height: 32rpx;
			background-image: url('../../../static/modalImg/error.png');
			position: absolute;
			background-size: cover;
			top: -10rpx;
			right: -10rpx;
		}
	}

	.item {
	width: 140rpx;
	height: 140rpx;
	border-radius: 8rpx;
	position: relative;
	border: 2rpx dashed #d8d8d8;

	.uploadIcon {
		width: 100%;
		height: 120rpx;
		display: flex;
		justify-content: center;
		align-items: flex-end;
		font-size: 24rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #d8d8d8;
		background: url('https://fengtaohui.oss-cn-beijing.aliyuncs.com/uploads/image/20230105/6400955ab08a2f639e9790b815c418db63b666a9d7e40.png') no-repeat;
		background-size: 20rpx 20rpx;
		background-position: center 30rpx;
		position: absolute;
		top: 0;
		left: 0;
		bottom: 0;
		right: 0;
		margin: auto;
		z-index: 5;
	}
	}
}
}

.add-btn {
	position: fixed;
	z-index: 9;
	left: 60rpx;
	right: 60rpx;
	bottom: 40rpx;
	height: 80rpx;
	font-size: 32rpx;
	font-weight: 500;
	line-height: 80rpx;
	text-align: center;
	border-radius: 40rpx;
	color: #fff;
	font-family: PingFangSC-Regular, PingFang SC;
	background: #40CA8F;
}

.uploadImage{
	display: flex !important;
	flex-wrap: wrap !important;
}
.pdf-box{
	width: auto;
	height: auto;
	color: #37BA7E;
	margin: 20rpx 20rpx 10rpx 0rpx;
	padding-right: 30rpx;
	box-sizing: border-box;
}
</style>