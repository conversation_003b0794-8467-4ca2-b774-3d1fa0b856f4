<template>
    <view>
        <scroll-view class="main" scroll-y :scroll-with-animation="true" refresher-enabled scroll-top="scrollTop">
            <template v-if="dataList && dataList.length > 0">
                <section v-for="(item, index) in dataList" :key="index">
                    <view class="list-section" >
                        <view class="items" style="width:100%'">
                            <view class="item_content" @click="details(item)">
                                <p class="item" v-if="(item.settlementStatus == 2 ||item.settlementStatus == 0) && item.settlementType == 1">
                                    <span>养殖户：<text class="text">{{ item.farmersName }}</text></span>
                                    <span class="status">{{ statusHash[item.settlementStatus] }}</span>
                                </p>
                                <p class="item" v-if="(item.settlementStatus == 2 ||item.settlementStatus == 0) && item.settlementType == 2">
                                    <span>运输方：<text class="text">{{ item.driverName }} （{{ item.driverPhone }} ）</text></span>
                                    <span class="status">{{ statusHash[item.settlementStatus] }}</span>
                                </p>
                                <p class="item" v-if="(item.settlementStatus == 2 ||item.settlementStatus == 0) && item.settlementType == 3">
                                    <span>牛经纪：<text class="text">{{ item.brokerName }}</text></span>
                                    <span class="status">{{ statusHash[item.settlementStatus] }}</span>
                                </p>
                                <p class="item" v-if="(item.settlementStatus == 2 ||item.settlementStatus == 0) && item.settlementType == 4">
                                    <span>其他费用</span>
                                    <span class="status">{{ statusHash[item.settlementStatus] }}</span>
                                </p>
                                <p class="item" v-if="item.settlementStatus == 1">
                                    <span>费用说明：<text class="text">{{ item.remark || '--' }}</text></span>
                                    <span class="status">{{ statusHash[item.settlementStatus] }}</span>
                                </p>
                                <p>付款时间：<text class="text">{{item.settlementTime || '--'}}</text></p>
                                <p>金额：<text class="text">{{item.finalAmount || '--'}}元</text></p>
                                <p v-if="item.settlementStatus == 2">发票：<text class="text">{{item.invoiceNum}}张</text></p>
                            </view>
                            <view class="list_btn_items" v-if="(item.settlementStatus == 0 && $hasPermi('nmb:purchaseOrder:costFee:fillFee')) ||($hasPermi('nmb:financeOrder:costFee:pay') && item.settlementStatus == 2)">
                                <view class="btn_section">
                                    <view class="btn_item" @click="repayment(item)" v-if="item.settlementStatus == 0 && $hasPermi('nmb:purchaseOrder:costFee:fillFee')">
                                        <img class="icon" src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/zhoudingdan/dingdan.png" alt="">
                                        <text>补充信息</text>
                                    </view>
                                    <view class="btn_item" @click="pay(item)" v-if="$hasPermi('nmb:financeOrder:costFee:pay') && item.settlementStatus == 2">
                                        <img class="icon" src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/zhoudingdan/yulebao.png" alt="">
                                        <text>{{btnHash[item.settlementType]}}</text>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </view>
                </section>
            </template>
			<template v-else>
				<u-empty text="暂无数据" mode="coupon" :icon-size='150'></u-empty>
			</template>
            <div :style="'height:'+ (isIphonex ? 152 : 118) + 'rpx'" v-if="$hasPermi('nmb:purchaseOrder:costFee:addOtherFee')"></div>
        </scroll-view>
		<view class="container-footer" :style="'padding:'+(isIphonex ? '40rpx 60rpx' : '15rpx 60rpx')" v-if="$hasPermi('nmb:purchaseOrder:costFee:addOtherFee')">
			<u-button hover-class='none' :custom-style="{
				'background-color': '#40CA8F',
				'color':'white'
			}" shape="circle" @click="repayment1">添加其他费用</u-button>
		</view>
    </view>
</template>

<script>
import { page } from '@/api/pages/settlement.js'
export default {
    name: '',
    data() {
        return {
            scrollTop: 0,
            dataList: [],
            purchaseOrderId: '',
            statusHash: {
                0: '待补充',
                1: '已付款',
                2: '待付款'
            },
            btnHash: {
                1: '采购货款',
                2: '运输费',
                3: '服务费',
                4: '其他费用'
            }
        }
    },
    onLoad(opation) {
        this.purchaseOrderId = opation.purchaseOrderId
        this.getList()
    },
    onShow() {
        this.getList()
    },
    methods: {
        getList(val) { 
            uni.showLoading({
                title: '加载中',
				icon: 'none'
			})
            
            page({
                orderId: this.purchaseOrderId
            }).then(response=>{
                if(response.code === 200 ) {
                    this.dataList =response.result;
                }
            })
            uni.hideLoading()
        },
        repayment(item) {
            uni.navigateTo({
                url: `/myPackge1/pages/payment/form?settlementType=${item.settlementType}&settlementId=${item.settlementId}&finalAmount=${item.finalAmount || ''}`
            })
        },
        pay(item) {
            uni.navigateTo({
                url: `/myPackge1/pages/payment/pay?settlementType=${item.settlementType}&settlementId=${item.settlementId}&finalAmount=${item.finalAmount || ''}`
            })
        },
        details(item) {
            uni.navigateTo({
                url: `/myPackge1/pages/payment/detail?settlementId=${item.settlementId}`
            })
        },
        repayment1() {
            uni.navigateTo({
                url: '/myPackge1/pages/payment/form?settlementType=4&purchaseOrderId=' + this.purchaseOrderId
            })
        }
    },
}
</script>

<style scoped lang="scss">
@import "@/common/css/listItem.scss";
.main{
    padding-top: 2rpx;
}
.container-footer {
    width: 100%;
    position: fixed;
    bottom: 0;
    background-color: white;
    color: white;
}
.item{
    display: flex;
    align-content: center;
    justify-content: space-between;
}
</style>