<template>
    <view>
        <scroll-view class="main" scroll-y>
            <block>
                <section>
                    <view class="list-section" >
                        <view class="middle" style="width:100%'">
                            <view class="list-item">
                                <p>费用类型：<span>{{ settlementHash[dataInfo.settlementType] }}</span></p>
                                <p>应付金额（元）：<span>{{ dataInfo.finalAmount || '' }}</span></p>
                                <p>扣款金额（元）：<span>{{ dataInfo.deductAmount || '' }}</span></p>
                                <p>实付金额（元）：<span>{{ dataInfo.payAmount || '' }}</span></p>
                                <p>备注：<span>{{ dataInfo.remark || '' }}</span></p>
                            </view>
                        </view>
                    </view>
                </section>
                <section>
                    <view class="list-section" >
                        <view class="middle" style="width:100%'">
                            <view class="list-item">
                                <p>账户名称：<span>{{ dataInfo.payeeAccountName || '' }}</span></p>
                                <p>收款银行：<span>{{ dataInfo.payeeBankName || '' }}</span></p>
                                <p>收款账号：<span>{{ dataInfo.payeeBankNo || '' }}</span></p>
                            </view>
                        </view>
                    </view>
                </section>
                <section>
                    <view class="list-section" >
                        <view class="middle" style="width:100%'">
                            <view class="list-item">
                                <p>发票等相关凭证：
                                    <span class="link" v-if="dataInfo.contractUrl" @click="showPdf(dataInfo.contractUrl)">点击查看</span>
                                </p>
                            </view>
                        </view>
                    </view>
                </section>
            </block>
        </scroll-view>
    </view>
</template>
<script>
import { info } from '@/api/pages/settlement'
export default {
    name: '',
    data() {
        return {
            dataInfo: {},
            settlementId: '',
            settlementHash: {
                1: '采购货款',
                2: '运输费',
                3: '服务费',
                4: '其他费用',
                5: '售牛款',
            }
        }
    },
    onLoad(opation) {
        // 接收路由参数
        this.settlementId = opation.settlementId
        this.getInfo()
    },
    methods: {
        getInfo() { 
            uni.showLoading({
                title: '加载中',
				icon: 'none'
			})
            info({
                settlementId: this.settlementId
            }).then(response=>{
                if(response.code === 200 ) {
                    this.dataInfo =response.result || {};
                }
            })
            uni.hideLoading()
        },
        previewImage(url) {
            uni.previewImage({
                urls: [url],
            })
        },
        showPdf(url) {
			wx.downloadFile({
				url,
				success: (res) => {
                    uni.saveFile({
                        tempFilePath: res.tempFilePath, //临时路径
                        success: function (res) {
                            uni.openDocument({
                                filePath: res.savedFilePath,
                                showMenu: true,
                                success: function (res) {
                                    console.log('打开文档成功');
                                }
                            });
                        }
                    })
				},
			})
        }
    },
}
</script>

<style scoped lang="scss">
    @import "@/common/css/superviseHome.scss";
    .list-item p{
        white-space: wrap;
        line-height: 48rpx;
            font-weight: 400;
            font-size: 26rpx;
            color: #999999;
            display: flex;
            justify-content: space-between;
             span{
                display: block;
                color: #333333;
            }
    }
    .file_box{
        p{
            line-height: 48rpx;
            font-weight: 400;
            font-size: 26rpx;
            color: #999999;
            span{
                color: #333333;
            }
        }
        .file_items{
            display: flex;
            flex-wrap: wrap;
            image{
                width: 154rpx;
                height: 154rpx;
                background: #d8d8d8;
                border-radius: 16rpx;
                margin-right: 20rpx;
            }
        }
    }
    .link{
        color: #2B6BFE !important;
    }
</style>
