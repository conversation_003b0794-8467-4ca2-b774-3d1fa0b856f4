<template>
	<view>
		<scroll-view class="main" scroll-y :scroll-with-animation="true">
			<view class="container">
				<u-form :model="form" ref="uForm" :error-type="errorType" label-width="auto" label-position="top">
					<u-form-item label="费用类型">
						<text>{{ settlementHash[settlementType] }}</text>
					</u-form-item>
					<u-form-item label="应付金额（元）" prop="finalAmount">
						<u-input v-model="form.finalAmount" placeholder="请输入应付金额" />
					</u-form-item>
					<u-form-item label="扣款金额（元）" prop="deductAmount">
						<u-input v-model="form.deductAmount" placeholder="请输入扣款金额" />
					</u-form-item>
					<u-form-item label="实付金额" prop="finalAmount">
						<u-input v-model="actual" disabled placeholder="请输入实付金额" />
					</u-form-item>
					<u-form-item label="备注" prop="remark">
						<u-input v-model="form.remark" placeholder="请输入备注" />
					</u-form-item>
					<u-form-item label="账户名称" prop="payeeAccountName">
						<u-input v-model="form.payeeAccountName" placeholder="请输入账户名称" />
					</u-form-item>
					<u-form-item label="收款银行" prop="payeeBankName">
						<u-input v-model="form.payeeBankName" placeholder="请输入收款银行" />
					</u-form-item>
					<u-form-item label="收款账号" prop="payeeBankNo">
						<u-input v-model="form.payeeBankNo" placeholder="请输入收款账号" />
					</u-form-item>
					<u-form-item label="支付凭证" prop="certificateUrl" :border-bottom="false" style="overflow: visible">
						<view class="uploadImage">
							<view class="itemAlready" v-if="certificate">
								<image mode="scaleToFill" :src="form.certificateUrl" @click="previewImage(form.certificateUrl)" />
								<view class="closeIcon" @click="deleteImage(index)"></view>
							</view>
							<view class="item" v-if="!certificate">
								<view class="uploadIcon" @click="unloadImage()">点击上传</view>
							</view>
						</view>
					</u-form-item>
				</u-form>
			</view>
			<div :style="'height:' + (isIphonex ? 172 : 148) + 'rpx'"></div>
		</scroll-view>
		<view class="add-btn" @click="addPayment">提交</view>
	</view>
</template>
<script>
import { uploadFiles } from '@/api/obsUpload/index'
import { pay } from '@/api/pages/settlement'
export default {
data() {
	return {
		isIphonex: getApp().globalData.systemInfo.isIphonex,
		form: {
			settlementId: '',
			finalAmount: '',
			payeeAccountName: '',
			payeeBankName: '',
			payeeBankNo: '',
			certificateUrl: '',
			remark: ''
		},
		certificate: false,
		errorType: ['message'],
		rules: {},
		settlementType: '',
		purchaseOrderId: '',
		settlementId: '',
		actual: '',
		settlementHash: {
			1: '采购货款',
			2: '运输费',
			3: '服务费',
			4: '其他费用'
		}
	}
},
onLoad(opation) {
	this.settlementType = opation.settlementType
	this.purchaseOrderId = opation.purchaseOrderId
	this.settlementId = opation.settlementId
	this.form.finalAmount = opation.finalAmount
},
onReady() {
	this.$refs.uForm.setRules(this.rules)
},
methods: {
	deleteImage() {
		this.form.contentImages = ''
		this.certificate = false
		this.$forceUpdate()
	},
	unloadImage() {
		const that = this
		uni.chooseImage({
			count: 1, //默认9
			sizeType: ['original', 'compressed'], //可以指定是原图还是压缩图，默认二者都有
			sourceType: ['album', 'camera'], //从相册选择
			name: 'file',
			success: function (res) {
				uploadFiles({
					filePath: res.tempFilePaths[0],
				}).then((data) => {
					that.form.certificateUrl = data
					that.certificate = true
					that.form.certificateUrl != '' ? that.resetField('certificateUrl') : ''
				})
			},
			fail(e) {},
		})
	},
	resetField(value) {
		this.$refs.uForm.fields.forEach((e) => {
			if (e.prop == value) {
			e.resetField()
			}
		})
	},
	previewImage(url) {
		uni.previewImage({
			urls: [url],
		})
	},
	resetField(value) {
		this.$refs.uForm.fields.forEach(e => {
			if (e.prop == value) {
				e.resetField()
			}
		})
	},
	addPayment() {
		pay({
			...this.form,
			settlementId: this.settlementId
		}).then(res => {
			if (res.code == 200) {
				uni.showToast({
					title: '支付成功',
					icon: 'none'
				})
				uni.navigateBack({
					delta:1
				})
			}
		})
	},
},
}
</script>

<style lang="less" scoped>
.container {
	margin: 30rpx;
	background: #fff;
	box-sizing: border-box;
	padding: 30rpx 32rpx 40rpx;
	border-radius: 30rpx;

	/deep/ .u-form-item {
		padding: 20rpx 20rpx !important;
	}

	.tips {
		font-size: 28rpx;
		color: #c0c3ca;
	}

	.voucher {
		padding-bottom: 40rpx;
		font-size: 28rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #999999;
	}

	.all-img {
		image {
		width: 154rpx;
		height: 154rpx;
		background: #d8d8d8;
		border-radius: 16rpx;
		margin-right: 20rpx;
		}
	}

	.manyMode {
		background: transparent;
		height: auto;
	}

	.uploadImage {
		display: flex;
		flex-wrap: wrap;
		// justify-content: space-between;
		// align-content: space-between;
		position: relative;

		.itemAlready {
		width: 140rpx;
		height: 140rpx;
		border-radius: 8rpx;
		position: relative;
		margin: 0 20rpx 10rpx 0rpx;

		image {
			width: 100%;
			height: 100%;
			border-radius: 8rpx;
		}

		.closeIcon {
			width: 32rpx;
			height: 32rpx;
			background-image: url('../../../static/modalImg/error.png');
			position: absolute;
			background-size: cover;
			top: -10rpx;
			right: -10rpx;
		}
	}

	.item {
	width: 140rpx;
	height: 140rpx;
	border-radius: 8rpx;
	position: relative;
	border: 2rpx dashed #d8d8d8;

	.uploadIcon {
		width: 100%;
		height: 120rpx;
		display: flex;
		justify-content: center;
		align-items: flex-end;
		font-size: 24rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #d8d8d8;
		background: url('https://fengtaohui.oss-cn-beijing.aliyuncs.com/uploads/image/20230105/6400955ab08a2f639e9790b815c418db63b666a9d7e40.png') no-repeat;
		background-size: 20rpx 20rpx;
		background-position: center 30rpx;
		position: absolute;
		top: 0;
		left: 0;
		bottom: 0;
		right: 0;
		margin: auto;
		z-index: 5;
	}
	}
}
}

.add-btn {
	position: fixed;
	z-index: 9;
	left: 60rpx;
	right: 60rpx;
	bottom: 40rpx;
	height: 80rpx;
	font-size: 32rpx;
	font-weight: 500;
	line-height: 80rpx;
	text-align: center;
	border-radius: 40rpx;
	color: #fff;
	font-family: PingFangSC-Regular, PingFang SC;
	background: #40CA8F;
}
</style>