<template>
    <view style="padding-bottom: 40rpx;">
        <scroll-view class="main" scroll-y>
            <block>
                <section>
                    <view class="title">
                        合同信息
                    </view>
                    <view class="list-section" >
                        <view class="middle" style="width:100%';">
                            <view class="list-item">
                                <p>合同编号：<span>{{ contractInfo.saleContractCode }}</span></p>
                                <p>需求方公司名：<span>{{ contractInfo.companyName }}</span></p>
                                <p>需求方详细地址：<span>{{ contractInfo.province }}{{ contractInfo.city }}{{ contractInfo.detailAddress }}</span></p>
                                <p>销售员：<span>{{ contractInfo.saleUserName }}</span></p>
                                <p>项目经理：<span>{{ contractInfo.projectManagerName }}</span></p>
                                <p>合同期限：<span>{{ contractInfo.contractStartTime }} ~ {{ contractInfo.contractEndTime }}</span></p>
                                <p>合同文件：
                                    <span class="link" v-if="contractInfo.contractUrl" @click="previewFiles(contractInfo.contractUrl)">{{ getFileName(contractInfo.contractUrl) }}</span>
                                    <span v-else>--</span>
                                </p>
                            </view>
                        </view>
                    </view>
                </section>
            </block>
        </scroll-view>
    </view>
</template>

<script>
import { saleContractInfo } from '@/api/pages/salesContract'
export default {
    onLoad(options) {
        this.saleContractId = options.saleContractId
        this.getInfo(this.saleContractId)
    },
    data() {
        return {
            isIphonex: getApp().globalData.systemInfo.isIphonex,
            saleContractId: '',
            contractInfo: {}
        }
    },
    mounted() {
    },
    methods: {
        getInfo(id) {
            saleContractInfo({ saleContractId: id }).then(res => {
                this.contractInfo = res.result
            })
        },
        previewImage(url) {
            uni.previewImage({
                urls: [url],
            })
        },
        getFileName(url) {
      const matches = url.match(/[^/?#]+(?=[?#]|$)/);
      return matches ? decodeURIComponent(matches[0]) : '';
    },
    previewFiles(url){
      console.log(url)
      wx.downloadFile({
        url: url,
        success: function(res) {
          const filePath = res.tempFilePath
          wx.openDocument({
            filePath: filePath,
            fileType: 'pdf',
            success: function(res) {
            },
            fail(e) {
              console.log(e)
            }
          })
        },fail(e){
          console.log(e)
        },

      })
    },
    },
}
</script>

<style scoped lang="scss">
@import "@/common/css/superviseHome.scss";
.list-item p{
    white-space: normal !important;
    display: flex;
    justify-content: space-between;
    font-size: 26rpx;
    color: #999;
    line-height: 43rpx;

    span{
        width: 60%;
        text-align: right;
        color: #333;
        font-size: 26rpx;
        font-weight: 400;
    }
}
.title{
    list-style: 50rpx;
    font-weight: 500;
    font-size: 32rpx;
    color: #222222;
    padding: 20rpx;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .btn-box{
        display: flex;
        align-items: center;
    }
    text{
        font-weight: 400;
        font-size: 28rpx;
        color: #EA501E;
        margin-right: 20rpx;
    }
}
.link{
    color: #1DB17A !important;
}
</style>