<template>
  <view>
    <section v-for="(item, index) in list" :key="index" @click="handleDetail(item)">
      <view class="list-section">
        <view class="items">
          <view class="item_title">
            <view class="title">{{ item.saleContractCode }}</view>
          </view>
          <view class="item_content">
            <p class="item">需求方：<text class="text">{{ item.companyName }}({{ item.projectManagerName}})</text></p>
            <p class="item">合同期限：<text class="text">{{ item.contractStartTime }} - {{ item.contractEndTime}}</text></p>
            <view class="file_box">
              <view>合同文件：</view>
              <view class="file_name" @click.stop="previewFiles(item.contractUrl)">
                {{ getFileName(item.contractUrl) }}
              </view>
            </view>
          </view>
          <view class="list_btn_items">
            <view class="btn_section">
              <view class="btn_item" @click.stop="libraryRecords(item)" v-if="$hasPermi('nmb:demandOrder:list')">
                <img class="icon" src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/xiaoshouhetong/dingdan.png" alt="" />
                <text>周订单</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </section>
  </view>
</template>

<script>
export default {
  props: ['list'],
  computed: {
    getContractUrls() {
      return (val) => {
        if(!val) return
        return val.split(',')
      }
    }
  },
  data() {
    return {}
  },
  methods: {
    libraryRecords(item) {
      uni.navigateTo({
        url: `/myPackge1/pages/weeklyOrders/index?saleContractId=${item.saleContractId}`
      })
    },
    previewImage(url) {
      uni.previewImage({
        urls: [url],
      })
    },
    handleDetail(item) {
      uni.navigateTo({
        url: `/myPackge1/pages/salesContract/contractDetail?saleContractId=${item.saleContractId}`
      })
    },
    getFileName(url) {
      const matches = url.match(/[^/?#]+(?=[?#]|$)/);
      return matches ? decodeURIComponent(matches[0]) : '';
    },
    previewFiles(url){
      console.log(url)
      wx.downloadFile({
        url: url,
        success: function(res) {
          const filePath = res.tempFilePath
          wx.openDocument({
            filePath: filePath,
            fileType: 'pdf',
            success: function(res) {},
          })
        },

      })
    },
  },
}
</script>
<style lang="scss" scoped>
@import '@/common/css/listItem.scss';
// .explain {
//   font-size: 26rpx !important;
//   display: block !important;
//   color: #999999;
//   width: 100%;
//   font-family: PingFang SC-Medium;
//   p {
//     white-space: nowrap;
//     margin-bottom: 25rpx;
//   }
//   text {
//     color: #333;
//   }
//   .text {
//     width: 180rpx;
//   }
//   .img-box {
//     width: 100%;
//     color: #37BA7E;
//     img {
//       width: 85rpx;
//       height: 85rpx;
//       border-radius: 20rpx;
//       margin-right: 20rpx;
//     }
//   }
// }
// .item {
//   width: 100%;
//   display: flex;
//   justify-items: center;
//   padding: 30rpx 0 0 0;
//   text-align: center;
//   border-top: 2rpx solid #f2f2f2;
//   margin-top: 20rpx;
//   .icon {
//     width: 50rpx;
//     height: 50rpx;
//   }

  // text {
  //   font-size: 28rpx;
  //   font-family: PingFang SC-Medium, PingFang SC;
  //   font-weight: 400;
  //   color: #40CA8F;
  //   padding-left: 10rpx;
  //   height: 50rpx;
  //   line-height: 50rpx;
  // }

  // &:nth-child(4n) {
  //   width: 17%;
  // }
// }
</style>
