// 价格明细
export const tableColumnsConfigPrice = [
  { title: '销售日期',key: 'salesDate', width: '240rpx' },
  { title: '销售订单',key: 'purchaseOrderCode', width: '350rpx' },
  { title: '销售地',key: 'deliveryCity', width: '240rpx' },
  { title: '销售单价(元/kg)',key: 'salesUnitPrice', width: '240rpx' },
  { title: '采购订单',key: 'purchaseOrderCode', width: '350rpx' },
  { title: '采购日期',key: 'purchaseDate', width: '300rpx' },
  { title: '牛源地',key: 'cowSource', width: '200rpx' },
  { title: '养殖户',key: 'farmersName', width: '300rpx' },
  { title: '采购单价（元/kg）',key: 'purchaseUnitPrice', width: '200rpx' },
  { title: '进销差价（元/kg）',key: 'salesPurchaseUnitPriceDiff', width: '200rpx' },
]
//成本分析表格配置
export const tableColumnsConfigCost = {
  // 采购明细
  purchase: [
    { key: 'purchaseDate', title: '采购日期', width: '200rpx' },
    { key: 'purchaseOrderCode', title: '采购订单', width: '300rpx' },
    { key: 'payeeName', title: '养殖户', width: '200rpx' },
    { key: 'purchaseNumber', title: '采购数量(头)', width: '200rpx' },
    { key: 'purchaseUnitPrice', title: '采购单价(元/kg)', width: '250rpx' },
    { key: 'purchaseWeight', title: '采购重量(kg)', width: '250rpx' },
    { key: 'finalAmount', title: '应付金额(万元)', width: '250rpx' },
    { key: 'deductAmount', title: '扣款金额(万元)', width: '250rpx' },
    { key: 'payAmount', title: '实付金额(万元)', width: '250rpx' },
    { key: 'salesDate', title: '销售日期', width: '250rpx' },
    { key: 'purchaseOrderCode', title: '销售订单', width: '300rpx' },
    { key: 'customerName', title: '客户', width: '350rpx' },
  ],
  service: [
    { key: 'purchaseDate', title: '采购日期', width: '200rpx' },
    { key: 'purchaseOrderCode', title: '采购订单', width: '300rpx' },
    { key: 'brokerName', title: '牛经纪', width: '200rpx' },
    { key: 'purchaseNumber', title: '采购数量(头)', width: '200rpx' },
    { key: 'acceptanceResult', title: '验收结果', width: '200rpx' },
    { key: 'satisfaction', title: '满意度', width: '200rpx' },
    { key: 'finalAmount', title: '应付金额(万元)', width: '250rpx' },
    { key: 'deductAmount', title: '扣款金额(万元)', width: '250rpx' },
    { key: 'payAmount', title: '实付金额(万元)', width: '250rpx' },
    { key: 'salesDate', title: '销售日期', width: '250rpx' },
    { key: 'purchaseOrderCode', title: '销售订单', width: '300rpx' },
    { key: 'customerName', title: '客户', width: '350rpx' },
  ],
  transportation: [
    { key: 'purchaseDate', title: '采购日期', width: '200rpx' },
    { key: 'purchaseOrderCode', title: '采购订单', width: '300rpx' },
    { key: 'driverName', title: '承运人', width: '200rpx' },
    { key: 'deliveryOnTimeStatus', title: '准时达', width: '200rpx', format:(v)=>{return v==1?'是':'否'} },
    { key: 'delayHours', title: '延迟时长（小时）', width: '200rpx' },
    { key: 'finalAmount', title: '应付金额(万元)', width: '250rpx' },
    { key: 'deductAmount', title: '扣款金额(万元)', width: '250rpx' },
    { key: 'payAmount', title: '实付金额(万元)', width: '250rpx' },
    { key: 'salesDate', title: '销售日期', width: '250rpx' },
    { key: 'purchaseOrderCode', title: '销售订单', width: '300rpx' },
    { key: 'customerName', title: '客户', width: '350rpx' },
  ],
  otherExpenses: [
    { key: 'purchaseDate', title: '采购日期', width: '200rpx' },
    { key: 'purchaseOrderCode', title: '采购订单', width: '300rpx' },
    { key: 'otherExpenses', title: '费用说明', width: '200rpx' },
    { key: 'finalAmount', title: '应付金额(万元)', width: '250rpx' },
    { key: 'deductAmount', title: '扣款金额(万元)', width: '250rpx' },
    { key: 'payAmount', title: '实付金额(万元)', width: '250rpx' },
    { key: 'salesDate', title: '销售日期', width: '250rpx' },
    { key: 'purchaseOrderCode', title: '销售订单', width: '300rpx' },
    { key: 'customerName', title: '客户', width: '350rpx' },
  ],
}
// 牛源分析表格配置
export const tableColumnsConfigCow = {
  customer: [
    { key: 'salesDate', title: '销售日期', width: '200rpx' },
    { key: 'purchaseOrderCode', title: '销售订单', width: '300rpx' },
    { key: 'customerName', title: '客户', width: '200rpx' },
    { key: 'salesUnitPrice', title: '销售单价(元/kg)', width: '250rpx' },
    { key: 'salesWeight', title: '销售重量(kg)', width: '250rpx' },
    { key: 'purchaseDate', title: '采购日期', width: '200rpx' },
    { key: 'purchaseOrderCode', title: '采购订单', width: '300rpx' },
    { key: 'purchaseUnitPrice', title: '采购均价(元/kg)', width: '250rpx' },
    { key: 'purchaseWeight', title: '采购重量(kg)', width: '250rpx' },
    { key: 'purchaseSaleDiffUnitPrice', title: '进销差价(元/kg)', width: '250rpx' },
    { key: 'purchaseSaleDiffWeight', title: '重量差额(kg)', width: '250rpx' },
  ],
  broker: [
    { key: 'purchaseDate', title: '采购日期', width: '200rpx' },
    { key: 'purchaseOrderCode', title: '采购订单', width: '300rpx' },
    { key: 'brokerName', title: '牛经纪', width: '200rpx' },
    { key: 'purchaseNumber', title: '采购数量(头)', width: '200rpx' },
    { key: 'acceptanceResult', title: '验收结果', width: '200rpx' },
    { key: 'satisfaction', title: '满意度', width: '200rpx' },
    { key: 'serviceAmount', title: '服务费(万元)', width: '250rpx' },
    { key: 'serviceUnitAmount', title: '服务费(元/头)', width: '250rpx' },
    { key: 'salesDate', title: '销售日期', width: '200rpx' },
    { key: 'purchaseOrderCode', title: '销售订单', width: '300rpx' },
    { key: 'customerName', title: '客户', width: '200rpx' },
  ],
  farmer: [
    { key: 'purchaseDate', title: '采购日期', width: '200rpx' },
    { key: 'purchaseOrderCode', title: '采购订单', width: '300rpx' },
    { key: 'farmersName', title: '养殖户', width: '200rpx' },
    { key: 'purchaseUnitPrice', title: '购单价（元/kg）', width: '250rpx' },
    { key: 'purchaseWeight', title: '采购重量（kg）', width: '200rpx' },
    { key: 'purchaseAverageWeight', title: '牛只均重（kg）', width: '200rpx' },
    { key: 'salesDate', title: '销售日期', width: '200rpx' },
    { key: 'purchaseOrderCode', title: '销售订单', width: '300rpx' },
    { key: 'customerName', title: '客户', width: '200rpx' },
  ],
  driver: [
    { key: 'transportDate', title: '运输日期', width: '200rpx' },
    { key: 'purchaseOrderCode', title: '采购订单', width: '300rpx' },
    { key: 'purchaseOrderCode', title: '销售订单', width: '300rpx' },
    { key: 'driverName', title: '承运人', width: '200rpx' },
    { key: 'origin', title: '出发地', width: '200rpx' },
    { key: 'destination', title: '到达地', width: '200rpx' },
    { key: 'transportHours', title: '运输时长（小时）', width: '200rpx' },
    { key: 'purchaseNumber', title: '发车数量（头）', width: '200rpx' },
    { key: 'arriveNumber', title: '抵达数量（头）', width: '200rpx' },
    { key: 'purchaseWeight', title: '发车重量（kg）', width: '200rpx' },
    { key: 'arriveWeight', title: '抵达重量（kg）', width: '200rpx' },
    { key: 'arriveWeightDiff', title: '重量差（kg）', width: '200rpx' },
    { key: 'deliveryOnTimeStatus', title: '准时达', width: '200rpx', format:(v)=>{return v==1?'是':'否'} },
    { key: 'transportHours', title: '到达时长（h）', width: '200rpx' },
  ],
}


// 运输明细表格配置
export const tableColumnsConfigTransport = [
  { title: '运输日期',key: 'transportDate', width: '200rpx' },
  { title: '出发地',key: 'origin', width: '200rpx' },
  { title: '目的地',key: 'destination', width: '200rpx' },
  { title: '运输时长（小时）',key: 'transportHours', width: '250rpx' },
  { title: '发车数量(头)',key: 'departureQuantity', width: '250rpx' },
  { title: '抵达数量(头)',key: 'arrivalQuantity', width: '250rpx' },
  { title: '数量差(头)',key: 'transportLossNumber', width: '250rpx' },
  { title: '发车重量(kg)',key: 'departureWeight', width: '250rpx' },
  { title: '抵达重量(kg)',key: 'arrivalWeight', width: '250rpx' },
  { title: '重量差(kg)',key: 'transportLossWeight', width: '250rpx' },
  { title: '准时达',key: 'deliveryOnTimeStatus', width: '250rpx', format:(v)=>{return v==1?'是':'否'} },
  { title: '延迟时长(h)',key: 'delayHours', width: '250rpx' },
  { title: '销售订单',key: 'demandOrderCode', width: '300rpx' },
  { title: '采购订单',key: 'purchaseOrderCode', width: '300rpx' }
]