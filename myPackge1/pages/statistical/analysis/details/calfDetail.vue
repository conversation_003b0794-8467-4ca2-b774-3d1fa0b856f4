<template>
    <view class="sale-detail">
        <view class="table-container">
            <scroll-view class="table-scroll" scroll-x="true">
                <view class="table-inner" :style="{ width: tableWidth }">
                    <!-- 表格头部 -->
                    <view class="table-header">
                        <view class="header-row">
                            <view class="header-cell" v-for="(column, index) in tableColumns" :key="index"
                                :style="{ width: column.width }">
                                {{ column.title }}
                            </view>
                        </view>
                    </view>

                    <!-- 表格内容 -->
                    <scroll-view class="table-body" scroll-y="true" @scrolltolower="loadMore"
                        :style="{ height: tableHeight + 'px' }" :scroll-top="scrollTop" @scroll="onScroll">
                        <!-- 数据加载中提示 - 初始加载 -->
                        <view class="loading-initial" v-if="loading && tableData.length === 0">
                            <u-loading size="36" mode="circle"></u-loading>
                            <text>数据加载中...</text>
                        </view>
                        <view class="table-content" v-else>
                            <view class="table-row" v-for="(item, rowIndex) in tableData" :key="rowIndex">
                                <view v-for="(column, colIndex) in tableColumns" :key="colIndex" class="table-cell" :style="{ width: column.width }">
                                    {{ formatCellValue(item, column) }}
                                </view>
                            </view>
                            <!-- 无数据提示 -->
                            <view class="no-data" v-if="tableData.length === 0 && !loading">
                                <u-empty mode="data" icon="http://cdn.uviewui.com/uview/empty/data.png">
                                    <text>暂无数据</text>
                                </u-empty>
                            </view>

                            <!-- 加载更多提示 - 滚动加载 -->
                            <view class="loading-more" v-if="loading && tableData.length > 0">
                                <u-loading size="24" mode="circle"></u-loading>
                                <text>加载更多数据...</text>
                            </view>

                            <!-- 无更多数据提示 -->
                            <view class="no-more" v-if="!hasMore && tableData.length > 0 && !loading">
                                <u-divider text="已加载全部数据" :hairline="true"></u-divider>
                            </view>
                        </view>
                    </scroll-view>
                </view>
            </scroll-view>
        </view>
    </view>
</template>

<script>
import { cowSourceCustomerAnalysisList, cowSourceBrokerAnalysisList, cowSourceFarmersAnalysisList, cowSourceDriversAnalysisList } from '@/api/pages/statistical';
import {tableColumnsConfigCow} from './config/tableColumns'
export default {
    data() {
        return {
            provinceId: '',
            pageNum: 1,
            pageSize: 20,
            loading: false,
            hasMore: true,
            scrollTop: 0,
            tableHeight: 500, // 默认高度，会在onReady中计算
            tableData: [],
            sourceType: '',
            titleMap: {
                'customer': '客户详情',
                'broker': '牛经纪详情',
                'farmer': '养殖户详情',
                'driver': '承运人详情'
            }
        };
    },
    computed: {
        tableColumns(){
            return tableColumnsConfigCow[this.sourceType]
        },
        // 计算表格总宽度
        tableWidth() {
            console.log(this.tableColumns)
            if(!this.tableColumns) return '100%';
            return this.tableColumns.reduce((sum, { width }) => sum + parseInt(width), 0) + 20 + 'rpx';
        }
    },
    onLoad(option) {
        if (option.provinceId) {
            this.provinceId = option.provinceId;
        }
        this.sourceType = option.sourceType;
        console.log(this.provinceId)
        uni.setNavigationBarTitle({
            title: this.titleMap[this.sourceType]
        });
        // 获取数据
        this.getTableData();
    },
    onReady() {
        // 计算表格高度
        this.calculateTableHeight();
    },
    methods: {
        formatCellValue(item, column) {
            const value = item[column.key];
            return column.format? column.format(value): value;
        },
        // 计算表格高度
        calculateTableHeight() {
            // 使用固定高度，因为卡片组件的高度相对固定
            const windowHeight = uni.getSystemInfoSync().windowHeight;
            // 估计卡片组件的高度约为300rpx，转换为px
            const cardHeight = uni.upx2px(300);
            // 减去卡片高度和一些边距
            this.tableHeight = windowHeight - cardHeight - 20;

            // 或者使用延迟查询，等待组件渲染完成
            setTimeout(() => {
                const query = uni.createSelectorQuery().in(this);
                query.select('.info-cards').boundingClientRect(data => {
                    if (data) {
                        this.tableHeight = windowHeight - data.height - 20;
                    }
                }).exec();
            }, 300);
        },

        // 获取表格数据
        async getTableData(isLoadMore = false) {
            const API_MAP = {
                customer: cowSourceCustomerAnalysisList,
                broker: cowSourceBrokerAnalysisList,
                farmer: cowSourceFarmersAnalysisList,
                driver: cowSourceDriversAnalysisList
            };
            const apiMethod = API_MAP[this.sourceType];
            if (this.loading) return;
            this.loading = true;
            try {
                const params = {
                    provinceId: this.provinceId,
                    pageNum: this.pageNum,
                    pageSize: this.pageSize
                };
                const res = await apiMethod(params);
                if (res.code === 200 && res.result) {
                    this.tableData = isLoadMore? [...this.tableData, ...res.result.list]: res.result.list;
                    this.hasMore = !res.result.lastPage;
                }
                console.log('tableData', this.tableData);
            } catch (err) {
                console.error('获取详情失败', err);
                uni.showToast({
                    title: '获取数据失败',
                    icon: 'none'
                });
            } finally {
                this.loading = false;
            }
        },
        // 加载更多数据
        loadMore() {
            if (!this.hasMore || this.loading) return;

            this.pageNum++;
            this.getTableData(true);
        },

        // 滚动事件处理
        onScroll(e) {
            this.scrollTop = e.detail.scrollTop;
        },
    }
};
</script>

<style lang="scss" scoped>
.sale-detail {
    min-height: 100vh;
    background-color: #f5f7fa;
    padding-bottom: 30rpx;
}

// 表格容器样式
.table-container {
    margin: 20rpx;
    background-color: #fff;
    border-radius: 10rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

// 整体滚动容器
.table-scroll {
    width: 100%;
    overflow-x: auto;
}

// 表格内部容器
.table-inner {
    display: inline-block;
    min-width: 100%;
}

// 表格头部样式
.table-header {
    background-color: #f8f8f8;
    position: sticky;
    top: 0;
    z-index: 5;

    .header-row {
        display: flex;
    }

    .header-cell {
        padding: 20rpx 15rpx;
        font-size: 28rpx;
        font-weight: bold;
        color: #333;
        text-align: center;
        border-right: 1px solid #eee;

        &:last-child {
            border-right: none;
        }
    }
}

// 表格内容样式
.table-body {
    .table-content {
        .table-row {
            display: flex;
            border-bottom: 1px solid #eee;

            &:last-child {
                border-bottom: none;
            }

            &:nth-child(even) {
                background-color: #f9f9f9;
            }
        }

        .table-cell {
            padding: 20rpx 15rpx;
            font-size: 28rpx;
            color: #666;
            text-align: center;
            border-right: 1px solid #eee;

            &:last-child {
                border-right: none;
            }
        }
    }
}

// 初始加载提示样式
.loading-initial {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 100rpx 0;

    text {
        margin-top: 20rpx;
        font-size: 28rpx;
        color: #999;
    }
}

// 加载更多样式
.loading-more {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 30rpx 0;
    background-color: #f9f9f9;

    text {
        margin-left: 15rpx;
        font-size: 28rpx;
        color: #666;
    }
}

// 无更多数据样式
.no-more {
    padding: 20rpx 0;
    background-color: #f9f9f9;
}

// 无数据提示样式
.no-data {
    padding: 100rpx 0;
    text-align: center;

    text {
        font-size: 28rpx;
        color: #999;
        margin-top: 20rpx;
    }
}</style>
