<template>
  <view class="info-cards">
    <view class="info-card" v-for="(card, index) in cardData" :key="index">
      <view class="card-title" :style="{ backgroundColor: card.bgColor }">{{ card.title }}</view>
      <view class="card-value" v-if="staticType==1">{{ card.value }}{{ card.unit }}</view>
      <view class="card-value" :class="card.url? 'card-img': ''" v-else @click="previewImage(card.url)">{{ card.value }}{{ card.unit }}</view>
    </view>
  </view>
</template>

<script>
import { customerSingleCount, transportAnalysisDetail } from '@/api/pages/statistical';
export default {
  props: {
    customerId: {
      type: String,
      default: ''
    },
    staticType: {
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      cardData: [
        { title: '客户', value: '', unit: '', bgColor: '#40CA8F', key: 'customerName' },
        { title: '联系人', value: '', unit: '', bgColor: '#40CA8F', key: 'contactName' },
        { title: '联系电话', value: '', unit: '', bgColor: '#40CA8F', key: 'contactPhone' },
        { title: '销售合同', value: '', unit: '张', bgColor: '#40CA8F', key: 'salesContractNumber' },
        { title: '周订单', value: '', unit: '个', bgColor: '#40CA8F',key: 'demandOrderNumber' },
        { title: '成交单', value: '', unit: '个', bgColor: '#40CA8F',key: 'purchaseOrderNumber' }
      ]
    };
  },
  created() {
  },
  watch: {
    customerId(newVal) {
      if (newVal) {
        if(this.staticType==1) {
          this.fetchCustomerInfo(newVal);
        } else {
          this.getDriverData(newVal)
        }
      }
    }
  },
  methods: {
    fetchCustomerInfo(customerId) {
      customerSingleCount({customerId}).then(res => {
        if (res.code === 200 && res.result) {
          this.cardData.forEach(card => {
            card.value = res.result[card.key] || 0;
          });
        }
      })
    },
    getDriverData(driverId){
      transportAnalysisDetail({driverId}).then(res => {
        console.log('transportAnalysisDetail', res);
        const cardDataTemplate = [
          { title: '承运人', value: '', unit: '', bgColor: '#40CA8F', key: 'driverName' },
          { title: '联系电话', value: '', unit: '', bgColor: '#40CA8F', key: 'driverPhone' },
          { title: '车牌号', value: '', unit: '', bgColor: '#40CA8F', key: 'licensePlateNumber' },
          { title: '身份证', value: '2', unit: '张', bgColor: '#40CA8F', key: 'idcardUrl', type: 'url', url: '' },
          { title: '行驶证件', value: '2', unit: '张', bgColor: '#40CA8F',key: 'licenseUrl', type: 'url', url: '' },
          { title: '运输次数', value: '', unit: '次', bgColor: '#40CA8F',key: 'transportNumber' }
        ]
        this.cardData = cardDataTemplate.map(item => {
          const resultValue = res.result[item.key];
          if (resultValue) {
            if (item.type === 'url') {
              return { ...item, url: resultValue };
            } else {
              return { ...item, value: resultValue };
            }
          }
          return item;
        })
      })
    },
    previewImage(url) {
      if(!url) return;
      const urls = url.split(',')
      uni.previewImage({
        urls: urls
      })
    },
  }
};
</script>

<style lang="scss" scoped>
.info-cards {
  display: flex;
  flex-wrap: wrap;
  padding: 20rpx;
  background-color: #fff;
  position: sticky;
  top: 0;
  z-index: 10;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  
  .info-card {
    width: 33.33%;
    padding: 10rpx;
    box-sizing: border-box;
    margin-bottom: 20rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    
    .card-title {
      width: 100%;
      height: 70rpx;
      background-color: #40CA8F;
      color: #fff;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 10rpx;
      font-size: 28rpx;
      font-weight: bold;
      transition: all 0.3s ease;
      
      &:active {
        opacity: 0.8;
        transform: scale(0.98);
      }
    }
    
    .card-value {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
      margin-top: 10rpx;
      text-align: center;
      width: 100%;
      /* overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap; */
    }
    .card-img{
      color: #08BA7E;
    }
    
    .card-label {
      font-size: 24rpx;
      color: #999;
      margin-top: 5rpx;
      text-align: center;
    }
  }
}
</style> 