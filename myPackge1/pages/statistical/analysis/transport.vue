<template>
    <view class="sale-analysis">
      <!-- 搜索组件 -->
      <statis-search @search="handleSearchResult"></statis-search>
      
      <view class="sales-overview">
        <view class="sales-total">
          <text class="total-number">{{ driverData.ontimeRate || '0' }}%</text>
          <text class="total-label">准时达</text>
        </view>
        <view class="sales-details">
          <view class="detail-item">
            <view class="detail-label">承运人</view>
            <view class="detail-value">{{ driverData.driverNumber || 0 }}个</view>
          </view>
          <view class="detail-item">
            <view class="detail-label">运输次数</view>
            <view class="detail-value">{{ driverData.transportNumber || 0 }}次</view>
          </view>
          <view class="detail-item">
            <view class="detail-label">累计数量差</view>
            <view class="detail-value">{{ driverData.transportLossNumber || '0' }}头</view>
          </view>
          <view class="detail-item">
            <view class="detail-label">累计重量差</view>
            <view class="detail-value">{{ driverData.transportLossWeight || '0' }}kg</view>
          </view>
        </view>
      </view>
      
      <!-- 客户销售列表 -->
      <view class="customer-list">
        <view class="customer-item" v-for="(item, index) in driverData.driverList" :key="index" @click="handleDetail(item)">
          <view class="customer-header">
            <text class="customer-name">{{ item.driverName }}</text>
            <view class="customer-arrow">
              <u-icon name="arrow-right" color="#08BA7E" size="28"></u-icon>
            </view>
          </view>
          
          <view class="customer-details">
            <view class="detail-row">
              <view class="detail-col">
                <view class="detail-label green-bg">运输次数</view>
                <view class="detail-value">{{ item.transportNumber }}次</view>
              </view>
              <view class="detail-col">
                <view class="detail-label blue-bg">数量差额</view>
                <view class="detail-value">{{ item.transportLossNumber }}头</view>
              </view>
            </view>
            
            <view class="detail-row">
              <view class="detail-col">
                <view class="detail-label orange-bg">重量差额</view>
                <view class="detail-value">{{ item.transportLossWeight }}kg</view>
              </view>
              <view class="detail-col">
                <view class="detail-label purple-bg">准时达</view>
                <view class="detail-value">{{ item.ontimeRate}}%</view>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 无数据提示 -->
        <view class="no-data" v-if="!driverData.driverList || driverData.driverList.length === 0">
          <text>暂无数据</text>
        </view>
      </view>
    </view>
  </template>
  
  <script>
  import statisSearch from "@/components/statisSearch/statisSearch.vue";
  import { transportAnalysis } from '@/api/pages/statistical';
  
  export default {
    components: {
      statisSearch
    },
    data() {
      return {
        driverData: {},
        searchParams: {}
      };
    },
    onLoad() {
      this.gettransportAnalysis();
    },
    methods: {
      handleSearchResult(params) {
        this.searchParams = params;
        this.gettransportAnalysis();
      },
      gettransportAnalysis() {
        transportAnalysis(this.searchParams).then(res => {
          if (res.code === 200 && res.result) {
            this.driverData = res.result;
          }
        }).catch(err => {
          console.error('获取数据失败', err);
          uni.showToast({
            title: '获取数据失败',
            icon: 'none'
          });
        });
      },
      handleDetail(item) {
        uni.navigateTo({
          url: `/myPackge1/pages/statistical/analysis/details/transportDetail?driverId=${item.driverId}`
        });
      }
    }
  };
  </script>
  
  <style lang="scss" scoped>
  .sale-analysis {
    min-height: 100vh;
    background-color: #f5f7fa;
    padding-bottom: 30rpx;
    padding-top: 30rpx;
  }
  
  .sales-overview {
    background-color: #fff;
    margin: 20rpx;
    border-radius: 20rpx;
    padding: 30rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  }
  
  .sales-total {
    display: flex;
    flex-direction: column;
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 20rpx;
    margin-bottom: 20rpx;
    
    .total-number {
      font-size: 80rpx;
      font-weight: bold;
      color: #08BA7E;
    }
    
    .total-label {
      font-size: 28rpx;
      color: #666;
      margin-top: -10rpx;
    }
  }
  
  .sales-details {
    display: flex;
    justify-content: space-between;
    
    .detail-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      
      .detail-label {
        font-size: 28rpx;
        color: #666;
        margin-bottom: 10rpx;
      }
      
      .detail-value {
        font-size: 32rpx;
        color: #333;
        font-weight: bold;
      }
    }
  }
  
  .customer-list {
    background-color: #fff;
    margin: 0 20rpx 20rpx 20rpx;
    border-radius: 20rpx;
    padding: 30rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
    
    .list-header {
      margin-bottom: 30rpx;
      
      .header-title {
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
      }
    }
    
    .customer-item {
      margin-bottom: 30rpx;
      padding-bottom: 30rpx;
      border-bottom: 1px solid #f0f0f0;
      
      &:last-child {
        border-bottom: none;
        margin-bottom: 0;
        padding-bottom: 0;
      }
    }
    
    .customer-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20rpx;
      
      .customer-name {
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
      }
    }
    
    .customer-details {
      .detail-row {
        display: flex;
        margin-bottom: 20rpx;
        
        &:last-child {
          margin-bottom: 0;
        }
      }
      
      .detail-col {
        flex: 1;
        margin-right: 20rpx;
        
        &:last-child {
          margin-right: 0;
        }
      }
      
      .detail-label {
        display: inline-block;
        padding: 6rpx 20rpx;
        border-radius: 20rpx;
        font-size: 24rpx;
        color: #fff;
        margin-bottom: 10rpx;
      }
      
      .green-bg {
      background-color: #E9FFF4;
      color: #6EC096;
    }

    .blue-bg {
      background-color: #FDF3E8;
      color: #DD9544;
    }

    .orange-bg {
      background-color: #E1EEFF;
      color: #7999D5;
    }

    .purple-bg {
      background-color: #FFEEE8;
      color: #CF5F45;
    }
      
      .detail-value {
        font-size: 28rpx;
        color: #333;
        font-weight: bold;
        padding-left: 15rpx;
      }
    }
  }
  
  .no-data {
    text-align: center;
    padding: 50rpx 0;
    color: #999;
    font-size: 28rpx;
  }
  </style>
  