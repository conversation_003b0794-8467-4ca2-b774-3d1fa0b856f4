<template>
  <view class="page-container">
    <statis-search @search="handleSearchResult" />
    <statis-total :totalInfo="totalInfo" />
    
    <!-- 列表 -->
    <view class="cost-list">
      <view class="cost-item" 
        v-for="(item, index) in costList" 
        :key="index"
        @click="handleDetails(item)"
      >
        <view class="item-header">
          <text class="item-title">{{ item.label }}</text>
          <u-icon name="arrow-right" color="#08BA7E" size="28" />
        </view>
        <view class="item-content">
          <view class="data-row" v-for="(val, idx) in item.dataLabel" :key="idx">
            <view class="data-col">
              <text class="data-label" :class="val.bg">{{ val.label }}</text>
              <text class="data-value">{{ val.value }}</text>
            </view>
          </view>
          <view class="data-row" v-for="(val, idx) in item.dataValue" :key="idx">
            <view class="data-col">
              <text class="data-label" :class="val.bg">{{ val.label }}</text>
              <text class="data-value">{{ val.value }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 无数据提示 -->
    <view class="no-data" v-if="!costList.length">
      <u-empty mode="data" icon="http://cdn.uviewui.com/uview/empty/data.png">
        <text>暂无成本数据</text>
      </u-empty>
    </view>
  </view>
</template>

<script>
import statisSearch from "@/components/statisSearch/statisSearch.vue";
import { costAnalysis } from '@/api/pages/statistical';
import statisTotal from "@/components/statisTotal/statisTotal.vue"

export default {
  components: {
    statisSearch,
    statisTotal
  },
  data() {
    return {
      costData: {},
      searchParams: {},
      totalInfo: {},
      costList: []
    };
  },
  onLoad() {
    this.getcostAnalysis();
  },
  methods: {
    handleSearchResult(params) {
      this.searchParams = params;
      this.getcostAnalysis();
    },
    getcostAnalysis() {
      costAnalysis(this.searchParams).then(res => {
        if (res.code === 200 && res.result) {
          this.costData = res.result;
          this.totalInfo = {
            totalCost: this.formatAmount(this.costData.finalCostAmount),
            costItems: [
              { name: '货款成本', value: this.costData.purchaseAmount, percentage: this.costData.purchaseAmountPercent, color: '#08BA7E' },
              { name: '服务费', value: this.costData.serviceAmount, percentage: this.costData.serviceAmountPercent, color: '#40CA8F' },
              { name: '运输费', value: this.costData.transportAmount, percentage: this.costData.transportAmountPercent, color: '#FFB800' },
              { name: '其他费用', value: this.costData.otherAmount, percentage: this.costData.otherAmountPercent, color: '#FF6B6B' }
            ]
          }
          this.formartListData(this.costData)
        } else {
          this.getDefaultTotalInfo()
        }
      }).catch(err => {
        uni.showToast({
          title: '获取数据失败',
          icon: 'none'
        });
      });
    },
    getDefaultTotalInfo() {
      return {
        totalCost: 0,
        costItems: [
          { name: '货款成本', value: 0, percentage: '0%', color: '#08BA7E' },
          { name: '服务费', value: 0, percentage: '0%', color: '#40CA8F' },
          { name: '运输费', value: 0, percentage: '0%', color: '#FFB800' },
          { name: '其他费用', value: 0, percentage: '0%', color: '#FF6B6B' }
        ]
      };
    },
    formatNumber(value) {
      if (!value) return '0';
      return parseFloat(value).toLocaleString();
    },
    formatAmount(value) {
      if (!value) return '0';
      return (parseFloat(value) / 10000).toFixed(2);
    },
    formartListData(value) {
      this.costList = [
        {
          label: '采购成本',
          type: 1,
          typeName: 'purchase',
          dataLabel: [
            {
              label: '养殖户',
              value: value.farmersNumber,
              bg: 'green-bg'
            },
            {
              label: '客户',
              value: value.customerNumber,
              bg: 'blue-bg'
            },
          ],
          dataValue: [
            {
              label: '采购总量',
              value: value.purchaseWeight,
              bg: 'orange-bg'
            },
            {
              label: '总费用',
              value: value.purchaseAmount,
              bg: 'purple-bg'
            },
          ]
        },
        {
          label: '服务费',
          type: 3,
          typeName: 'service',
          dataLabel: [
            {
              label: '牛经纪',
              value: value.cowBrokerNumber,
              bg: 'green-bg'
            },
            {
              label: '客户',
              value: value.customerNumber,
              bg: 'blue-bg'
            }
          ],
          dataValue: [
            {
              label: '采购数量',
              value: value.purchaseNumber,
              bg: 'orange-bg'
            },
            {
              label: '总费用',
              value: value.serviceAmount,
              bg: 'purple-bg'
            },
          ]
        },
        {
          label: '运输费',
          type: 2,
          typeName: 'transportation',
          dataLabel: [
            {
              label: '承运人',
              value: value.driverNumber,
              bg: 'green-bg'
            },
            {
              label: '客户',
              value: value.customerNumber,
              bg: 'blue-bg'
            }
          ],
          dataValue: [
            {
              label: '运输次数',
              value: value.transportNumber,
              bg: 'orange-bg'
            },
            {
              label: '总费用',
              value: value.transportAmount,
              bg: 'purple-bg'
            },
          ]
        },
        {
          label: '其他费用',
          type: 4,
          typeName: 'otherExpenses',
          dataLabel: [
            {
              label: '费用类型',
              value: '其他费用',
              bg: 'green-bg'
            },
            {
              label: '客户',
              value: value.customerNumber,
              bg: 'blue-bg'
            }
          ],
          dataValue: [
            {
              label: '总项数',
              value: value.otherCostNumber,
              bg: 'orange-bg'
            },
            {
              label: '总费用',
              value: value.otherAmount,
              bg: 'purple-bg'
            },
          ]
        },

      ]
      console.log(this.costList)
    },
    handleDetails(item){
      uni.navigateTo({
        url: `/myPackge1/pages/statistical/analysis/details/costDetail?settlementType=${item.type}&typeName=${item.typeName}`
      });
    }
  }

};
</script>
  
<style lang="scss" scoped>
.page-container {
  min-height: 100vh;
  background: #f5f6fa;
  padding-bottom: 30rpx;
  overflow: auto;
  padding-top: 30rpx;
}

.chart-container {
  background: #fff;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 20rpx;
  
  .chart-title {
    font-size: 28rpx;
    color: #333;
    font-weight: 500;
    margin-bottom: 20rpx;
    display: block;
  }
}

.line-chart {
  margin-bottom: 30rpx;
  height: 600rpx;
}

.pie-chart {
  height: 600rpx;
}

.cost-list {
  margin: 20rpx;
  
  .cost-item {
    background: #fff;
    border-radius: 16rpx;
    padding: 30rpx;
    margin-bottom: 20rpx;
    
    .item-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20rpx;
      
      .item-title {
        font-size: 32rpx;
        color: #333;
        font-weight: 500;
      }
    }
    
    .item-content {
      display: flex;
      flex-wrap: wrap;
      .data-row {
        display: flex;
        flex-wrap: wrap;
        margin-bottom: 20rpx;
        width: 50%;
        
        .data-col {
          // flex: 1;
          min-width: 50%;
          display: flex;
          // align-items: center;
          margin-bottom: 10rpx;
          flex-direction: column;
          
          .data-label {
            padding: 4rpx 16rpx;
            border-radius: 20rpx;
            font-size: 24rpx;
            margin-right: 10rpx;
          }
          
          .data-value {
            font-size: 28rpx;
            color: #333;
            margin-top: 20rpx;
            font-weight: bold;
            padding-left: 15rpx;
          }
        }
      }
    }
  }
}

.green-bg {
  background-color: #E9FFF4;
  color: #6EC096;
}

.blue-bg {
  background-color: #FDF3E8;
  color: #DD9544;
}

.orange-bg {
  background-color: #E1EEFF;
  color: #7999D5;
}

.purple-bg {
  background-color: #FFEEE8;
  color: #CF5F45;
}

.no-data {
  text-align: center;
  padding: 100rpx 0;
  background: #fff;
  margin: 20rpx;
  border-radius: 16rpx;
}
</style>
  