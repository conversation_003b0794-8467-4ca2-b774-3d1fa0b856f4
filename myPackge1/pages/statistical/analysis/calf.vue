<template>
    <view class="sale-analysis">
      <!-- 搜索组件 -->
      <statis-search @search="handleSearchResult" placeholder="请输入客户名称、养殖户、牛经纪"></statis-search>
      
      <!-- 总览卡片 -->
      <view class="sales-overview">
        <view class="sales-total">
          <text class="total-number">{{ cowData.satisfaction || 0 }}%</text>
          <text class="total-label">满意度</text>
        </view>
        <view class="sales-details">
          <view class="detail-item">
            <view class="detail-label">牛源地</view>
            <view class="detail-value">{{ cowData.cowSourceNumber || 0 }}家</view>
          </view>
          <view class="detail-item">
            <view class="detail-label">交易量</view>
            <view class="detail-value">{{ cowData.salesNumber || 0 }}头</view>
          </view>
          <view class="detail-item">
            <view class="detail-label">成交量</view>
            <view class="detail-value">{{ cowData.salesWeightUnit || '0吨' }}</view>
          </view>
          <view class="detail-item">
            <view class="detail-label">累计亏损重量</view>
            <view class="detail-value">{{ cowData.purchaseSaleDiffWeightUnit || '0吨' }}</view>
          </view>
        </view>
      </view>
      
      <!-- 牛源列表 -->
      <view class="source-list">
        <view class="source-item" v-for="(item, index) in cowData.cowSourceList" :key="index">
          <view class="source-title">{{ item.provinceName }}</view>
          
          <!-- 牛源信息卡片组 -->
          <view class="card-grid">
            <!-- 客户卡片 -->
            <view class="info-card blue-card" @click="handleDetails(item, 'customer')">
              <view class="card-title">客户</view>
              <view class="card-value">{{ item.customerNumber || 0 }}个</view>
            </view>
            
            <!-- 交易频次卡片 -->
            <view class="info-card green-card">
              <view class="card-title">交易频次</view>
              <view class="card-value">{{ item.orderNumber || 0 }}次</view>
            </view>
            
            <!-- 进销差价卡片 -->
            <view class="info-card green-card">
              <view class="card-title">进销差价</view>
              <view class="card-value">
                {{ item.profitRange }}
              </view>
            </view>
            
            <!-- 亏损重量卡片 -->
            <view class="info-card green-card">
              <view class="card-title">亏损重量</view>
              <view class="card-value">{{ item.purchaseSaleDiffWeight }}</view>
            </view>
            
            <!-- 牛经纪卡片 -->
            <view class="info-card blue-card" @click="handleDetails(item, 'broker')">
              <view class="card-title">牛经纪</view>
              <view class="card-value">{{ item.cowBrokerNumber || 0 }}个</view>
            </view>
            
            <!-- 成交量卡片 -->
            <view class="info-card green-card">
              <view class="card-title">成交量</view>
              <view class="card-value">{{ item.brokerMatchedNumber || 0 }}头</view>
            </view>
            
            <!-- 服务费卡片 -->
            <view class="info-card green-card">
              <view class="card-title">服务费</view>
              <view class="card-value">{{ item.serviceAmount }}元/头</view>
            </view>
            
            <!-- 满意度卡片 -->
            <view class="info-card green-card">
              <view class="card-title">满意度</view>
              <view class="card-value">{{ item.satisfaction || 0 }}%</view>
            </view>
            
            <!-- 养殖户卡片 -->
            <view class="info-card blue-card" @click="handleDetails(item, 'farmer')">
              <view class="card-title">养殖户</view>
              <view class="card-value">{{ item.farmersNumber || 0 }}个</view>
            </view>
            
            <!-- 交易量卡片 -->
            <view class="info-card green-card">
              <view class="card-title">交易量</view>
              <view class="card-value">{{ item.farmerSalesNumber || 0 }}头</view>
            </view>
            
            <!-- 单价范围卡片 -->
            <view class="info-card green-card">
              <view class="card-title">单价范围</view>
              <view class="card-value">{{ item.salesPriceRange }}</view>
            </view>
            
            <!-- 牛只均重卡片 -->
            <view class="info-card green-card">
              <view class="card-title">牛只均重</view>
              <view class="card-value">{{ item.farmersSalsesAverageWeight }}</view>
            </view>
            
            <!-- 承运人卡片 -->
            <view class="info-card blue-card" @click="handleDetails(item, 'driver')">
              <view class="card-title">承运人</view>
              <view class="card-value">{{ item.driverNumber || 0 }}个</view>
            </view>
            
            <!-- 运输次数卡片 -->
            <view class="info-card green-card">
              <view class="card-title">运输次数</view>
              <view class="card-value">{{ item.orderNumber || 0 }}次</view>
            </view>
            
            <!-- 准时达卡片 -->
            <view class="info-card green-card">
              <view class="card-title">准时达</view>
              <view class="card-value">{{ item.ontimeRate }}</view>
            </view>
            
            <!-- 损耗重量卡片 -->
            <view class="info-card green-card">
              <view class="card-title">损耗重量</view>
              <view class="card-value">{{ item.transportLossWeight }}</view>
            </view>
          </view>
        </view>
        <!-- 无数据提示 -->
        <view class="no-data" v-if="!cowData.cowSourceList || cowData.cowSourceList.length === 0">
          <u-empty mode="data" icon="http://cdn.uviewui.com/uview/empty/data.png">
            <text>暂无牛源数据</text>
          </u-empty>
        </view>
      </view>
    </view>
  </template>
  
  <script>
  import statisSearch from "@/components/statisSearch/statisSearch.vue";
  import { cowSourceAnalysis } from '@/api/pages/statistical';
  
  export default {
    components: {
      statisSearch
    },
    data() {
      return {
        cowData: {
          cowSourceList: []
        },
        searchParams: {},
      };
    },
    onLoad() {
      this.getcowSourceAnalysis();
    },
    methods: {
      handleSearchResult(params) {
        console.log('params', params);
        this.searchParams = params;
        this.getcowSourceAnalysis();
      },
      getcowSourceAnalysis() {
        cowSourceAnalysis(this.searchParams).then(res => {
          if (res.code === 200 && res.result) {
            this.cowData = res.result;
          }
        }).catch(err => {
          console.error('获取分析数据失败', err);
          uni.showToast({
            title: '获取数据失败',
            icon: 'none'
          });
        });
      },
      formatNumber(value) {
        if (!value) return '0';
        return parseFloat(value).toLocaleString();
      },
      formatAmount(value) {
        if (!value) return '0';
        return parseFloat(value).toFixed(2);
      },
      formatPriceRange(range) {
        if (!range) return '0~0元/kg';
        return range.replace('元/kg', '');
      },
      formatWeight(weight) {
        if (!weight) return '0kg';
        const weightNum = parseFloat(weight);
        if (weightNum >= 1000) {
          return (weightNum / 1000).toFixed(2) + 'kg';
        }
        return weightNum.toFixed(2) + 'kg';
      },
      formatAvgWeight(weight) {
        if (!weight) return '0kg/头';
        return parseFloat(weight).toFixed(2) + 'kg/头';
      },
      formatPercentage(value) {
        if (!value) return '0%';
        return (parseFloat(value) * 100).toFixed(0) + '%';
      },
      handleDetails(item, type) {
        uni.navigateTo({
          url: `/myPackge1/pages/statistical/analysis/details/calfDetail?provinceId=${item.provinceId}&sourceType=${type}`
        });
      }
    }
  };
  </script>
  
  <style lang="scss" scoped>
  .sale-analysis {
    min-height: 100vh;
    background-color: #f5f7fa;
    padding-bottom: 30rpx;
    overflow: auto;
    padding-top: 30rpx;
  }
  
  .sales-overview {
    background-color: #fff;
    margin: 20rpx;
    border-radius: 20rpx;
    padding: 30rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  }
  
  .sales-total {
    display: flex;
    flex-direction: column;
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 20rpx;
    margin-bottom: 20rpx;
    
    .total-number {
      font-size: 80rpx;
      font-weight: bold;
      color: #08BA7E;
    }
    
    .total-label {
      font-size: 28rpx;
      color: #666;
      margin-top: -10rpx;
    }
  }
  
  .sales-details {
    display: flex;
    justify-content: space-between;
    
    .detail-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      
      .detail-label {
        font-size: 28rpx;
        color: #666;
        margin-bottom: 10rpx;
      }
      
      .detail-value {
        font-size: 32rpx;
        color: #333;
        font-weight: bold;
      }
    }
  }
  
  .source-list {
    margin: 0 20rpx;
  }
  
  .source-item {
    background-color: #fff;
    border-radius: 20rpx;
    padding: 30rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  }
  
  .source-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 20rpx;
    padding-bottom: 15rpx;
    border-bottom: 1px solid #f0f0f0;
  }
  
  .card-grid {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -8rpx;
  }
  
  .info-card {
    width: calc(25% - 16rpx);
    margin: 8rpx;
    background-color: #fff;
    border-radius: 12rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-2rpx);
      box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
    }
    
    .card-title {
      height: 60rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      color: #fff;
      font-size: 28rpx;
      border-radius: 12rpx 12rpx 0 0;
      font-weight: 500;
    }
    
    .card-value {
      text-align: center;
      font-size: 24rpx;
      color: #333;
      font-weight: 500;
      padding: 12rpx 8rpx;
      word-break: break-all;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      min-height: 72rpx;
    }
  }
  
  .blue-card {
    .card-title {
      background: linear-gradient(135deg, #1E90FF, #4169E1);
    }
    
    &:hover {
      .card-title {
        background: linear-gradient(135deg, #4169E1, #1E90FF);
      }
    }
  }
  
  .green-card {
    .card-title {
      background: linear-gradient(135deg, #40CA8F, #4CAF50);
    }
    
    &:hover {
      .card-title {
        background: linear-gradient(135deg, #4CAF50, #40CA8F);
      }
    }
  }
  
  .no-data {
    background-color: #fff;
    border-radius: 20rpx;
    padding: 50rpx 0;
    text-align: center;
    
    text {
      font-size: 28rpx;
      color: #999;
      margin-top: 20rpx;
    }
  }
  </style>