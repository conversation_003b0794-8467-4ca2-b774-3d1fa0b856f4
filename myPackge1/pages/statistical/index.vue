<template>
    <view class="page-container">
		<CustomNavbar  :bgColor="'#08BA7E'" :titleColor="'#FFFFFF'" />
        <view class="header-bg">
            <!-- 页面标题区域 -->
            <view class="header-title-section">
                <view class="title-decoration">
                    <!-- <image class="decoration-icon" src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/statistical/sousuo.png" mode="aspectFit"></image> -->
                </view>
            </view>
            <statis-search @search="handleSearchResult" class="search-component"></statis-search>
        </view>
        
        <scroll-view class="content-scroll" scroll-y>
            <section>
                <view class="list-section" v-for="(section, index) in sections" :key="index">
                    <view class="middle">
                        <view class="top-explain" @click.stop="detail(section)">
                            <view class="title">
                                <view class="icon-container">
                                    <image v-if="section.title === '销售概览'" class="section-icon" src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/statistical/xiaoshou.png"></image>
                                    <image v-else-if="section.title === '利润分析'" class="section-icon" src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/statistical/money_icon.png"></image>
                                    <image v-else-if="section.title === '价格分析'" class="section-icon" src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/statistical/qianbao.png"></image>
                                    <image v-else-if="section.title === '成本分析'" class="section-icon" src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/statistical/pie_icon.png"></image>
                                    <image v-else-if="section.title === '牛源分析'" class="section-icon" src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/statistical/cows.png"></image>
                                    <image v-else class="section-icon" src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/statistical/yunshu.png"></image>
                                </view>
                                {{ section.title }}
                                <!-- 价格分析的图例显示在标题后面 -->
                                <view v-if="section.title === '价格分析'" class="price-legend">
                                    <view class="legend-item">
                                        <view class="legend-dot purchase-dot"></view>
                                        <text class="legend-text">采购价</text>
                                    </view>
                                    <view class="legend-item">
                                        <view class="legend-dot sale-dot"></view>
                                        <text class="legend-text">销售价</text>
                                    </view>
                                </view>
                            </view>
                            <view>
                                <u-icon name="arrow-right" color="#333333" size="22"></u-icon>
                            </view>
                        </view>
                        <view class="list-item" :class="{'table-content': section.type === 'table', 'chart-content': section.type !== 'table'}">
                            <template v-if="section.type === 'table'">
                                <template v-if="section.showType == 1">
                                    <u-grid :border="false" col="3">
                                        <u-grid-item v-for="(listItem, listIndex) in section.tdData" :key="listIndex">
                                            <text class="grid-text">{{ listItem.label }}&nbsp;&nbsp;</text>
                                            <text class="grid-text" :style="{ color: listItem.color }">{{ listItem.value
                                            }} <text class="unit-text">{{ listItem.unit }}</text></text>
                                        </u-grid-item>
                                    </u-grid>
                                </template>
                                <template v-else>
                                    <view>
                                        <view class="sale-box">
                                            <u-grid :border="false" col="3">
                                                <u-grid-item v-for="(listItem, listIndex) in section.tdData"
                                                    :key="listIndex">
                                                    <text class="grid-text">{{ listItem.label }}&nbsp;</text>
                                                    <text class="grid-text">{{ listItem.value }} <text class="unit-text">{{
                                                        listItem.unit }}</text></text>
                                                </u-grid-item>
                                            </u-grid>
                                        </view>
                                    </view>
                                </template>
                            </template>
                            <template v-else-if="section.type === 'line-chart'">
                                <view class="chart-box chart-container">
                                    <qiun-data-charts  
                                      loadingType="0" 
                                      type="line" 
                                      :opts="optsLine"
                                      :chartData="lineData" 
                                    />
                                </view>
                            </template>
                            <template v-else-if="section.type === 'pie-chart'">
                                <view class="chart-box chart-container">
                                    <qiun-data-charts
                                      loadingType="0"
                                      type="ring"
                                      :opts="opts"
                                      :chartData="chartData"
                                    />
                                </view>
                            </template>
                        </view>
                    </view>
                </view>
            </section>
        </scroll-view>
    </view>
</template>
<script>
import uniNavBar from "@/components/uni-nav-bar/uni-nav-bar"
import cusTable from "@/components/cusTable/index.vue"
import statisSearch from "@/components/statisSearch/statisSearch.vue"
import { singleCount, priceAnalysis } from '@/api/pages/statistical'
import CustomNavbar from '@/components/uni-custom-navbar/CustomNavbar.vue'


export default {
    components: {
        uniNavBar,
        cusTable,
        statisSearch,
        CustomNavbar
    },
    data() {
        return {
            scrollTop: 0,
            isIphonex: getApp().globalData.systemInfo.isIphonex,
            searchParams: {
                customerName: '',
                startTime: '',
                endTime: ''
            },
            sections: [
                {
                    title: '销售概览',
                    type: 'table',
                    showType: 1,
                    tdData: [
                        { label: '客户', value: '', unit: '家', key: 'customerNumber' },
                        { label: '累计交易量', value: '', unit: '头', key: 'salesNumber' },
                        { label: '累计成交量', value: '', unit: 'kg', key: 'salesWeight' },
                        { label: '销售收入', value: '', unit: '万元', color: '#08BA7E', key: 'salesAmount' }
                    ]
                },
                {
                    title: '利润分析',
                    type: 'table',
                    showType: 1,
                    tdData: [
                        { label: '销售收入', value: '', unit: '万元', key: 'salesAmount' },
                        { label: '费用支出', value: '', unit: '万元', key: 'purchaseAmount' },
                        { label: '利润', value: '', unit: '万元', key: 'profit' },
                        { label: '利润率', value: '', unit: '%', color: '#08BA7E', key: 'profitRate' }
                    ]
                },
                {
                    title: '价格分析',
                    type: 'line-chart',
                    tdData: []
                },
                {
                    title: '成本分析',
                    type: 'pie-chart',
                    tdData: []
                },
                {
                    title: '牛源分析',
                    type: 'table',
                    showType: 2,
                    tdData: [
                        { label: '牛源地', value: '', unit: '处', key: 'cowSourceNumber' },
                        { label: '经纪人', value: '', unit: '个', key: 'cowBrokerNumber' },
                        { label: '养殖户', value: '', unit: '个', key: 'farmersNumber' },
                        { label: '累计交易量', value: '', unit: '头', key: 'purchaseNumber' },
                        { label: '满意度', value: '', unit: '%', key: 'satisfaction' },
                        { label: '累计亏损重量', value: '', unit: 'kg', key: 'transportLossWeight' }
                    ]
                },
                {
                    title: '运输分析',
                    type: 'table',
                    showType: 2,
                    tdData: [
                        { label: '承运人', value: '', unit: '个', key: 'driverNumber' },
                        { label: '运输车次', value: '', unit: '次', key: 'orderNumber' },
                        { label: '准时达', value: '', unit: '%', key: 'onTimeOrderNumber' },
                        { label: '累计数量差', value: '', unit: '', color: '#08BA7E', key: 'purchaseSaleDiffNumber' },
                        { label: '累计损耗重量', value: '', unit: 'kg', color: '#08BA7E', key: 'purchaseSaleDiffWeight' },
                        // { label: '结算', value: '', unit: '万元', color: '#08BA7E', key: '结算' },
                    ]
                },
            ],
            optsLine: {
                color: ["#1890FF", "#91CB74", "#FAC858", "#EE6666", "#73C0DE", "#3CA272", "#FC8452", "#9A60B4", "#ea7ccc"],
                padding: [15, 10, 0, 15],
                enableScroll: false,
                legend: {
                    show: false, // 隐藏默认图例，使用自定义图例
                },
                xAxis: {
                    disableGrid: true,
                    show: true,
                    rotateLabel: true,
                    rotateAngle: 45,
                    marginTop: 10,
                    lineColor: "#f5f5f5",
                },
                yAxis: {
                    disabled: false,
                    disableGrid: false,
                    gridType: "solid",
                    dashLength: 1,
                    show: true,
                },
                extra: {
                    line: {
                        type: "straight",
                        width: 3, // 增加线条宽度
                        activeType: "hollow",
                        show: true,
                        activeOpacity: 1,
                        // 添加数据点样式
                        dataPointShape: "circle",
                        dataPointRadius: 4
                    },
                    tooltip: {
                        showBox: true,
                        showArrow: true,
                        showCategory: true,
                        borderWidth: 0,
                        borderRadius: 5,
                        borderColor: '#000',
                        backgroundColor: 'rgba(0,0,0,0.7)',
                        fontColor: '#fff',
                        fontSize: 13
                    }
                }
            },
            opts: {
                rotate: false,
                rotateLock: false,
                color: ["#5BEABA", "#FFD60A", "#F5C842", "#4F76F9"], // 调整颜色顺序：采购费用(绿)、服务费用(橙)、运输费用(黄)、其他费用(蓝)
                padding: [20, 20, 20, 20],
                dataLabel: true,
                enableScroll: false,
                legend: {
                    show: true,
                    position: "bottom",
                    lineHeight: 25,
                    type: "circle", // 圆形图例
                    fontSize: 14,
                    fontColor: "#666666",
                    margin: 10
                },
                title: {
                    name: "",
                    fontSize: 15,
                    color: "#666666",
                },
                subtitle: {
                    name: "",
                    fontSize: 20,
                    color: "#7cb5ec"
                },
                extra: {
                    ring: {
                        ringWidth: 40, // 减小环形宽度，确保中间有明显空心
                        activeOpacity: 0.5,
                        activeRadius: 10,
                        offsetAngle: 0,
                        labelWidth: 15,
                        border: true,
                        borderWidth: 3,
                        borderColor: "#FFFFFF",
                    },
                    label: {
                        show: true, // 显示标签
                        fontSize: 14,
                        color: '#000',
                        formatter: (item) => `${item.data}%` // 只显示百分比
                    }
                }
            },
            lineData: {
                categories: [],
                series: []
            },
            chartData: {}
        }
    },
    onLoad() {
        this.getSingleCount();
        this.getPriceAnalysis();
    },
    methods: {
        handleSearchResult(params) {
            console.log(params);
            this.searchParams = params;
            this.getSingleCount(params);
            this.getPriceAnalysis(params);
        },
        getSingleCount(params = {}) {
            singleCount(params).then(res => {
                if (res.code === 200) {
                    const safeNum = (v) => isNaN(v) ? 0 : Number(v);

                    // 计算总值用于百分比计算
                    const purchaseAmount = safeNum(res.result.purchaseAmount);
                    const serviceAmount = safeNum(res.result.serviceAmount);
                    const transportAmount = safeNum(res.result.transportAmount);
                    const otherAmount = safeNum(res.result.otherAmount);
                    const total = purchaseAmount + serviceAmount + transportAmount + otherAmount;

                    // 计算百分比
                    const calculatePercentage = (value) => {
                        return total > 0 ? Math.round((value / total) * 100) : 0;
                    };

                    this.chartData = {
                        series: [
                            {
                                data: [
                                    {
                                        name: '采购费用',
                                        value: purchaseAmount,
                                        data: calculatePercentage(purchaseAmount),
                                        varietiesCount: res.result.purchaseAmountUnit
                                    },
                                    {
                                        name: '服务费用',
                                        value: serviceAmount,
                                        data: calculatePercentage(serviceAmount),
                                        varietiesCount: res.result.serviceAmountUnit
                                    },
                                    {
                                        name: '运输费用',
                                        value: transportAmount,
                                        data: calculatePercentage(transportAmount),
                                        varietiesCount: res.result.transportAmountUnit
                                    },
                                    {
                                        name: '其他费用',
                                        value: otherAmount,
                                        data: calculatePercentage(otherAmount),
                                        varietiesCount: res.result.otherAmountUnit
                                    }
                                ]
                            }
                        ]
                    };
                    console.log(this.chartData)
                    this.updateSections(res.result);
                }
            });
        },
        getPriceAnalysis(params = {}) {
            priceAnalysis(params).then(res => {
                if (res.code === 200) {
                    this.lineData = JSON.parse(JSON.stringify(this.formartLineChatData(res.result)));
                    console.log(this.lineData)
                }
            });
        },
        updateSections(result) {
            this.sections
                .filter(section => section.type === 'table')
                .forEach(({ tdData }) => {
                    tdData.forEach(cell => {
                        const { unit, key } = cell;
                        cell.value = unit === '万元'
                            ? this.formatAmount(result[key])
                            : result[key] ?? '--'; 
                    });
                });
        },
        formartLineChatData(val) {
            const FIELD_MAP = [
                { key: 'yaxis1', name: '采购价', parser: Number },
                { key: 'yaxis2', name: '销售价', parser: Number }
            ];
            return {
                categories: val.map(({ xaxis }) => xaxis),
                series: FIELD_MAP.map(({ name, key, parser }) => ({
                    name,
                    data: val.map(item => parser(item[key]))
                }))
            }
        },
        detail(item) {
            console.log(item);
            const PAGE_MAPPING = {
                '销售概览': '/myPackge1/pages/statistical/analysis/sale',
                '利润分析': '/myPackge1/pages/statistical/analysis/profit',
                '价格分析': '/myPackge1/pages/statistical/analysis/price',
                '成本分析': '/myPackge1/pages/statistical/analysis/cost',
                '牛源分析': '/myPackge1/pages/statistical/analysis/calf',
                '运输分析': '/myPackge1/pages/statistical/analysis/transport'
            };
            const targetPath = PAGE_MAPPING[item?.title];
            console.log(targetPath);
            if(targetPath){
                uni.navigateTo({
                    url: targetPath
                })
            }
        },
        formatAmount(value) {
            if (!value) return '0';
            return (parseFloat(value) / 10000).toFixed(2);
        },
    }
}
</script>
<style lang="scss" scoped>
.page-container {
    height: 100vh;
    display: flex;
    flex-direction: column;
    // background: linear-gradient(to bottom, #f8fffe 0%, #f5f6fa 100%);
}

.header-bg {
    // background: linear-gradient(135deg, #08BA7E 0%, #4DD0A7 50%, #7DE3C4 100%);
    height: 720rpx;
    position: relative;
    padding-top: 30rpx;
    overflow: hidden;
}

.header-bg::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url(https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/statistical/statistical_bg.png) no-repeat 100% 100%;
    background-size: 100% 100%;
    // z-index: 1;
}

.header-title-section {
    position: relative;
    z-index: 2;
    padding: 40rpx 60rpx 20rpx;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.main-title {
    color: #FFFFFF;
    font-size: 48rpx;
    font-weight: bold;
    margin-bottom: 10rpx;
    text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.sub-title {
    color: rgba(255, 255, 255, 0.9);
    font-size: 28rpx;
    font-weight: 400;
    margin-bottom: 20rpx;
}

.title-decoration {
    position: absolute;
    right: 60rpx;
    top: 50%;
    transform: translateY(-50%);
}

.decoration-icon {
    width: 120rpx;
    height: 120rpx;
}

.search-component {
    position: relative;
    z-index: 2;
    margin-top: 20rpx;
    position: absolute;
    bottom: 180rpx;
    right: 0;
    left: 0;
}

.content-scroll {
    flex: 1;
    height: 0;
    // margin-top: -150rpx;
    margin-top: -205rpx;
    padding-bottom: 60rpx;
}

.list-section {
    background: #fff;
    margin: 30rpx;
    border-radius: 20rpx;
    padding: 30rpx 0rpx;
    .middle {
        .top-explain {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15rpx;
            padding-left: 35rpx;
            padding-right: 25rpx;
            // padding-bottom: 15rpx;
            // border-bottom: 1px solid #f5f5f5;

            .title {
                font-size: 32rpx;
                color: #222;
                font-weight: bold;
                display: flex;
                align-items: center;
                font-style: normal;
                font-family: SourceHanSansSC, SourceHanSansSC;

                .icon-container {
                    margin-right: 15rpx;
                }

                .section-icon {
                    width: 40rpx;
                    height: 40rpx;
                    border-radius: 10rpx;
                    padding: 8rpx;
                    vertical-align: middle;
                }

                .price-legend {
                    display: flex;
                    align-items: center;
                    margin-left: 30rpx;

                    .legend-item {
                        display: flex;
                        align-items: center;
                        margin-right: 30rpx;

                        .legend-dot {
                            width: 16rpx;
                            height: 16rpx;
                            border-radius: 50%;
                            margin-right: 8rpx;
                        }

                        .purchase-dot {
                            background-color: #1890FF;
                        }

                        .sale-dot {
                            background-color: #91CB74;
                        }

                        .legend-text {
                            font-size: 26rpx;
                            color: #666;
                            font-weight: normal;
                        }
                    }
                }
            }
        }

        .list-item {
            // 表格内容应用68rpx左边距
            &.table-content {
                padding-left: 68rpx;
                padding-right: 25rpx;
            }

            // 图表内容保持原来的25rpx边距
            &.chart-content {
                padding-left: 25rpx;
                padding-right: 25rpx;
            }
        }
    }
}

.chart-container {
  position: relative;
  z-index: 1;
  margin-top: 10rpx;
}

.chart-box {
    height: 600rpx;
    width: 100%;
    z-index: 1;
    background-color: #fff;
    border-radius: 16rpx;
    padding: 20rpx;
    margin-top: 10rpx;
    // border: 1px solid rgba(8, 186, 126, 0.05);
}

/deep/ .uni-charts-canvas {
  z-index: 1 !important;
}

.list-item-btn {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-top: 20rpx;

    .item {
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 28rpx;
        font-family: PingFang SC-Medium, PingFang SC;
        font-weight: 400;
        color: #40CA8F;
        padding-left: 10rpx;
        line-height: 50rpx;
        margin-left: 30rpx;

        img {
            width: 45rpx;
            height: 45rpx;
            margin-right: 10rpx;
        }
    }
}

.container-footer {
    width: 100%;
    position: fixed;
    bottom: 0;
    background-color: white;
    color: white;
}

.bottomInfo {
    width: 100%;
    background: #FFFFFF;
    box-shadow: 0rpx -2rpx 10rpx 0rpx #F4F6FF;
    font-size: 28rpx;
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    color: #333333;
    box-sizing: border-box;
    padding: 21rpx 30rpx 30rpx;

    .total {
        padding: 0rpx 0 15rpx;
    }

    .bottom {
        width: 100%;
        font-size: 28rpx;
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        color: #333333;
        display: flex;
        justify-content: space-between;
    }
}

.search {
    width: 100% !important;
}

.searchs {
    padding: 0 30rpx 30rpx 30rpx !important;
}

.list-section {
    padding: 30rpx 0rpx !important;

    .middle {
        .top-explain {
            padding-left: 35rpx !important;
            padding-right: 25rpx !important;
        }

        .list-item {
            &.table-content {
                padding-left: 68rpx !important;
                padding-right: 25rpx !important;
            }

            &.chart-content {
                padding-left: 25rpx !important;
                padding-right: 25rpx !important;
            }
        }
    }

    margin-bottom: 30rpx;

    .sale-box {
        width: 100%;
    }
}

.main-box {
    overflow: auto;
    padding-bottom: 50rpx;
}

.grid-text {
    font-size: 26rpx;
    padding: 8rpx 0 15rpx 0rpx;
    display: block;
    text-align: center;
    line-height: 1.4;
    /* #ifndef APP-PLUS */
    box-sizing: border-box;
    /* #endif */
    font-family: SourceHanSansSC, SourceHanSansSC;
    color: #999999;
    font-weight: 400;
}

/deep/ .u-grid-item-box {
    padding: 15rpx 10rpx !important;
    text-align: center;
    align-items: self-start !important;

    .grid-text {
        font-size: 28rpx !important;
        display: block;
        line-height: 1.4;
    }

    text:nth-child(1) {
        color: #888;
        font-size: 26rpx !important;
        margin-bottom: 8rpx;
    }

    text:nth-child(2) {
        color: #222;
        font-weight: 600;
        font-size: 32rpx !important;
        line-height: 1.2;
    }

    /* 特殊颜色的数值 */
    text:nth-child(2)[style*="color: rgb(8, 186, 126)"],
    text:nth-child(2)[style*="color: #08BA7E"] {
        color: #27AC6B !important;
        font-weight: 500 !important;
        font-size: 64rpx !important;
    }
}

/deep/ .u-icon {
    margin-left: 10rpx;
}

.unit-text {
    font-size: 22rpx !important;
    color: #999;
    margin-left: 4rpx;
    font-weight: normal;
    // opacity: 0.8;
}

.statistics {
    height: 400rpx;
    background-color: #fff;
}

/deep/ .u-popup, 
/deep/ .u-mask, 
/deep/ .u-calendar,
/deep/ .u-picker,
/deep/ .u-select,
/deep/ .u-drawer {
  z-index: 9999 !important;
}

/deep/ canvas,
/deep/ .qiun-title,
/deep/ .uni-canvas {
  z-index: 1 !important;
}
</style>