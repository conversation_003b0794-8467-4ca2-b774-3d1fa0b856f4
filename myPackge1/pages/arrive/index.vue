<template>
	<view>
        <scroll-view scroll-y :scroll-with-animation="true" class="main">
            <view class="container">
                <u-form :model="form" ref="uForm" :error-type="errorType" label-width="auto" label-position="top" :label-style="labelStyle">
                    <u-form-item label="抵达数量（头）" required="true" prop="purchaseNumber">
                        <u-input  :custom-style="customStyle" :placeholder-style="placeholderStyle" v-model="form.purchaseNumber" type="number" placeholder="请输入抵达数量" @input="changePurchaseNumber"/>
                    </u-form-item>
                    <u-form-item label="运转差额（头）" required="true">
                        <u-input  :custom-style="customStyle" :placeholder-style="placeholderStyle" v-model="differenceNum" type="number" placeholder="自动计算，发车数量-抵达数量"/>
                    </u-form-item>
					<u-form-item label="预计抵达时间" required="true" right-icon="arrow-right">
                        <text>{{ dataInfo.deliveryEndTime }}</text>
                    </u-form-item>
					<u-form-item label="实际抵达时间" required="true" prop="deliveryEndTime" right-icon="arrow-right">
						<u-input  :custom-style="customStyle" :placeholder-style="placeholderStyle" v-model="form.deliveryEndTime" placeholder="请选择实际抵达时间" disabled @click="showTime = true"/>
					</u-form-item>
					<u-form-item label="延迟时长（小时）" required="true">
						{{ differenceTime }}
					</u-form-item>
                    <u-form-item label="抵达过磅视频" required="true" prop="grossVideo" :border-bottom="false" style="overflow: visible">
						<view class="uploadImage">
							<view class="itemAlready" v-show="form.grossVideo.length" v-for="(item, index) in form.grossVideo" :key="index">
								<video mode="scaleToFill" :src="item" @click="previewImage(item)" />
								<view class="closeIcon" @click="deleteFile('grossVideo',index)"></view>
							</view>
							<view class="item" v-if="form.grossVideo.length < 6">
								<view class="uploadIcon" @click="unloadVideo('grossVideo')">点击上传</view>
							</view>
						</view>
					</u-form-item>
                    <u-form-item label="车头带牌照片" required="true" prop="carHeadUrl" :border-bottom="false" style="overflow: visible">
						<view class="uploadImage">
							<view class="itemAlready" v-show="form.carHeadUrl.length" v-for="(item, index) in form.carHeadUrl" :key="index">
								<image mode="scaleToFill" :src="item" @click="previewImage(item)" />
								<view class="closeIcon" @click="deleteFile(index)"></view>
							</view>
							<view class="item" v-if="form.carHeadUrl.length < 6">
								<view class="uploadIcon" @click="unloadImage('carHeadUrl')">点击上传</view>
							</view>
						</view>
					</u-form-item>
                    <u-form-item label="车尾带牌照片" required="true" prop="carTailUrl" :border-bottom="false" style="overflow: visible">
						<view class="uploadImage">
							<view class="itemAlready" v-show="form.carTailUrl.length" v-for="(item, index) in form.carTailUrl" :key="index">
								<image mode="scaleToFill" :src="item" @click="previewImage(item)" />
								<view class="closeIcon" @click="deleteFile(index)"></view>
							</view>
							<view class="item" v-if="form.carTailUrl.length < 6">
								<view class="uploadIcon" @click="unloadImage('carTailUrl')">点击上传</view>
							</view>
						</view>
					</u-form-item>
                    <u-form-item label="抵达过磅照片" required="true" prop="grossWeightingUrl" :border-bottom="false" style="overflow: visible">
						<view class="uploadImage">
							<view class="itemAlready" v-if="form.grossWeightingUrl.length">
								<image mode="scaleToFill" :src="form.grossWeightingUrl[0]" @click="previewImage(form.grossWeightingUrl[0])" />
								<view class="closeIcon" @click="deleteFile('grossWeightingUrl', index)"></view>
							</view>
							<view class="item" v-if="form.grossWeightingUrl.length < 1">
								<view class="uploadIcon" @click="unloadImage('grossWeightingUrl')">点击上传</view>
							</view>
						</view>
					</u-form-item>
                    <u-form-item label="抵达重量（kg）" required="true" prop="grossWeight">
                        <u-input  :custom-style="customStyle" :placeholder-style="placeholderStyle" v-model="form.grossWeight" type="number" placeholder="请输入抵达重量" @input="changeGrossWeight"/>
                    </u-form-item>
                    <u-form-item label="卸车视频" required="true" prop="tareVideo" :border-bottom="false" style="overflow: visible">
						<view class="uploadImage">
							<view class="itemAlready" v-show="form.tareVideo.length" v-for="(item, index) in form.tareVideo" :key="index">
								<video mode="scaleToFill" :src="item"  @click="previewImage(item)" />
								<view class="closeIcon" @click="deleteFile('tareVideo',index)"></view>
							</view>
							<view class="item" v-if="form.tareVideo.length < 6">
								<view class="uploadIcon" @click="unloadVideo('tareVideo')">点击上传</view>
							</view>
						</view>
					</u-form-item>
                    <u-form-item label="空载照片" required="true" prop="tareWeightingUrl" :border-bottom="false" style="overflow: visible">
						<view class="uploadImage">
							<view class="itemAlready" v-show="form.tareWeightingUrl.length" v-for="(item, index) in form.tareWeightingUrl" :key="index">
								<image mode="scaleToFill" :src="item" @click="previewImage(item)" />
								<view class="closeIcon" @click="deleteFile(index)"></view>
							</view>
							<view class="item" v-if="form.tareWeightingUrl.length < 6">
								<view class="uploadIcon" @click="unloadImage('tareWeightingUrl')">点击上传</view>
							</view>
						</view>
					</u-form-item>
                    <u-form-item label="空车重量（kg）" required="true" prop="tareWeight">
                        <u-input  :custom-style="customStyle" :placeholder-style="placeholderStyle" v-model="form.tareWeight" type="number" placeholder="请输入空车重量" @input="changeFinalWeight"/>
                    </u-form-item>
                    <u-form-item label="抵达净重量（kg）" required="true">
                        <u-input  :custom-style="customStyle" :placeholder-style="placeholderStyle" v-model="finalWeight" disabled placeholder="自动计算，毛重重量-空车重量"/>
                    </u-form-item>
                    <u-form-item label="空车重量差（kg）" required="true">
                        <u-input  :custom-style="customStyle" :placeholder-style="placeholderStyle" v-model="tareWeight" disabled placeholder="自动计算，抵达空车重量-发车空车重量"/>
                    </u-form-item>
                    <u-form-item label="运转重量差（kg）" required="true">
                        <u-input  :custom-style="customStyle" :placeholder-style="placeholderStyle" v-model="differenceWeight" placeholder="自动计算，发车净重量 - 抵达净重量" disabled/>
                    </u-form-item>
                    <u-form-item label="异常说明" required="true">
                        <u-input  :custom-style="customStyle" :placeholder-style="placeholderStyle" v-model="form.exceptionDetail" placeholder="请输入异常说明"/>
                    </u-form-item>
                </u-form>
            </view>
            <div :style="'height:'+ (isIphonex ? 152 : 118) + 'rpx'"></div>
        </scroll-view>
		<view class="container-footer" :style="'padding:'+(isIphonex ? '40rpx 60rpx' : '15rpx 60rpx')">
			<u-button hover-class='none' :custom-style="{
				'background-color': '#40CA8F',
				'color':'white'
			}" shape="circle" @click="ifConfirm">保存</u-button>
		</view>
		<u-picker :safe-area-inset-bottom="true" v-model="showTime" :params="params" mode="time" @confirm="submitTime"></u-picker>
        <u-modal v-model="showConfirm" content="本次运输尚未进行运输保养，请确认是否进行抵达确认？" confirm-text="是" cancel-text="否" show-cancel-button @confirm="nextStep" @cancel="showConfirm = false"></u-modal>
	</view>
</template>

<script>
import dateUtils from '@/common/utils/dateUtils.js'
import { uploadFiles } from '@/api/obsUpload/index'
import { purchaseOrderInfo1 } from '@/api/pages/purchaseOrder'
import { add } from '@/api/pages/acceptance'
export default {
    data() {
        return {
            customStyle: { fontSize: '26rpx' },
            labelStyle: { color: '#333', fontSize: '26rpx' },
            placeholderStyle: 'color:#999;font-size: 26rpx;',
            form: {
                purchaseNumber: '',
                deliveryEndTime: '',
                grossVideo: [],
                grossWeightingUrl: [],
                grossWeight: '',
                tareVideo: [],
                tareWeightingUrl: [],
                tareWeight: '',
                carHeadUrl: [],
                carTailUrl: [],
                exceptionDetail: ''
            },
            showTime: false, //时间弹出选择框
            deliveryEndTime: {
                title: '结束时间',
                timestamp: 0
            },
            params: {
                year: true,
                month: true,
                day: true,
                hour: true,
                minute: true,
                second: false
            },
            showPicker: false,
            isIphonex: getApp().globalData.systemInfo.isIphonex,
            errorType: ['message'],
            rules: {
                purchaseNumber:  { required: true, message: '请输入抵达头数', trigger: 'blur'},
                grossVideo: { required: true, message: '请上传抵达过磅视频', trigger: 'change'},
                grossWeightingUrl: { required: true, message: '请上传抵达过磅照片', trigger: 'change'},
                tareVideo: { required: true, message: '请上传卸车视频', trigger: 'blur'},
                tareWeightingUrl: { required: true, message: '请上传空载照片', trigger: 'blur'},
                carHeadUrl: { required: true, message: '请上传车头带牌照片', trigger: 'blur'},
                carTailUrl: { required: true, message: '请上传空载照片', trigger: 'blur'},
                tareWeight: { required: true, message: '请输入车尾带牌照片', trigger: 'blur'},
                grossWeight: { required: true, message: '请输入抵达重量', trigger: 'blur'},
                deliveryEndTime: { required: true, message: '请选择实际抵达时间', trigger: 'blur'},
            },
            purchaseOrderId: '',
            dataInfo: {},
            finalWeight: '',
            differenceWeight: '',
            differenceTime: '',
            differenceNum: '',
            tareWeight: '',
            showConfirm: false,
        }
    },
    onReady() {
        this.$refs.uForm.setRules(this.rules)
    },
    onLoad(opation) {
        this.purchaseOrderId = opation.purchaseOrderId
        this.getInfo()
    },
    methods: {
        getInfo() {
            purchaseOrderInfo1({
                purchaseOrderId: this.purchaseOrderId
            }).then(res=> {
                this.dataInfo = res.result || {}
                this.form.tareWeight = res.result.tareWeight || ''
                this.form.deliveryEndTime = this.dataInfo.deliveryEndTime
                this.getHours(this.dataInfo.deliveryEndTime, this.dataInfo.deliveryEndTime)
                this.changeFinalWeight()
            })
        },
        submitTime(val) {
            this.deliveryEndTime.timestamp = val.timestamp
            const currentTime  = val.year + '-' + val.month + '-' + val.day + ' ' + val.hour + ':' + val.minute
            this.deliveryEndTime.title = currentTime;
            this.form.deliveryEndTime = currentTime
            this.getHours(this.dataInfo.deliveryEndTime, this.form.deliveryEndTime)
        },

        getHours(dateStart,dateEnd) {
            const differenceTime = dateUtils.getHours(dateStart,dateEnd)
            this.differenceTime = differenceTime > 0 ? differenceTime.toFixed(2) : 0
        },
        ifConfirm() {
            if (this.dataInfo.maintenanceNumber <= 0) {
                this.showConfirm = true
            } else {
                this.nextStep()
            }
        },
        nextStep() {
            const errorArr = []
            Object.keys(this.form).forEach(key => {
                // 判断字段为空（null/undefined/空字符串）且值不等于 0（严格不等于）
                if (!this.form[key] && this.form[key] !== 0 || this.form[key].length === 0) {
                    errorArr.push(this.rules[key].message)
                }
            })
            if (errorArr.length > 0) {
                uni.showToast({
                    title: errorArr[0],
                    icon: 'none'
                })
                return
            }
            add({
                ...this.form,
                grossVideo: this.form.grossVideo.join(','),
                grossWeightingUrl: this.form.grossWeightingUrl.join(','),
                tareVideo: this.form.tareVideo.join(','),
                tareWeightingUrl: this.form.tareWeightingUrl.join(','),
                carHeadUrl: this.form.carHeadUrl.join(','),
                carTailUrl: this.form.carTailUrl.join(','),
                purchaseOrderId: this.purchaseOrderId
            }).then(res => {
                if (res.code == 200) {
                    uni.showToast({
                        title: '操作成功',
                        icon: 'none'
                    })
                    uni.navigateBack({
                        delta:1
                    })
                }
            })
        },
        deleteFile(type, index) {
            console.log(this.form[type])
            this.form[type].splice(index, 1)
            this.$forceUpdate()
            this.resetField(type)
        },
        unloadImage(type) {
            const that = this
            uni.chooseImage({
                count: 6, //默认9
                sizeType: ['original', 'compressed'], //可以指定是原图还是压缩图，默认二者都有
                sourceType: ['album', 'camera'], //从相册选择
                name: 'file',
                success: function (res) {
                    uploadFiles({
                        filePath: res.tempFilePaths[0],
                    }).then((data) => {
                        that.form[type].push(data)
                        that.resetField(type)
                    })
                },
                fail(e) {},
            })
        },
        unloadVideo(type) {
            const that = this
            uni.chooseVideo({
                count: 6, //默认9
                sizeType: ['compressed'], //可以指定是原图还是压缩图，默认二者都有
                sourceType: ['album', 'camera'], //从相册选择
                maxDuration: 60,
                success: function (res) {
                    uploadFiles({
                        filePath: res.tempFilePath,
                    }).then((data) => {
                        that.form[type].push(data)
                        that.resetField(type)
                    })
                },
                fail(e) {},
            })
        },
        previewImage(url) {
            uni.previewImage({
                urls: [url],
            })
        },
        resetField(value) {
            this.$refs.uForm.fields.forEach(e => {
                if (e.prop == value) {
                    e.resetField()
                }
            })
        },
        changeGrossWeight() {
            this.finalWeight = (Number(this.form.grossWeight) - Number(this.form.tareWeight)).toFixed(2)
            this.differenceWeight = (Number(this.dataInfo.netWeight) - Number(this.finalWeight)).toFixed(2)
        },
        changePurchaseNumber() {
            this.differenceNum = (Number(this.dataInfo.deliveryNumber) - Number(this.form.purchaseNumber)).toFixed(0)
        },
        changeFinalWeight() {
            this.tareWeight = (Number(this.form.tareWeight) - Number(this.dataInfo.tareWeight)).toFixed(2)
        }
    }
}
</script>

<style lang="less" scoped>

.regulatory-area {
    padding: 29rpx;
    .regulatory-area-title {
        font-size: 28rpx;
        color: #333;
        font-weight: 400;
        padding-bottom: 24rpx;
        font-family: PingFang SC-Bold, PingFang SC;
    }
}
.container {
    margin: 30rpx;
    background: #fff;
    box-sizing: border-box;
    padding: 30rpx 32rpx 40rpx;
    border-radius: 30rpx;

    .time-view {
        display: flex;
        justify-content: space-between;
        line-height: 50rpx;

        .start-time {
            width: 50%;
            border-bottom: 1px solid #F4F4F4;
            text-align: center;
            color: #999;
        }

    }
    /deep/ .u-form-item {
        padding: 20rpx 20rpx !important;
    }

    .tips {
        font-size: 28rpx;
        color: #c0c3ca;
    }

    .voucher {
        padding-bottom: 40rpx;
        font-size: 28rpx;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #999999;
    }

    .all-img {
        image {
        width: 154rpx;
        height: 154rpx;
        background: #d8d8d8;
        border-radius: 16rpx;
        margin-right: 20rpx;
        }
    }

    .manyMode {
        background: transparent;
        height: auto;
    }

    .uploadImage {
        display: flex;
        flex-wrap: wrap;
        // justify-content: space-between;
        // align-content: space-between;
        position: relative;

        .itemAlready {
        width: 140rpx;
        height: 140rpx;
        border-radius: 8rpx;
        position: relative;
        margin: 0 20rpx 10rpx 0rpx;

        image, video {
            width: 100%;
            height: 100%;
            border-radius: 8rpx;
        }

        .closeIcon {
            width: 32rpx;
            height: 32rpx;
            background-image: url('../../../static/modalImg/error.png');
            position: absolute;
            background-size: cover;
            top: -10rpx;
            right: -10rpx;
        }
    }

    .item {
    width: 140rpx;
    height: 140rpx;
    border-radius: 8rpx;
    position: relative;
    border: 2rpx dashed #d8d8d8;

    .uploadIcon {
        width: 100%;
        height: 120rpx;
        display: flex;
        justify-content: center;
        align-items: flex-end;
        font-size: 24rpx;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #d8d8d8;
        background: url('https://fengtaohui.oss-cn-beijing.aliyuncs.com/uploads/image/20230105/6400955ab08a2f639e9790b815c418db63b666a9d7e40.png') no-repeat;
        background-size: 20rpx 20rpx;
        background-position: center 30rpx;
        position: absolute;
        top: 0;
        left: 0;
        bottom: 0;
        right: 0;
        margin: auto;
        z-index: 5;
    }
    }
}
}

uni-picker-view {
    display: block;
}

uni-picker-view .uni-picker-view-wrapper {
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    height: 100%;
    background-color: white;
}

uni-picker-view[hidden] {
    display: none;
}

picker-view {
    width: 100%;
    // height: 600upx;
    height: 600rpx;
    margin-top: 20upx;
}

.item {
    line-height: 100upx;
    text-align: center;
}

.popup-view {
    .picker-btn {
        display: flex;
        justify-content: space-between;
        padding: 30rpx 40rpx;

        .left {
            color: #999;
            font-size: 28rpx;
            font-family: PingFang SC-Medium;
        }

        .middle {
            font-size: 32rpx;
            color: #222;
            font-family: PingFang SC-Heavy;
        }

        .right {
            color: #40CA8F;
            font-size: 32rpx;
            font-family: PingFang SC-Medium;
        }
    }
}
.scroll-view{
    background-color: #fff;
}
.submit{
    width: 100%;
    height: 80rpx;
    color: #fff;
    text-align: center;
    line-height: 80rpx;
    font-size: 32rpx;
    padding: 0 20rpx;
    margin-top: 40rpx;
    view{
        width: 100%;
        height: 80rpx;
        border-radius: 40rpx;
        background-color: #40CA8F;
    }
}

.container-footer {
    width: 100%;
    position: fixed;
    bottom: 0;
    background-color: #f7f7f7;
    color: white;
    z-index: 100;
    display: flex;
    justify-content: space-between;
    u-button{
        width: 100%;
    }
}

.job-type-content {
    display: flex;
    flex-wrap: wrap;
    p {
        font-size: 24rpx;
        color: #999;
        background-color: #F4F4F4;
        padding: 0px 25rpx;
        border-radius: 60rpx 60rpx 60rpx 60rpx;
        margin: 15rpx 30rpx 15rpx 0;
        border: 2rpx solid transparent;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        line-height: 38rpx;
    }

    .job-type-active {
        border: 2rpx solid #40CA8F;
        color: #40CA8F;
        background-color: white;
    }

    .policy-type-active {
        border: 2rpx solid #40CA8F;
        color: #40CA8F;
        background-color: white;
    }

    .ear-type-active {
        border: 2rpx solid #40CA8F;
        color: #40CA8F;
        background-color: white;
    }
}
</style>