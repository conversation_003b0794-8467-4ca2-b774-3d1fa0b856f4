<template>
    <view>
        <u-popup v-model="showPicker" height='80%' mode="bottom" border-radius="14" :closeable="true" @close="closeModel">
            <scroll-view class="main" scroll-y :scroll-with-animation="true" refresher-enabled scroll-top="scrollTop">
                <view v-for="(item, index) in dataList" :key="index">
                    <video :src="item" controls loop muted/>
                </view>
            </scroll-view>
        </u-popup>
    </view>
</template>

<script>
export default {
    data() {
        return {
            showPicker: false,
            dataList: [],
        }
    },
    props: {
        videoUrl: {
            type: String,
            default: ''
        },
        pickerFilterShow: {
            type: Boolean,
            default: false
        }
    },
    watch: {
        pickerFilterShow: {
            handler(newValue, oldValue) {
                console.log(newValue, 'newValue')
                this.showPicker = newValue
            },
            immediate: true,
            deep: true
        },
        videoUrl(newVal, oldVal) {
            if(newVal) {
                this.dataList = this.videoUrl && this.videoUrl.split(',')
            }
        }
    },
    methods: {
        closeModel() {
            this.$emit('closeModel')
        }
    }
}
</script>

<style lang="scss" scoped>
.main{
    margin-top: 60rpx;
    video{
        width: 100%;
        height: 360rpx;
        margin-top: 15rpx;
    }
}
</style>