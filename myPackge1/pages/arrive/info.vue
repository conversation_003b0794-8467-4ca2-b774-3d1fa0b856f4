<template>
    <view>
        <scroll-view class="main" scroll-y>
            <block>
                <section>
                    <view class="list-section" >
                        <view style="width:100%'; margin: 28rpx 0 24px 19rpx;">
                            <view class="list-item">
                                <p>抵达数量（头）：{{ dataInfo.purchaseNumber }}</p>
                                <p>运转差额（头）：{{ dataInfo.purchaseNumberDifferent }}</p>
                                <p>实际抵达时间：{{ dataInfo.deliveryEndTime }}</p>
                                <p>延迟时长（小时）：{{ dataInfo.delayHours }}</p>
                            </view>
                            <view class="file_box" >
                                <p>抵达过磅视频</p>
                                <view class="file_items">
                                    <video v-for="(item,index) in grossVideo" :src="item" :key="index" 
                                        :controls="false"
                                        :show-fullscreen-btn="false"
                                        :show-play-btn="false"
                                        :show-center-play-btn="false"
                                        @click="showVideoFn(item)" class="file_img"></video>
                                </view>
                            </view>
                            <view class="file_box" >
                                <p>抵达过磅照片</p>
                                <view class="file_items">
                                    <image :src="dataInfo.grossWeightingUrl" mode="aspectFill" @click="previewImage(dataInfo.grossWeightingUrl)" class="file_img"></image>
                                </view>
                            </view>
                            <view class="list-item">
                                <p>抵达重量（kg）：{{ dataInfo.grossWeight }}</p>
                            </view>
                            <view class="file_box" >
                                <p>卸车视频</p>
                                <view class="file_items">
                                    <video v-for="(item,index) in grossVideo" :src="item" :key="index" 
                                        :controls="false"
                                        :show-fullscreen-btn="false"
                                        :show-play-btn="false"
                                        :show-center-play-btn="false"
                                        @click="showVideoFn(item)" class="file_img"></video>
                                </view>
                            </view>
                            <view class="file_box" >
                                <p>空载照片</p>
                                <view class="file_items">
                                    <image  v-for="(item,index) in tareWeightingUrl" :key="index" :src="item" mode="aspectFill" @click="previewImage(item)" class="file_img"></image>
                                </view>
                            </view>
                            <view class="list-item">
                                <p>空车重量（kg：{{ dataInfo.tareWeight }}</p>
                                <p>抵达净重量（kg）：{{ dataInfo.netWeight }}</p>
                                <p>运转重量差（kg）：{{ dataInfo.weightDifferent }}</p>
                            </view>
                        </view>
                    </view>
                </section>
            </block>
        </scroll-view>
        <u-popup v-model="showVideo" mode="center" @close="showVideo = false">
			<view class="video-box" v-if="showVideo">
                <video :src="videoUrl" controls autoplay loop muted/>
            </view>
		</u-popup>
    </view>
</template>
<script>
import { info } from '@/api/pages/acceptance'
export default {
    name: '',
    data() {
        return {
            dataInfo: {},
            purchaseOrderId: '',
            tareWeightingUrl: [],
            grossVideo: [],
            tareVideo: [],
            showVideo: false,
            videoUrl: ''
        }
    },
    onLoad(opation) {
        // 接收路由参数
        this.purchaseOrderId = opation.purchaseOrderId
        this.getInfo()
    },
    methods: {
        getInfo() { 
            uni.showLoading({
                title: '加载中',
				icon: 'none'
			})
            info({
                purchaseOrderId: this.purchaseOrderId
            }).then(response=>{
                if(response.code === 200 ) {
                    this.dataInfo =response.result || {};
                    this.tareWeightingUrl = this.dataInfo.tareWeightingUrl.split(',')
                    this.grossVideo = this.dataInfo.grossVideo.split(',')
                    this.tareVideo = this.dataInfo.tareVideo.split(',')
                }
            })
            uni.hideLoading()
        },
        previewImage(url) {
            uni.previewImage({
                urls: [url],
            })
        },
        showVideoFn(item) {
            this.videoUrl = item
            this.showVideo = true
        }
    },
}
</script>

<style scoped lang="scss">
    @import "@/common/css/superviseHome.scss";
    .list-item p{
        white-space: wrap;
    }
    .file_box{
        p{
            line-height: 48rpx;
            color: #999999;
        }
        .file_items{
            display: flex;
            flex-wrap: wrap;
            image, video{
                width: 134rpx;
                height: 134rpx;
                background: #d8d8d8;
                border-radius: 16rpx;
                margin-right: 20rpx;
                margin-bottom: 20rpx;
            }
        }
    }
    .video-box{
        width: 640rpx;
        min-height: 400rpx;
        background: #000;
        video{
            width: 100%;
        }
    }
</style>