<template>
    <view>
        <scroll-view class="main" scroll-y>
            <section>
                <view class="list-section">
                    <view class="title">销售订单详情</view>
                    <view class="item" v-for="(item, index) in orderDetails" :key="index">
                        <view class="label">{{ item.label }}：</view>
                        <view class="value">{{ item.value }}</view>
                    </view>
                </view>
            </section>
        </scroll-view>
    </view>
</template>

<script>
export default {
    data() {
        return {
            orderDetails: [
                { label: '订单编号', value: 'G2345678998765434' },
                { label: '需求方', value: '需求方公司' },
                { label: '活畜品种', value: '西门塔尔-母牛犊' },
                { label: '活畜月龄', value: '48月以上' },
                { label: '活畜重量', value: '600-750kg' },
                { label: '订单数量', value: 'null头' },
                { label: '采购要求', value: '每头重量要符合标准' },
                { label: '销售单价', value: '31.00元' },
                { label: '承运人', value: '李四号' },
                { label: '计划发车时间', value: '2025-05-15 14:29' },
                { label: '预计抵达时间', value: '2025-05-16 14:29' },
                { label: '项目经理', value: '李一号' },
                { label: '验收员', value: '李一号' }
            ]
        };
    }
};
</script>

<style scoped lang="scss">
.main {
    padding: 20rpx;
}

.list-section {
    background-color: #fff;
    border-radius: 10rpx;
    margin-bottom: 20rpx;
    padding: 20rpx;

    .title {
        font-weight: bold;
        font-size: 32rpx;
        color: #333333;
        margin-bottom: 20rpx;
    }

    .item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20rpx;

        .label {
            font-weight: 400;
            font-size: 26rpx;
            color: #999999;
        }

        .value {
            font-weight: 400;
            font-size: 26rpx;
            color: #333333;
        }
    }
}
</style>