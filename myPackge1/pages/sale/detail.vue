<template>
    <view style="padding-bottom: 40rpx;">
        <scroll-view class="main" scroll-y>
            <block>
                <section>
                    <view class="title">
                        订单信息
                    </view>
                    <view class="list-section" >
                        <view class="middle" style="width:100%';">
                            <view class="list-item">
                                <p>订单编号：<span>{{ dataInfo.purchaseOrderCode }}</span></p>
                                <p>需求方：<span>{{ dataInfo.customerName }}</span></p>
                                <p>活畜品种：<span>{{ dataInfo.varietiesName }} - {{ dataInfo.categoryName }}</span></p>
                                <p>活畜月龄：<span>{{ dataInfo.ageRange }}</span></p>
                                <p>活畜重量：<span>{{ dataInfo.weightRange }}</span></p>
                                <p>订单数量：<span>{{ dataInfo.demandNumber }}头</span></p>
                                <p>采购要求：<span>{{ dataInfo.demandOrderRemark }}</span></p>
                                <p>销售单价：<span>{{ dataInfo.salesUnitPrice }}元</span></p>
                                <p>承运人：<span>{{ dataInfo.driverName }} ({{ dataInfo.licensePlateNumber }})</span></p>
                                <!-- <p>监管方：<span>{{ dataInfo.supervisorCompanyName }}</span></p> -->
                                <p>计划发车时间：<span>{{ dataInfo.deliveryStartTime }}</span></p>
                                <p>预计抵达时间：<span>{{ dataInfo.deliveryEndTime }}</span></p>
                                <p>项目经理：<span>{{ dataInfo.projectManagerName }}</span></p>
                                <p>验收员：<span>{{ dataInfo.acceptorName }}</span></p>
                            </view>
                        </view>
                    </view>
                    <view class="title">
                        订单进度
                        <view class="btn-box">
                            <text>{{ dataInfo.showStatusName }}</text>
                            <u-button hover-class='none' custom-style="btn" type="primary" @click="exportFile">生成word文档</u-button>
                        </view>
                    </view>
                    <view class="list-section" >
                        <view class="tabs">
                            <view @click="selectItem(20)" class="tabs-item" :class="{ 'current': currentItem == 20 }">发车</view>
                            <view @click="selectItem(30)" class="tabs-item" :class="{ 'current': currentItem == 30 }">转运</view>
                            <view @click="selectItem(40)" class="tabs-item" :class="{ 'current': currentItem == 40 }">抵达</view>
                        </view>
                        <view>
                            <Depart v-if="currentItem == 20" :dataInfo="dataInfo"></Depart>
                            <Operate v-if="currentItem == 30" :dataInfo="dataInfo"></Operate>
                            <Arrived v-if="currentItem == 40" :dataInfo="dataInfo"></Arrived>
                        </view>
                    </view>
                    <view class="title">
                        款项信息
                        <view class="btn-box">
                            <text>{{ dataInfo.payoutStatus == 1 ? '已收款' : '待收款' }}</text>
                        </view>
                    </view>
                    <view class="list-section" >
                        <view>
                            <Sale :dataInfo="dataInfo"></Sale>
                        </view>
                    </view>
                </section>
            </block>
        </scroll-view>
    </view>
</template>
<script>
import { purchaseOrderInfo, dynamic } from '@/api/pages/purchaseOrder'
import Depart from '../purchase/components/depart.vue'
import Operate from '../purchase/components/operate1.vue'
import Arrived from '../purchase/components/arrived.vue'
import Sale from '../purchase/components/sale.vue'
import { setStorage, removeStorage } from '@/common/utils/storage.js'
export default {
    components: {
        Depart,
        Operate,
        Arrived,
        Sale
    },
    data() {
        return {
			numList:[
				{
					name: '发车'
				}, {
					name: '转运'
				}, {
					name: '抵达'
				}
			],
            dataInfo: {},
            confirmor: '',
            currentItem:'',
            currentItemPay: 1,
            dataList: []
        }
    },
    onLoad(opation) {
        // 接收路由参数
        this.purchaseOrderId = opation.purchaseOrderId
        this.confirmor = opation.confirmor
        removeStorage('purchaseOrderDataInfo')
        this.getInfo()
    },
    methods: {
        getInfo() { 
            uni.showLoading({
                title: '加载中',
				icon: 'none'
			})
            purchaseOrderInfo({
                purchaseOrderId: this.purchaseOrderId
            }).then(response=>{
                if(response.code === 200 ) {
                    this.dataInfo =response.result || {};
                    setStorage('purchaseOrderDataInfo', this.dataInfo)
                    this.currentItem = 20
                }
            })
            uni.hideLoading()
        },
        selectItem(item) {
			console.log(item)
			
			
			
            this.currentItem = item == 0 ? 20 : item == 1 ? 30 : 40;
			console.log(this.currentItem)
            if(this.dataInfo.showStatus >= item) {
                this.currentItem = item
            }
        },
        exportFile() {
            dynamic({
                id: this.purchaseOrderId
            }).then(response=>{
                if(response.code === 200 ) {
                    this.previewFiles(response.result)
                }
            })
        },
		previewFiles(url){
			wx.downloadFile({
				url,
				success: (res) => {
                    uni.saveFile({
                        tempFilePath: res.tempFilePath, 
                        success: function (res) {
                            uni.openDocument({
                                filePath: res.savedFilePath,
                                showMenu: true,
                                success: function (res) {
                                    console.log('打开文档成功');
                                }
                            });
                        }
                    })
				},
			})
		},
    },
}
</script>

<style scoped lang="scss">
@import "@/common/css/superviseHome.scss";
.list-item p{
    white-space: normal !important;
    display: flex;
    justify-content: space-between;
    font-size: 26rpx;
    color: #999;
    line-height: 43rpx;
    span{
        width: 500rpx;
        text-align: right;
        color: #333;
        font-size: 26rpx;
        font-weight: 400;
    }
}
/deep/ .u-btn{
    width: 180rpx!important;
    height: 40rpx !important;
    font-size: 24rpx!important;
    margin-left: 10rpx;
    background: linear-gradient( 305deg, #40CA8F 0%, #1AAF77 100%);
}
.title{
    list-style: 50rpx;
    font-weight: 500;
    font-size: 32rpx;
    color: #222222;
    padding: 20rpx;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .btn-box{
        display: flex;
        align-items: center;
    }
    text{
        font-weight: 400;
        font-size: 28rpx;
        color: #EA501E;
        margin-right: 20rpx;
    }
}
.list-section{
    flex-direction: column;
}
.tabs{
    display: flex;
    margin: 20rpx;
    background: #F7F8F7;
    border-radius: 10rpx;
    height: 64rpx;
    padding: 6rpx;
    box-sizing: border-box;
    .tabs-item{
        flex: 1;
        text-align: center;
        font-size: 26rpx;
        color: #999999;
        padding: 16rpx 20rpx;
        margin: 0 8rpx;
        border-radius: 20rpx;
        width: 118rpx;
        height: 52rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 500;
        .icon{
            font-size: 24rpx;
            margin-left: 5rpx;
        }
    }
    .current{
        background-color: #1AAF77;
        color: #FFFFFF;
    }
}
</style>