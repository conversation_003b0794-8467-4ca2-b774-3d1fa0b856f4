<template>
	<view>
		<u-popup v-model="showPicker" mode="bottom" border-radius="14" :closeable="true" @close="canel">
			<scroll-view scroll-y="true" class="scroll-view">
				<u-form :model="form" ref="form">
					<view class="regulatory-area">
						<h3 class="regulatory-area-title">需求方</h3>
						<u-form-item prop="customerCompanyName">
							<u-input :custom-style="customStyle" :placeholder-style="placeholderStyle" v-model="form.customerCompanyName" placeholder="请输入需求方" />
						</u-form-item>
					</view>
					<!-- <view class="regulatory-area">
						<h3 class="regulatory-area-title">项目经理</h3>
						<u-form-item prop="projectManagerName">
							<u-input :custom-style="customStyle" :placeholder-style="placeholderStyle" v-model="form.projectManagerName" placeholder="请输入项目经理" />
						</u-form-item>
					</view>
					<view class="regulatory-area">
						<h3 class="regulatory-area-title">牛经纪</h3>
						<u-form-item prop="brokerName">
							<u-input :custom-style="customStyle" :placeholder-style="placeholderStyle" v-model="form.brokerName" placeholder="请输入牛经纪" />
						</u-form-item>
					</view> -->
					<view class="regulatory-area">
						<h3 class="regulatory-area-title">状态</h3>
						<u-form-item prop="showStatus" right-icon="arrow-right">
							<u-input :custom-style="customStyle" :placeholder-style="placeholderStyle" v-model="showStatusName" disabled placeholder="请选择状态" @click="showSelect = true"/>
						</u-form-item>
					</view>
					<view class="regulatory-area">
						<h3 class="regulatory-area-title">订单日期范围</h3>
						<view class="time-view">
							<view class="start-time" :style="startTime.title === '起始时间' ? 'color:#999' : 'color:#222'"
								@click="handleShowTime('startTime')">
								{{ startTime.title }}
							</view>
							-
							<view class="start-time" :style="endTime.title === '结束时间' ? 'color:#999' : 'color:#222'"
								@click="handleShowTime('endTime')">
								{{ endTime.title }}
							</view>
						</view>
					</view>
					<view class="regulatory-area">
						<h3 class="regulatory-area-title">交易总价区间</h3>
						<view class="time-view">
							<u-input :custom-style="customStyle" :placeholder-style="placeholderStyle" v-model="form.minAmount" placeholder="最小值" :clearable="false" input-align="center"/>
							-
							<u-input :custom-style="customStyle" :placeholder-style="placeholderStyle" v-model="form.maxAmount" placeholder="最大值" :clearable="false" input-align="center"/>
						</view>
					</view>
				</u-form>
				<view :style="'height:'+ (isIphonex ? 48 : 24) + 'rpx'"></view>
			</scroll-view>
			<view class="button-group">
				<view class="button-group-view box">
					<view class="button-group-reset flex" @click="resetForm">
						重置
					</view>
					<view class="button-group-submit flex" @click="submitForm">
						确认
					</view>
				</view>
			</view>
		</u-popup>
		<u-select confirm-color='#40CA8F' v-model="showSelect" :list="showStatusList" value-name="value" label-name="label" @cancel="showSelect = false" @confirm="submitPicker"></u-select>
		<!-- <u-picker :safe-area-inset-bottom="true" v-model="showTime" mode="time" @confirm="submitTime"></u-picker> -->
		<u-calendar range-bg-color="rgba(64,202,143,0.13)" active-bg-color="#40CA8F"  range-color="#40CA8F" btn-type="success" v-model="showData" :mode="`range`" @change='changeData'></u-calendar>
		
	</view>
</template>

<script>
	export default {
		props: {
			pickerFilterShow: {
				type: Boolean,
				default: false
			}
		},
		data() {
			return {
                customStyle: { fontSize: '26rpx' },
                placeholderStyle: 'color:#999;font-size: 26rpx;',
				showTime: false, //时间弹出选择框
				showData:false,
				endTime: {
					title: '结束时间',
					timestamp: 0
				}, 
				startTime: {
					title: '起始时间',
					timestamp: 0
				},
				form: {
					brokerName: '',
					startTime:'',
					endTime:'',
					customerCompanyName:'',
					projectManagerName:'',
					minAmount:'',
					maxAmount:'',
					showStatus: ''
				},
				showPicker: false,
				showStatusName: '',
				showSelect: false, //状态选择框
				typeTime: '',
				showStatusList: [
					{ label: '运输中', value: 30 },
					{ label: '待商榷', value: 40 },
					{ label: '待收款', value: 50 },
					{ label: '待结算', value: 59 },
				]
			}
		},
		watch: {
			pickerFilterShow: {
				handler(newValue, oldValue) {
					this.showPicker = newValue
				},
				immediate: true,
				deep: true
			}
		},
		methods: {
			canel() {
				this.$emit('canel')
			},
			submitTime(val) {
				const arr = ['startTime', 'endTime']
				arr.map((item, index) => {
					if (item === this.typeTime) {
						this[item].timestamp = val.timestamp
						if (index === 0 || index === 1) {
							if (this.startTime.timestamp > this.endTime.timestamp && this.endTime.timestamp !== 0) {
								uni.showToast({
									title: '结束时间不能小于起始时间',
									icon: 'none'
								})
								return false
							}
						}
						this[item].title = val.year + '-' + val.month + '-' + val.day
						this.form[item] = val.year + '-' + val.month + '-' + val.day
					}
				})
			},
			handleShowTime(val) {
				this.showData = true;
				/* if (this.startTime.timestamp === 0 && val === 'endTime') {
					uni.showToast({
						title: '请选择起始日期',
						icon: 'none'
					})
					return
				}
				this.typeTime = val
				this.showTime = true */
			},
			changeData(e){
				this.startTime.title = e.startDate;
				this.endTime.title = e.endDate;
				this.form.startTime = e.startDate;
				this.form.endTime = e.endDate;
				this.showData = false;
			},
			submitPicker(val) {
				const values = val[0]
				this.showStatusName = values.label
				this.form.showStatus = values.value
			},
			handleSelect() {
				this.showSelect = true	
			},
			reset() {
				this.endTime.title = "起始时间"
				this.startTime.title = "结束时间"
				this.endTime.timestamp = 0
				this.startTime.timestamp = 0
				this.showStatusName = ''
				this.form = {
					brokerName: '',
					startTime:'',
					endTime:'',
					customerCompanyName:'',
					projectManagerName:'',
					minAmount:'',
					maxAmount:'',
					showStatus: ''
				}
			},
			resetForm() {
				this.$refs.clientSelectRef.resetClick()
				this.reset()
				this.$emit('canel')
				this.$emit('submitForm', this.form)
			}
		}
	}
</script>

<style lang="less" scoped>
	
	.button-group {
		width: 100%;
		padding: 29rpx;
		position: relative;
		background-color: white;
		z-index: 10;
		// margin-top: 53rpx;

		.button-group-view {
			border-radius: 100rpx 100rpx 100rpx 100rpx;
			display: flex;
			background-color: #40CA8F;

			.button-group-reset {
				padding: 20rpx 0;
				text-align: center;
				background: #FFFFFF;
				border-radius: 100rpx 0rpx 120rpx 100rpx;
				border: 2rpx solid #40CA8F;
				font-size: 32rpx;
				color: #40CA8F;
				font-family: PingFang SC-Bold, PingFang SC;
			}

			.button-group-submit {
				padding: 20rpx 0;
				text-align: center;
				background: #40CA8F;
				font-size: 32rpx;
				border-radius: 0rpx 100rpx 100rpx 0rpx;
				color: #FFFFFF;
			}
		}

	}

	.regulatory-area {
		padding: 29rpx;

		.regulatory-area-title {
			font-size: 28rpx;
			color: #333;
			font-weight: 400;
			padding-bottom: 24rpx;
			font-family: PingFang SC-Bold, PingFang SC;
		}

		.time-view {
			display: flex;
			justify-content: space-between;
			line-height: 50rpx;

			.start-time {
				width: 50%;
				border-bottom: 1px solid #F4F4F4;
				text-align: center;
				color: #999;
			}

		}
	}

	.scroll-view {
		height: 1000rpx;
		overflow-y: scroll;
	}

	.job-other-type {
		padding: 8rpx 29rpx;
		// padding-top: 0;

		.job-type-title {
			font-size: 28rpx;
			color: #333;
			font-weight: 400;
			padding-bottom: 24rpx;
			font-family: PingFang SC-Bold, PingFang SC;
		}

		.job-type-content {
			display: flex;
			flex-wrap: wrap;

			p {
				color: #999;
				background-color: #F4F4F4;
				padding: 17rpx 47rpx;
				border-radius: 100rpx 100rpx 100rpx 100rpx;
				margin: 0 21rpx 30rpx 0;
				border: 2rpx solid transparent;
			}

			.policy-type-active {
				border: 2rpx solid #40CA8F;
				color: #40CA8F;
				background-color: white;
			}

			.ear-type-active {
				border: 2rpx solid #40CA8F;
				color: #40CA8F;
				background-color: white;
			}
		}
	}


	uni-picker-view {
		display: block;
	}

	uni-picker-view .uni-picker-view-wrapper {
		display: flex;
		align-items: center;
		position: relative;
		overflow: hidden;
		height: 100%;
		background-color: white;
	}

	uni-picker-view[hidden] {
		display: none;
	}

	picker-view {
		width: 100%;
		// height: 600upx;
		height: 600rpx;
		margin-top: 20upx;
	}

	.item {
		line-height: 100upx;
		text-align: center;
	}

	.popup-view {
		.picker-btn {
			display: flex;
			justify-content: space-between;
			padding: 30rpx 40rpx;

			.left {
				color: #999;
				font-size: 28rpx;
				font-family: PingFang SC-Medium;
			}

			.middle {
				font-size: 32rpx;
				color: #222;
				font-family: PingFang SC-Heavy;
			}

			.right {
				color: #40CA8F;
				font-size: 32rpx;
				font-family: PingFang SC-Medium;
			}
		}
	}
</style>