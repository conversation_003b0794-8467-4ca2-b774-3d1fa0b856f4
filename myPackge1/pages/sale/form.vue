<template>
	<view>
        <scroll-view scroll-y :scroll-with-animation="true" class="main">
            <view class="container">
                <u-form :model="form" ref="uForm" :error-type="errorType" label-width="auto" label-position="top" :label-style="labelStyle">
					<u-form-item label="交易日期" required="true" prop="finalTradeTime" right-icon="arrow-right">
						<u-input :custom-style="customStyle" :placeholder-style="placeholderStyle" v-model="form.finalTradeTime" placeholder="请选择交易日期" disabled @click="handleShowTime('finalTradeTime')"/>
					</u-form-item>
                    <u-form-item label="活畜品种">
                        <text>{{ dataInfo.varietiesName }}</text>
                    </u-form-item>
                    <u-form-item label="品种分类" >
						<text>{{ dataInfo.categoryName }}</text>
                    </u-form-item>
                    <u-form-item label="活畜月龄">
                        <text>{{ dataInfo.ageRange }}</text>
                    </u-form-item>
                    <u-form-item label="活畜重量" >
						<text>{{ dataInfo.weightRange }}</text>
                    </u-form-item>
                    <u-form-item label="活畜耳标列表" >
                        <u-input :custom-style="customStyle" :placeholder-style="placeholderStyle" v-model="dataInfo.purchaseEarTags" disabled placeholder=" " @click="erCodeShowFn(dataInfo.purchaseEarTags)"/>
                    </u-form-item>
                    <u-form-item label="订单数量（头）" required="true" prop="finalNumber">
                        <u-input :custom-style="customStyle" :placeholder-style="placeholderStyle" v-model="form.finalNumber" type="number" placeholder="请输入订单数量"/>
                    </u-form-item>
                    <u-form-item label="验收员">
                        <text>{{ dataInfo.acceptorName }}</text>
					</u-form-item>
                    <u-form-item label="活牛总重量（kg）" required="true" prop="finalWeight">
                        <u-input :custom-style="customStyle" :placeholder-style="placeholderStyle" v-model="form.finalWeight" type="number" placeholder="请输入活牛总重量" @input="changefinalAmount"/>
                    </u-form-item>
                    <u-form-item label="活牛单价（元/kg）" required="true" prop="finalUnitPrice">
                        <u-input :custom-style="customStyle" :placeholder-style="placeholderStyle" v-model="form.finalUnitPrice" type="number" placeholder="请输入活牛单价" @input="changefinalAmount"/>
                    </u-form-item>
                    <u-form-item label="交易总价（元）" required="true" prop="finalAmount">
                        <u-input :custom-style="customStyle" :placeholder-style="placeholderStyle" v-model="finalAmount" disabled placeholder="请输入交易总价" />
                    </u-form-item>
					<u-form-item label="客户接收单" required="true" prop="finalAcceptanceUrl" :border-bottom="false" style="overflow: visible">
						<view class="uploadImage">
							<view class="itemAlready" v-if="form.finalAcceptanceUrl">
								<image mode="scaleToFill" :src="form.finalAcceptanceUrl" @click="previewImage(form.finalAcceptanceUrl)" />
								<view class="closeIcon" @click="deleteImage(index)"></view>
							</view>
							<view class="item" v-if="!form.finalAcceptanceUrl">
								<view class="uploadIcon" @click="unloadImage()">点击上传</view>
							</view>
						</view>
					</u-form-item>
					<u-form-item prop="saleContractUrl" label="合同文件"  :required="true">
						<u-input placeholder=" " v-model="saleContractUrlName" disabled  @click="previewFiles(form.saleContractUrl)"/>
						<img slot="right" style="width: 40rpx; height: 40rpx" @click="uploadPdf()" src="../../../myPackge2/icon/pdf.png" alt="" />
					</u-form-item>
                    <u-form-item label="款项说明" required="true" prop="paymentDetail">
                        <u-input :custom-style="customStyle" :placeholder-style="placeholderStyle" v-model="form.paymentDetail" type="textarea" placeholder="请输入款项说明" />
                    </u-form-item>
				</u-form>
			</view>
			<div :style="'height:'+ (isIphonex ? 152 : 118) + 'rpx'"></div>
        </scroll-view>
		<view class="container-footer" :style="'padding:'+(isIphonex ? '40rpx 60rpx' : '15rpx 60rpx')">
			<u-button hover-class='none' :custom-style="{
				'background-color': '#40CA8F',
				'color':'white'
			}" shape="circle" @click="addPurchase">提交</u-button>
		</view>
		<u-picker :safe-area-inset-bottom="true" v-model="showTime" mode="time" @confirm="submitTime"></u-picker>
		<u-modal v-model="erCodeShow" :content="codeContent"></u-modal>
	</view>
</template>

<script>
import { saleOrderInfo, confirm } from "@/api/pages/sale.js"
import { uploadFiles } from '@/api/obsUpload/index'
export default {
	data() {
		return {
			customStyle: { fontSize: '26rpx' },
			labelStyle: { color: '#333', fontSize: '26rpx' },
			placeholderStyle: 'color:#999;font-size: 26rpx;',
			showTime: false, //时间弹出选择框
			finalTradeTime: {
				title: '起始时间',
				timestamp: 0
			},
			erCodeShow: false,
			codeContent: '',
			form: {
				finalAcceptanceUrl: '',
				finalUnitPrice: '',
				finalWeight: '',
				finalNumber: '',
				finalTradeTime: '',
				saleContractUrl: '',
				paymentDetail: ''
			},
			errorType: ['message'],
			rules: {
				finalTradeTime: [
					{ required: true, message: '请选择交易日期', trigger: ['blur', 'change'] }
				],
				finalNumber: [
					{ required: true, message: '请输入订单数量', trigger: ['blur', 'change'] }
				],
				finalWeight: [
					{ required: true, message: '请输入活牛重量', trigger: ['blur', 'change'] }
				],
				finalUnitPrice: [
					{ required: true, message: '请输入活牛单价', trigger: ['blur', 'change'] }
				],
				finalAcceptanceUrl: [
					{ required: true, message: '请上传客户接收单', trigger: ['blur', 'change'] }
				],
				saleContractUrl: [
					{ required: true, message: '请上传合同文件', trigger: ['blur', 'change'] }
				],
				paymentDetail: [
					{ required: true, message: '请输入款项说明', trigger: 'blur' }
				],
			},
			isIphonex: getApp().globalData.systemInfo.isIphonex,
			indicatorStyle: `height: ${Math.round(uni.getSystemInfoSync().screenWidth / (750 / 100))}px;`,
			finalAmount: '',
			purchaseOrderId: '',
			dataInfo: {},
			isEdit: '',
			fileType: ["pdf"],
			saleContractUrlName: ''
		}
	},
	onReady() {
		this.$refs.uForm.setRules(this.rules)
	},
	onLoad(opation) {
		this.purchaseOrderId = opation.purchaseOrderId
		this.isEdit = opation.isEdit
		this.getInfo()
	},
	methods: {
		getInfo(){
			saleOrderInfo({
				purchaseOrderId: this.purchaseOrderId
			}).then(res => {
				const data = res.result
				this.dataInfo = data
				this.form.finalAcceptanceUrl = data.finalAcceptanceUrl;
				this.form.finalUnitPrice = data.finalUnitPrice;
				this.form.finalWeight = data.finalWeight;
				this.form.finalNumber = data.finalNumber + '';
				this.form.finalTradeTime = data.finalTradeTime;
				this.form.paymentDetail = data.paymentDetail
				this.form.saleContractUrl = data.saleContractUrl
				this.saleContractUrlName = data.saleContractUrl ? '合同附件.pdf' : ''
				this.changefinalAmount()
			})
		},
		submitTime(val) {
			const arr = ['finalTradeTime']
			arr.map((item, index) => {
				if (item === this.typeTime) {
					this[item].timestamp = val.timestamp
					this[item].title = val.year + '-' + val.month + '-' + val.day
					this.form[item] = val.year + '-' + val.month + '-' + val.day
				}
			})
		},
		handleShowTime(val) {
			this.typeTime = val
			this.showTime = true
		},
		addPurchase() {
			this.$refs.uForm.validate((valid) => {
				if (valid) {
					confirm({
						...this.form,
						purchaseOrderId: this.purchaseOrderId,
					}).then((res) => {
						if (res.code == 200) {
							uni.showToast({
								title: '修改成功',
								icon: 'none'
							})
							uni.navigateBack({
								delta:1
							})
						}
					})
				}
			})
		},
		erCodeShowFn(value) {
			if (!value) {
				return
			}
			this.erCodeShow = true
			this.codeContent = value
		},
		deleteImage() {
			this.form.finalAcceptanceUrl = ''
			this.$forceUpdate()
		},
		unloadImage() {
			const that = this
			uni.chooseImage({
				count: 1, //默认9
				sizeType: ['original', 'compressed'], //可以指定是原图还是压缩图，默认二者都有
				sourceType: ['album', 'camera'], //从相册选择
				name: 'file',
				success: function (res) {
					uploadFiles({
						filePath: res.tempFilePaths[0],
					}).then((data) => {
						console.log(data)
						that.form.finalAcceptanceUrl = data
						that.form.finalAcceptanceUrl != '' ? that.resetField('finalAcceptanceUrl') : ''
					})
				},
				fail(e) {},
			})
		},
		// 上传pdf
		uploadPdf(){
			let that = this; 
			uni.chooseMessageFile({
				type: 'file',
				count: 1,
				success: function (res) {
					let resFile = res.tempFiles[0]
					if(resFile.type=='file') {
						let index = resFile.name.lastIndexOf('.');
						let name = resFile.name.substring(index + 1);
						if(!that.fileType.includes(name)){
							uni.showToast({
								icon: 'none',
								title: '请上传pdf文件',
							})
							return
						} else {
							uploadFiles({
								filePath: resFile.path,
								name: resFile.name,
							}).then((data) => {
								console.log(data)
								that.form.saleContractUrl = data
								that.saleContractUrlName = resFile.name
							})
						}
					} else {
						uni.showToast({
							icon: 'none',
							title: '请上传pdf文件',
						})
					}
				},
			})
		},
		previewFiles(url){
			wx.downloadFile({
				url: url,
				success: function(res) {
					const filePath = res.tempFilePath
					wx.openDocument({
						filePath: filePath,
						fileType: 'pdf',
						success: function(res) {},
					})
				},
			})
		},
		previewImage(url) {
			uni.previewImage({
				urls: [url],
			})
		},
		resetField(value) {
			this.$refs.uForm.fields.forEach(e => {
				if (e.prop == value) {
					e.resetField()
				}
			})
		},
		changefinalAmount() {
			if (this.form.finalUnitPrice && this.form.finalWeight) {
				this.finalAmount = (Number(this.form.finalUnitPrice) * Number(this.form.finalWeight)).toFixed(2)
			}
		}
	}
}
</script>

<style lang="less" scoped>

.container {
	margin: 30rpx;
	background: #fff;
	box-sizing: border-box;
	padding: 30rpx 32rpx 40rpx;
	border-radius: 30rpx;
        /deep/ .u-form-item {
            padding: 20rpx 20rpx !important;
        }
        .tips {
            font-size: 26rpx;
            color: #ccc;
        }


        .voucher {
            padding-bottom: 40rpx;
            font-size: 28rpx;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #999999;
        }

        .all-img {
            image {
            width: 154rpx;
            height: 154rpx;
            background: #d8d8d8;
            border-radius: 16rpx;
            margin-right: 20rpx;
            }
        }

        .manyMode {
            background: transparent;
            height: auto;
        }

        .uploadImage {
            display: flex;
            flex-wrap: wrap;
            // justify-content: space-between;
            // align-content: space-between;
            position: relative;

            .itemAlready {
            width: 140rpx;
            height: 140rpx;
            border-radius: 8rpx;
            position: relative;
            margin: 0 20rpx 10rpx 0rpx;

            image {
                width: 100%;
                height: 100%;
                border-radius: 8rpx;
            }

            .closeIcon {
                width: 32rpx;
                height: 32rpx;
                background-image: url('../../../static/modalImg/error.png');
                position: absolute;
                background-size: cover;
                top: -10rpx;
                right: -10rpx;
            }
        }

        .item {
			width: 140rpx;
			height: 140rpx;
			border-radius: 8rpx;
			position: relative;
			border: 2rpx dashed #d8d8d8;

			.uploadIcon {
				width: 100%;
				height: 120rpx;
				display: flex;
				justify-content: center;
				align-items: flex-end;
				font-size: 24rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #d8d8d8;
				background: url('https://fengtaohui.oss-cn-beijing.aliyuncs.com/uploads/image/20230105/6400955ab08a2f639e9790b815c418db63b666a9d7e40.png') no-repeat;
				background-size: 20rpx 20rpx;
				background-position: center 30rpx;
				position: absolute;
				top: 0;
				left: 0;
				bottom: 0;
				right: 0;
				margin: auto;
				z-index: 5;
			}
        }
    }
}


	.scroll-view {
		overflow-y: scroll;
	}

	uni-picker-view {
		display: block;
	}

	uni-picker-view .uni-picker-view-wrapper {
		display: flex;
		align-items: center;
		position: relative;
		overflow: hidden;
		height: 100%;
		background-color: white;
	}

	uni-picker-view[hidden] {
		display: none;
	}

	picker-view {
		width: 100%;
		// height: 600upx;
		height: 600rpx;
		margin-top: 20upx;
	}

	.item {
		line-height: 100upx;
		text-align: center;
	}

	.popup-view {
		.picker-btn {
			display: flex;
			justify-content: space-between;
			padding: 30rpx 40rpx;

			.left {
				color: #999;
				font-size: 28rpx;
				font-family: PingFang SC-Medium;
			}

			.middle {
				font-size: 32rpx;
				color: #222;
				font-family: PingFang SC-Heavy;
			}

			.right {
				color: #40CA8F;
				font-size: 32rpx;
				font-family: PingFang SC-Medium;
			}
		}
	}
    .scroll-view{
        background-color: #fff;
    }
    .submit{
        width: 100%;
        height: 80rpx;
        color: #fff;
        text-align: center;
        line-height: 80rpx;
        font-size: 32rpx;
        padding: 0 20rpx;
        margin-top: 40rpx;
        view{
            width: 100%;
            height: 80rpx;
            border-radius: 40rpx;
            background-color: #40CA8F;
        }
    }

	.container-footer {
		width: 100%;
		position: fixed;
		bottom: 0;
		background-color: #f7f7f7;
		color: #f7f7f7;
		z-index: 100;
	}
	.searchs{
		display: flex;
		justify-content: space-between;
		padding: 20rpx;
		.search{
			flex: 1;
		}
		.fifter{
			width: 100rpx;
			height: 60rpx;
			background-color: #40CA8F;
			color: white;
			text-align: center;
			line-height: 60rpx;
			border-radius: 30rpx;
		}
	}
	.popup-view{
		min-height: 600rpx;
		padding-top: 20rpx;
	}
</style>