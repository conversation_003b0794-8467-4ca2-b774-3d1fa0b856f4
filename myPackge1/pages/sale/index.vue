<template>
    <view>
		<CustomNavbar  :bgColor="'#08BA7E'" :titleColor="'#FFFFFF'" />
        <view class="header">
            <view class="searchs_section">
                <view class="search_box"> 
                    <view class="search">
                        <u-search v-model="searchText" @search="handleSearch" placeholder="请输入需求方" bgColor="#FFFFFF"
                            placeholderColor="#A5B2AC" :use-action-icon="true" searchIconSize="30rpx"
                            searchIcon="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/statistical/sousuo.png"
                            :show-action="false">
                        </u-search>
                    </view>
                    <view class="fifter">
                        <img src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/xiaoshouhetong/shaixuan.png" alt="" @click="fifterClick" />
                    </view>
                </view>
            </view>
        </view>

        <scroll-view class="main" scroll-y :scroll-with-animation="true"
            @scrolltolower="scrollToLower" refresher-enabled :refresher-triggered="refresherState"
            @refresherrefresh="bindrefresherrefresh" :scroll-top="scrollTop">
            <template v-if="list && list.length > 0">
                <section v-for="(item,index) in list" :key="index">
                    <view class="list-section" >
                        <view class="items" style="width:100%'">
                            <view class="item_title" @click="detail(item)">
                                <view class="title">{{item.purchaseOrderCode}}</view>
                                <text class="status">{{ item.showStatusName }}</text>
                            </view>
                            <view class="item_content">
                                <p class="item">需求方：{{item.customerCompanyName}}(<text class="text link" @click="contractDetail(item)">{{ item.saleContractCode }}</text>)</p>
                                <p class="item">关联周订单：<text class="text link" @click="demandOrderDetail(item)">{{item.demandOrderCode}}</text></p>
                                <p class="item" @click="detail(item)">交易日期：<text class="text">{{item.finalTradeTime || ''}}</text></p>
                                <p class="item" @click="detail(item)">活畜品种：<text class="text">{{ item.varietiesName }}-{{ item.categoryName }}</text></p>
                                <p class="item" @click="detail(item)">活牛单价：<text class="text">{{ item.finalUnitPrice }} 元/kg</text></p>
                                <p class="item" @click="detail(item)">活畜数量：<text class="text">{{ item.finalNumber  || ''}}头</text></p>
                                <p class="item" @click="detail(item)">交易总价：<text class="text">{{ item.finalAmount  || ''}}元</text></p>
                                <p class="item" @click="detail(item)">款项说明：<text class="text">{{ item.paymentDetail  || ''}}</text></p>
                            </view>
                            <view class="list_btn_items" v-if="((item.purchaseOrderStatus >= 40 && item.purchaseOrderStatus <= 50) && $hasPermi('nmb:saleOrder:confirmOrder')) ||(item.purchaseOrderStatus == 50 && $hasPermi('nmb:financeOrder:receive'))">
                                <view class="btn_section">
                                    <view class="btn_item" @click="edit(item)" v-if="(item.purchaseOrderStatus >= 40 && item.purchaseOrderStatus <= 50) && $hasPermi('nmb:saleOrder:confirmOrder')" >
                                        <img class="icon" src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/xiaoshouhetong/dingdan.png" alt="">
                                        <text>调整订单</text>
                                    </view>
                                    <view class="btn_item" @click="repayment(item)" v-if="item.purchaseOrderStatus == 50 && $hasPermi('nmb:financeOrder:receive')" >
                                        <img class="icon" src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/caigou/feiyong.png" alt="">
                                        <text>确认收款</text>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </view>
                </section>
            </template>
            <template v-else>
                <u-empty text="暂无销售订单" mode="data" :icon-size='150'></u-empty>
            </template>
        </scroll-view>
        <FilterList :pickerFilterShow="pickerFilterShow" @canel="close" @close="close" @submitForm="submitForm" />
        <u-modal v-model="showRepayment" content="确认后无法撤回，请确认是否收到款项" confirmColor="#40CA8F" show-cancel-button @confirm="handleRepayment" @cancel="showRepayment = false"></u-modal>
    </view>
</template>

<script>

import FilterList from "./filterList/index.vue"
import { saleOrderPage, receipt } from "@/api/pages/sale.js"    
import CustomNavbar from '@/components/uni-custom-navbar/CustomNavbar.vue'

export default {
    components: {
        CustomNavbar,
        FilterList
    },
    name: '',
    data() {
        return {
            refresherState: false,
            noMore: false,
            isEmpty: false,
            scrollTop: 0,
            isIphonex: getApp().globalData.systemInfo.isIphonex,
            pickerFilterShow: false,
            showRepayment: false,
            list: [],
            pageNum: 1,
            pageSize: 10,
            searchText: '',
            filters: {
                pageNum: 1,
                pageSize: 10
            },
            demandOrderId: '',
            purchaseOrderId: ''
        }
    },
    onLoad(opation) {
        // 接收路由参数
        this.demandOrderId = opation.demandOrderId
    },
    onShow() {
        this.getList()
    },
    methods: {
        getList(val) { 
            uni.showLoading({
                title: '加载中',
				icon: 'none'
			})
            let params ={}
            if(val) {
                if(this.searchText) {
                    val.searchValue = this.searchText;
                    this.filters.searchValue = this.searchText;
                }else {
                    delete val.searchValue
                    delete this.filters.searchValue
                }
                params =  this.searchText ? val : {...this.filters,...val}
            }
            
            saleOrderPage({
                pageNum: 1,
                pageSize: 10,
                ...params,
                demandOrderId: this.demandOrderId
            }).then(response=>{
                if(response.code === 200 ) {
                    let list =response.result?.list || [];
                    let total = response.result?.total || 0;
                    if(this.pageNum >=2) {
                        this.list = this.list.concat(list);
                        this.list.length >= total ? this.noMore = true : this.noMore = false;
                    }else {
                        if(total >=1) {
                            this.isEmpty = false;
                            this.list = list;
                            this.list.length >= total ? this.noMore = true : this.noMore = false;
                        }else {
                            this.isEmpty = true
                        }
                        
                    }
                }
            })
            uni.hideLoading()
        },        // 搜索
        fifterClick() {
            this.searchText = ''
            this.pickerFilterShow = true
		},
        handleSearch() {
            let params = this.getPamams();
            this.pageNum = 1;
            params.pageNum= this.pageNum
            params.searchValue = this.searchText;
            this.filters.pageNum = this.pageNum;
            this.filters.searchValue = this.searchText;
            this.getList(params);
        },
        submitForm(val) {
            this.pageNum = 1
            val.pageNum = this.pageNum;
            val.pageSize= this.pageSize;
            this.filters = val;
            this.list = [];
            this.getList(val);
            this.pickerFilterShow = false;
		},
        scrollToLower() {
			if (this.noMore) return;
            this.pageNum++;
            this.isParams();
		},
        bindrefresherrefresh() {
			this.refresherState = true;
			this.pageNum = 1;
			this.noMore = false;
            this.isParams();
			setTimeout(() => {
				this.refresherState = false;
                this.$toast('刷新成功')
			}, 1000);
		},
        getPamams() {
			let params = {
				pageNum: this.pageNum,
				pageSize: this.pageSize
			}
			return params
		},
        isParams() {
            let params = this.getPamams();
            this.getList(params);
        },
        detail(item, type) {
            uni.navigateTo({
                url:  `/myPackge1/pages/sale/detail?purchaseOrderId=${item.purchaseOrderId}`
            })
        },
        edit(item) {
            uni.navigateTo({
                url:  `/myPackge1/pages/sale/form?purchaseOrderId=${item.purchaseOrderId}`
            })
        },
        detailList(item) {
            uni.navigateTo({
                url:  `/myPackge1/pages/transport/detailList?purchaseOrderId=${item.purchaseOrderId}`
            })
        },
        contractDetail(item) {
            uni.navigateTo({
                url: `/myPackge1/pages/salesContract/contractDetail?saleContractId=${item.saleContractId}`
            })
        },
        demandOrderDetail(item) {
            uni.navigateTo({
                url: '/myPackge1/pages/weeklyOrders/detail?demandOrderId=' + item.demandOrderId
            })
        },
        repayment(item) {
            this.showRepayment = true
            this.purchaseOrderId = item.purchaseOrderId
        },
        handleRepayment() {
            receipt({
                purchaseOrderId: this.purchaseOrderId
            }).then(res => {
                if (res.code == 200) {
                    this.showRepayment = false
                    this.getList()
                }
            })
        }
    },
}
</script>

<style scoped lang="scss">
@import "@/common/css/listItem.scss";
.header{
    width: 750rpx;
    height: 727rpx;
    display: flex;
    box-sizing: border-box;
    position: relative;
    background: url(https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/header_bg/xiaoshoudingdan.png) no-repeat 100% 100%;
    background-size: 100% 100%;
    position: relative;
}
.searchs_section{
    margin-top: 349rpx;
}
.main{
    margin-top: -262rpx;
}
.link{
    color: #EA501E !important;
}
</style>