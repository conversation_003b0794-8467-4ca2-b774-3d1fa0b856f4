<template>
    <view class="main">
        <view class="tabs">
            <view class="tabs-item" :class="{ 'active': currentItem == 1 }" @click="selectItem(1)">甄选</view>
            <view class="tabs-item" :class="{ 'active': currentItem == 2 }" @click="selectItem(2)">控槽</view>
            <view class="tabs-item" :class="{ 'active': currentItem == 3 }" @click="selectItem(3)">检疫</view>
            <view class="tabs-item" :class="{ 'active': currentItem == 4 }" @click="selectItem(4)">收购</view>
            <view class="tabs-item" :class="{ 'active': currentItem == 5 }" @click="selectItem(5)">检查</view>
            <view class="tabs-item" :class="{ 'active': currentItem == 6 }" @click="selectItem(6)">整车</view>
        </view>
        <view class="content">
            <Acquisition v-if="currentItem == 1" :dataList='dataInfo.prepareFarmersList || []'></Acquisition>
            <Control v-if="currentItem == 2" :dataList='dataInfo.prepareFarmersList || []'></Control>
            <Quarantine v-if="currentItem == 3" :dataInfo='dataInfo'></Quarantine>
            <Select v-if="currentItem == 4" :dataList='dataInfo.prepareFarmersList || []'></Select>
            <Inspect v-if="currentItem == 5" :dataInfo='dataInfo'></Inspect>
            <Vehicle v-if="currentItem == 6" :dataInfo='dataInfo'></Vehicle>
        </view>
    </view>
</template>

<script>
import Acquisition from './acquisition.vue'
import Control from './control.vue'
import Quarantine from './quarantine1.vue'
import Select from './select.vue'
import Inspect from './inspect.vue'
import Vehicle from './vehicle.vue'
export default {
    components: {
        Select,
        Control,
        Quarantine,
        Acquisition,
        Inspect,
        Vehicle
    },
    data() {
        return {
            currentItem: 1,
        }
    },
    props: {
        dataInfo: Object
    },
    methods: {
        selectItem(index) {
            this.currentItem = index
        }
    }
}
</script>

<style lang="scss" scoped>
.main {
    display: flex;
}
.tabs{
    width: 124rpx;
    background-color: #f5f5f5;
    display: flex;
    flex-direction: column;
    margin: 0 20rpx 20rpx 20rpx;
    border-radius: 10rpx;
    .tabs-item{
        width: 100%;
        height: 74rpx;
        line-height: 74rpx;
        text-align: center;
        background-color: #f5f5f5;
        color: #333333;
        font-size: 26rpx;
    }
    .active{
        background-color: #fff;
        color: #1DB17A;
    }
    /deep/ .u-collapse-title{
    font-size: 30rpx !important;
    font-weight: 500 !important;
}
}
.content{
    flex: 1;
    padding-left: 20rpx;
    padding-bottom: 30rpx;
}
</style>