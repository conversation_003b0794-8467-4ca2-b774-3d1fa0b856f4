<template>
    <view class="main">
        <u-popup v-model="showPopup" mode="bottom" @cancle="showPopup = false" height="80%">
            <view class="popup_main">
                <view class="picker-btn">
                    <view class="left" @click="showPopup=false">取消</view>
                    <view class="right" @click="selectContract">确定</view>
                </view>
                <scroll-view scroll-y="true" class="scroll-view">
                    <view class="list-item" v-for="(item, index) in contractList" :key="index">
                        <view class="checkbox">
                            <u-checkbox shape="circle" active-color="#40CA8F" @change="checkboxChange(item, index)" v-model="item.check"></u-checkbox>
                        </view>
                        <view>
                            <view class="title" >{{ item.saleContractCode }}</view>
                            <view class="content">
                                <p>需求方：{{ item.companyName }}</p>
                                <p>项目经理：{{ item.projectManagerName }}</p>
                                <p>合同期限：{{ item.contractStartTime }}-{{ item.contractEndTime }}</p>
                            </view>
                        </view>
                    </view>
                </scroll-view>
            </view>
        </u-popup>
    </view>
</template>

<script>
import { saleContractList } from '@/api/pages/salesContract'
export default {
    data() {
        return {
            showPopup: false,
            contractList: [],
            currentContract: {}
        }
    },
    props: {
        contractPopupShow: {
            type: Boolean,
            default: false
        }
    },
    watch: {
        contractPopupShow: {
            handler(newValue, oldValue) {
                console.log(newValue, 'newValue')
                this.showPopup = newValue
            },
            immediate: true,
            deep: true
        }
    },
    created() {
        this.getContractList()
    },
    methods: {
        selectContract() {
            this.$emit('selectContract', this.currentContract)
            this.showPopup = false
        },
        getContractList() {
            saleContractList({
                contractStatus: 1
            }).then(res => {
                this.contractList = res.result.map(item => ({
                    ...item,
                    check: false
                }))
            })
        },
        checkboxChange(item, index) {
            const contractList = this.contractList.map(item => ({ ...item, check: false }));
            item.check = true
            contractList[index] = item
            this.contractList = contractList
            this.currentContract = item
        }
    }
}
</script>

<style lang="scss" scoped>
.popup_main{
    height: 80%;
}
.picker-btn {
    display: flex;
    justify-content: space-between;
    padding: 30rpx 40rpx;

    .left {
        color: #999;
        font-size: 28rpx;
        font-family: PingFang SC-Medium;
    }
    .right {
        color: #40CA8F;
        font-size: 32rpx;
        font-family: PingFang SC-Medium;
    }
}
.list-item{
    padding: 20rpx 40rpx;
    border-bottom: 1px solid #E5E5E5;
    display: flex;
    align-items: center;
    .checkbox{
        width: 30rpx;
        margin-right: 20rpx;
    }
    .title{
        font-size: 32rpx;
        font-weight: bold;
    }
    .content{
        margin-top: 20rpx;
        font-size: 28rpx;
        color: #666;
        line-height: 46rpx;
    }
}
</style>