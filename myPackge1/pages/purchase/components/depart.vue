<template>
    <view>
        <view class="list-item">
            <p>承运人：<span>{{ dataInfo.driverName }}</span></p>
            <p>联系电话：<span>{{ dataInfo.driverPhone }}</span></p>
            <p>车牌号：<span>{{ dataInfo.licensePlateNumber }}</span></p>
            <p>实际发车时间：<span>{{ dataInfo.goTime || '' }}</span></p>
            <p>发车方向：<span>{{ dataInfo.startEndPlace || '' }}</span></p>
            <p>预计抵达时间：<span>{{ dataInfo.deliveryEndTime || '' }}</span></p>
            <p>运载数量：<span>{{ dataInfo.deliveryNumber || '' }}头</span></p>
            <p>确认人：<span>{{ dataInfo.goConfirmUserName || '' }}</span></p>
        </view>
    </view>
</template>

<script>
export default {
    data() {
        return {
        }
    },
    props: {
        dataInfo: {
            type: Object,
            default: () => {
                return {}
            }
        }
    },
    methods: {
    }
}
</script>

<style lang="scss" scoped>
.list-item{
    line-height: 50rpx;
    p{
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 26rpx;
        color: #999999;
        line-height: 60rpx;
        padding: 0 26rpx;
        font-weight: 400;
        // margin-bottom: 8rpx;
        span{
            color: #333333;
            font-size: 26rpx;
            font-weight: 400;
            text-align: right;
            min-width: 200rpx;
            word-break: break-all;
        }
    }
    .file_box{
        p{
            color: #999999;
            margin-bottom: 10rpx;
            font-size: 26rpx;
        }
        .file{
            display: flex;
            flex-wrap: wrap;
            img, video{
                width: 160rpx;
                height: 160rpx;
                margin-right: 10rpx;
                margin-bottom: 10rpx;
                border-radius: 20rpx;
            }
        }
    }
    .link{
        color: #2B6BFE !important;
        border-bottom: 1rpx solid #2B6BFE;
    }
}
</style>