<template>
    <view style="padding-bottom: 30rpx">
        <view class="item">
            <!-- <p>活畜数量<span>{{ dataInfo.deliveryNumber }}</span></p> -->
            <!-- <p>打标记数量<span>{{ dataInfo.body }}</span></p> -->
            <p>牛经纪：<span>{{ dataInfo.brokerName }}</span></p>
            <p>品控员：<span>{{ dataInfo.qualityUserName }}</span></p>
            <p>检疫日期：<span>{{ dataInfo.quarantineTime }}</span></p>
            <view class="file_box">
                <p>检疫证：</p>
                <view class="file">
                    <img class="icon" v-for="(item, index) in quarantineUrl" :key="index" @click="previewImage(item)" :src='item' />
                </view>
            </view>
        </view>
    </view>
</template>

<script>
import { getStorage } from '@/common/utils/storage.js'
export default {
    data() {
        return {
            showVideo: false,
            dataInfo: {},
            quarantineUrl: []
        }
    },
    mounted() {
        this.dataInfo = getStorage('purchaseOrderDataInfo')
        this.getData()
    },
    methods: {
        getData() {
            this.quarantineUrl = this.dataInfo.quarantineUrl ? this.dataInfo.quarantineUrl.split(',') : []
        },
        previewImage(url) {
            uni.previewImage({
                urls: [url],
            })
        }
    }
}
</script>

<style lang="scss" scoped>
.item{
    line-height: 50rpx;
    p{
        white-space: wrap;
        display: flex;
        justify-content: space-between;
        font-size: 26rpx;
        color: #999999;
        line-height: 60rpx;
        padding-right: 20rpx;
        font-weight: 400;
        span{
            color: #333333;
        }
    }
    .file_box{
        p{
            color: #999999;
            margin-bottom: 10rpx;
            font-size: 26rpx;
        }
        .file{
            display: flex;
            flex-wrap: wrap;
            img, video{
                width: 160rpx;
                height: 160rpx;
                margin-right: 10rpx;
                margin-bottom: 10rpx;
                border-radius: 20rpx;
            }
        }
    }
    .link{
        color: #2B6BFE !important;
        border-bottom: 1rpx solid #2B6BFE;
    }
}
</style>