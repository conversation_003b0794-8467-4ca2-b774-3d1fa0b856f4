<template>
    <view style="padding-bottom: 30rpx">
        <u-collapse>
            <u-collapse-item :title="item.farmersName" v-for="(item, index) in dataList" :key="index">
                <view class="item">
                    <p>养殖户联系电话：<span>{{ item.farmersPhone }}</span></p>
                    <p>采购单价：<span v-if="item.purchaseUnitPrice">{{ item.purchaseUnitPrice }}</span></p>
                    <p>采购数量：<span v-if="item.purchaseNumber">{{ item.purchaseNumber }}头</span></p>
                    <p>采购重量：<span v-if="item.purchaseWeight">{{ item.purchaseWeight }}kg</span></p>
                    <view class="file_box">
                        <p>空车视频：</p>
                        <view class="file">
                            <video
                                :controls="false"
                                :show-fullscreen-btn="false"
                                :show-play-btn="false"
                                :show-center-play-btn="false"
                                @click="showVideoFn(item)"
                                v-for="(item, index) in item.tareVideoCopy" :key="index"
                                class="icon" :src="item" />
                        </view>
                    </view>
                    <view class="file_box">
                        <p>空车照片：</p>
                        <view class="file">
                            <img v-for="(item, index) in item.tareWeightingUrlCopy" :key="index" @click="previewImage(item)" class="icon" :src="item" />
                        </view>
                    </view>
                    <view class="file_box">
                        <p>装车视频：</p>
                        <view class="file">
                            <video
                                :controls="false"
                                :show-fullscreen-btn="false"
                                :show-play-btn="false"
                                :show-center-play-btn="false"
                                @click="showVideoFn(item)" 
                                v-for="(item, index) in item.loadingVideoCopy" :key="index"
                                class="icon" :src="item" />
                        </view>
                    </view>
                    <view class="file_box">
                        <p>装车照片：</p>
                        <view class="file">
                            <img v-for="(item, index) in item.loadingUrlCopy" :key="index" @click="previewImage(item)" class="icon" :src="item" />
                        </view>
                    </view>
                    <view class="file_box">
                        <p>满车过磅视频：</p>
                        <view class="file">
                            <video
                                :controls="false"
                                :show-fullscreen-btn="false"
                                :show-play-btn="false"
                                :show-center-play-btn="false"
                                @click="showVideoFn(item)" 
                                v-for="(item, index) in item.grossVideoCopy" :key="index"
                                class="icon" :src="item" />
                        </view>
                    </view>
                    <view class="file_box">
                        <p>满车过磅照片：</p>
                        <view class="file">
                            <img v-for="(item, index) in item.grossWeightingUrlCopy" :key="index" @click="previewImage(item)" class="icon" :src="item" />
                        </view>
                    </view>
                    <p>采购合同：<span class="link" v-if="item.contractUrl" @click="downloadFile(item.contractUrl)">点击查看</span></p>
                    <p>养殖承诺书：<span class="link" v-if="item.shortId" @click="showCommitment(item)">点击查看</span></p>
                </view>
            </u-collapse-item>
        </u-collapse>
        <u-popup v-model="showVideo" mode="center" @close="showVideo = false">
			<view class="video-box" v-if="showVideo">
                <video :src="videoUrl" controls autoplay loop muted/>
            </view>
		</u-popup>
        <u-popup v-model="showPopup" height='80%'  mode="bottom"  border-radius="14" closeable="true" @close="showPopup = false">
            <scroll-view class="popup-content" :scroll-y="true" :show-scrollbar="false" v-if="showPopup">
                <Commitment :currentShortId="shortId"></Commitment>
            </scroll-view>
        </u-popup>
    </view>
</template>

<script>
import { getStorage } from '@/common/utils/storage.js'
import Commitment from '../../../../pages/commitment/index.vue'
export default {
    components: {
        Commitment
    },
    data() {
        return {
            showVideo: false,
            videoUrl: '',
            dataList: [],
            showPopup: false,
            shortId: ''
        }
    },
    mounted() {
        this.dataList = getStorage('purchaseOrderDataInfo').prepareFarmersList
        this.getData()
    },
    methods: {
        getData() {
            this.dataList.map(item => {
                item.tareVideoCopy = item.tareVideo ? item.tareVideo.split(',') : []
                item.tareWeightingUrlCopy = item.tareWeightingUrl ? item.tareWeightingUrl.split(',') : []
                item.loadingVideoCopy = item.loadingVideo ? item.loadingVideo.split(',') : []
                item.loadingUrlCopy = item.loadingUrl ? item.loadingUrl.split(',') : []
                item.grossVideoCopy = item.grossVideo ? item.grossVideo.split(',') : []
                item.grossWeightingUrlCopy = item.grossWeightingUrl ? item.grossWeightingUrl.split(',') : []
                return item
            })
        },
        showVideoFn(item) {
            console.log('播放视频');
            this.videoUrl = item
            this.showVideo = true
        },
        previewImage(url) {
            uni.previewImage({
                urls: [url],
            })
        },
        downloadFile(url) {
            uni.downloadFile({
                url,
                success: (res) => {
                    if (res.statusCode === 200) {
                        console.log('文件下载成功', res.tempFilePath);
                        uni.openDocument({
                            filePath: res.tempFilePath,
                            showMenu: true,
                            success: function (res) {
                            }
                        });
                    }
                }
            });
        },
        showCommitment(item) {
            console.log(item, item.shortId)
            this.showPopup = true
            this.shortId = item.shortId
        }
    }
}
</script>

<style lang="scss" scoped>
.item{
    line-height: 50rpx;
    p{
        white-space: wrap;
        display: flex;
        justify-content: space-between;
        font-size: 26rpx;
        color: #999999;
        line-height: 60rpx;
        padding-right: 20rpx;
        font-weight: 400;
        span{
            width: 280rpx;
            color: #333333;
            text-align: right;
            font-size: 26rpx;
        }
    }
    .file_box{
        p{
            color: #999999;
            margin-bottom: 10rpx;
            font-size: 26rpx;
        }
        .file{
            display: flex;
            flex-wrap: wrap;
            img, video{
                width: 160rpx;
                height: 160rpx;
                margin-right: 10rpx;
                margin-bottom: 10rpx;
                border-radius: 20rpx;
            }
        }
    }
    .link{
        color: #1DB17A !important;
        font-size: 26rpx;
    }
}
</style>