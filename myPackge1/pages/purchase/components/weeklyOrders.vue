<template>
    <view class="main">
        <u-popup v-model="showPopup" mode="bottom" @cancle="showPopup = false" height="80%">
            <view class="popup_main">
                <view class="picker-btn">
                    <view class="left" @click="showPopup=false">取消</view>
                    <view class="right" @click="selectContract">确定</view>
                </view>
                <scroll-view scroll-y="true" class="scroll-view">
                    <view class="list-item" v-for="(item, index) in demandOrderList" :key="index">
                        <view class="checkbox">
                            <u-checkbox shape="circle" active-color="#40CA8F" @change="checkboxChange(item, index)" v-model="item.check"></u-checkbox>
                        </view>
                        <view>
                            <view class="title" >{{ item.demandOrderCode }}</view>
                            <view class="content">
                                <p>需求方：{{ item.companyName }}</p>
                                <p>交货时间：{{ item.deliveryStartTime }} - {{ item.deliveryEndTime }}</p>
                                <p>活畜品种：{{ item.varietiesName }}-{{ item.categoryName }}</p>
                                <p>活牛单价：{{ item.unitPrice }} 元/kg</p>
                            </view>
                        </view>
                    </view>
                </scroll-view>
            </view>
        </u-popup>
    </view>
</template>

<script>
import { demandOrderPage } from '@/api/pages/demandOrder'
export default {
    data() {
        return {
            showPopup: false,
            demandOrderList: [],
            currentContract: {}
        }
    },
    props: {
        weeklyOrdersShow: {
            type: Boolean,
            default: false
        },
        saleContractId: {
            type: String,
            default: ''
        }
    },
    watch: {
        weeklyOrdersShow: {
            handler(newValue, oldValue) {
                console.log(newValue, 'newValue')
                this.showPopup = newValue
                if (newValue) {
                    this.getDemandOrderList()
                }
            },
            immediate: true,
            deep: true
        }
    },
    methods: {
        selectContract() {
            this.$emit('selectOrder', this.currentOrder)
            this.showPopup = false
        },
        getDemandOrderList() {
            demandOrderPage({
                pageNum: 1,
                pageSize: 1000,
                saleContractId: this.saleContractId
            }).then(res => {
                this.demandOrderList = res.result.list.map(item => ({
                    ...item,
                    check: false
                }))
            })
        },
        checkboxChange(item, index) {
            const demandOrderList = this.demandOrderList.map(item => ({ ...item, check: false }));
            item.check = true
            demandOrderList[index] = item
            this.demandOrderList = demandOrderList
            this.currentOrder = item
        }
    }
}
</script>

<style lang="scss" scoped>
.popup_main{
    height: 80%;
}
.picker-btn {
    display: flex;
    justify-content: space-between;
    padding: 30rpx 40rpx;

    .left {
        color: #999;
        font-size: 28rpx;
        font-family: PingFang SC-Medium;
    }
    .right {
        color: #40CA8F;
        font-size: 32rpx;
        font-family: PingFang SC-Medium;
    }
}
.list-item{
    padding: 20rpx 40rpx;
    border-bottom: 1px solid #E5E5E5;
    display: flex;
    align-items: center;
    .checkbox{
        width: 30rpx;
        margin-right: 20rpx;
    }
    .title{
        font-size: 32rpx;
        font-weight: bold;
    }
    .content{
        margin-top: 20rpx;
        font-size: 28rpx;
        color: #666;
        line-height: 46rpx;
    }
}
</style>