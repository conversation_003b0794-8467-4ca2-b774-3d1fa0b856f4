<template>
    <view style="padding:0 20rpx 30rpx">
        <u-collapse>
            <u-collapse-item :title="item.maintenanceTime" v-for="(item, index) in dataList" :key="index">
                <view class="list-item">
                    <p>保养人：<span>{{ item.driverName }}</span></p>
                    <p>保养地点：<span>{{ item.maintenanceAddress }}</span></p>
                    <p>活畜数量差额：<span>{{ item.livestockNumberDifference }}头</span></p>
                    <p>异常说明：<span>{{ item.exceptionDescription }}</span></p>
                    <view class="file_box">
                        <p>车辆照片：</p>
                        <view class="file">
                            <img class="icon" v-for="(i, index) in item.carImageUrlCopy" :key="index" @click="previewImage(i)" :src="i" />
                        </view>
                    </view>
                    <view class="file_box">
                        <p>绑绳视频：</p>
                        <view class="file">
                            <video
                                :controls="false"
                                :show-fullscreen-btn="false"
                                :show-play-btn="false"
                                :show-center-play-btn="false"
                                @click="showVideoFn(i)" 
                                v-for="(i,index) in item.bindRopeVideoCopy" :key="index"
                                class="icon" :sre="i" />
                        </view>
                    </view>
                    <view class="file_box">
                        <p>保养视频：</p>
                        <view class="file">
                            <video
                                :controls="false"
                                :show-fullscreen-btn="false"
                                :show-play-btn="false"
                                :show-center-play-btn="false"
                                @click="showVideoFn(i)" 
                                v-for="(i,index) in item.maintenanceVideoCopy" :key="index"
                                class="icon" :sre="i" />
                        </view>
                    </view>
                </view>
            </u-collapse-item>
        </u-collapse>
        <u-popup v-model="showVideo" mode="center" @close="showVideo = false">
			<view class="video-box" v-if="showVideo">
                <video :src="videoUrl" controls autoplay loop muted/>
            </view>
		</u-popup>
    </view>
</template>

<script>
import { getStorage } from '@/common/utils/storage.js'
export default {
    data() {
        return {
            showVideo: false,
            videoUrl: '',
            dataList: []
        }
    },
    mounted() {
        this.dataList = getStorage('purchaseOrderDataInfo').maintenanceList
        this.getData()
    },
    methods: {
        getData() {
            this.dataList.map(item => {
                item.carImageUrlCopy = item.carImageUrl ? item.carImageUrl.split(',') : []
                item.maintenanceVideoCopy = item.maintenanceVideo ? item.maintenanceVideo.split(',') : []
                item.bindRopeVideoCopy = item.bindRopeVideo ? item.bindRopeVideo.split(',') : []
                return item
            })
        },
        previewImage(url) {
            uni.previewImage({
                urls: [url],
            })
        },
        showVideoFn(item) {
            console.log('播放视频');
            this.videoUrl = item
            this.showVideo = true
        }
    }
}
</script>

<style lang="scss" scoped>
.list-item{
    line-height: 70rpx;
    p{
        white-space: wrap;
        display: flex;
        justify-content: space-between;
        font-size: 26rpx;
        color: #999999;
        line-height: 60rpx;
        padding: 0 20rpx;
        font-weight: 400;
        span{
            // width: 480rpx;
            text-align: right;
            color: #333333;
        }
    }
    .file_box{
        p{
            color: #999999;
            margin-bottom: 10rpx;
            font-size: 26rpx;
        }
        .file{
            display: flex;
            flex-wrap: wrap;
            padding: 0 20rpx;
            img, video{
                width: 160rpx;
                height: 160rpx;
                margin-right: 10rpx;
                margin-bottom: 10rpx;
                border-radius: 20rpx;
            }
        }
    }
}
</style>