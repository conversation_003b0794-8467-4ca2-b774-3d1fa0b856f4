<template>
    <view style="padding-bottom: 30rpx">
        <view class="item">
            <p>承运人：<span v-if="dataInfo.driverName">{{ dataInfo.driverName }}（{{ dataInfo.driverPhone }}）</span></p>
            <p>车牌号：<span>{{ dataInfo.licensePlateNumber }}</span></p>
            <view class="file_box">
                <p>身份证正面照片：</p>
                <view class="file">
                    <img class="icon" @click="previewImage(dataInfo.driverIdCardFrontUrl)" :src='dataInfo.driverIdCardFrontUrl' />
                </view>
            </view>
            <view class="file_box">
                <p>身份证反面照片：</p>
                <view class="file">
                    <img class="icon" @click="previewImage(dataInfo.driverIdCardBackUrl)" :src='dataInfo.driverIdCardBackUrl' />
                </view>
            </view>
            <view class="file_box">
                <p>驾驶证：</p>
                <view class="file">
                    <img class="icon" @click="previewImage(dataInfo.driversLicenseUrl)" :src='dataInfo.driversLicenseUrl' />
                </view>
            </view>
            <view class="file_box">
                <p>行驶证：</p>
                <view class="file">
                    <img class="icon" @click="previewImage(dataInfo.drivingLicenseUrl)" :src='dataInfo.drivingLicenseUrl' />
                </view>
            </view>
            <view class="file_box">
                <p>牧运通备案码：</p>
                <view class="file">
                    <img class="icon" @click="previewImage(dataInfo.filingCode)" :src='dataInfo.filingCode' />
                </view>
            </view>
            <view class="file_box">
                <p>车头带牌照片：</p>
                <view class="file">
                    <img class="icon" @click="previewImage(dataInfo.carHeadUrl)" :src='dataInfo.carHeadUrl' />
                </view>
            </view>
            <view class="file_box">
                <p>车尾带牌照片：</p>
                <view class="file">
                    <img class="icon" @click="previewImage(dataInfo.carTailUrl)" :src='dataInfo.carTailUrl' />
                </view>
            </view>
            <view class="file_box">
                <p>检疫合格证：</p>
                <view class="file">
                    <img class="icon" v-for="(item, index) in quarantineCert" :key="index" @click="previewImage(item)" :src='item' />
                </view>
            </view>
            <view class="file_box">
                <p>地方性其他手续：</p>
                <view class="file">
                    <img class="icon" v-for="(item, index) in quarantineProcedures" :key="index" @click="previewImage(item)" :src='item' />
                </view>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    data() {
        return {
            showVideo: false,
            quarantineCert: [],
            quarantineProcedures: [],
        }
    },
    props: {
        dataInfo: {
            type: Object,
            default: () => {
                return {}
            }
        }
    },
    watch: {
        dataInfo() {
            this.setUrl()
        }
    },
    created() {
        this.setUrl()
    },
    methods: {
        setUrl() {
            this.quarantineCert = this.dataInfo.quarantineCert ? this.dataInfo.quarantineCert.split(',') : []
            this.quarantineProcedures = this.dataInfo.quarantineProcedures ? this.dataInfo.quarantineProcedures.split(',') : []
        },
        previewImage(url) {
            uni.previewImage({
                urls: [url],
            })
        }
    }                                                                                                                      
}
</script>

<style lang="scss" scoped>
.item{
    line-height: 50rpx;
    p{
        white-space: wrap;
        display: flex;
        justify-content: space-between;
        font-size: 26rpx;
        color: #999999;
        line-height: 60rpx;
        padding-right: 20rpx;
        font-weight: 400;
        span{
            color: #333333;
        }
    }
    .file_box{
        p{
            color: #999999;
            margin-bottom: 10rpx;
            font-size: 26rpx;
        }
        .file{
            display: flex;
            flex-wrap: wrap;
            img, video{
                width: 160rpx;
                height: 160rpx;
                margin-right: 10rpx;
                margin-bottom: 10rpx;
                border-radius: 20rpx;
            }
        }
    }
    .link{
        color: #2B6BFE !important;
        border-bottom: 1rpx solid #2B6BFE;
    }
}
</style>