<template>
    <view style="padding-bottom: 30rpx">
        <view class="item">
            <p>销售单价：<span>{{ dataInfo.salesUnitPrice }}元</span></p>
            <p>销售数量：<span>{{ dataInfo.salesNumber }}头</span></p>
            <p>销售重量：<span >{{ dataInfo.salesWeight }}kg</span></p>
            <p>销售总价：<span>{{ dataInfo.salesAmount }}元</span></p>
            <p>需求方：<span>{{ dataInfo.customerName }}</span></p>
            <p>合同文件：<span class="link" v-if="dataInfo.saleContractUrl" @click="downloadFile(dataInfo.saleContractUrl)">点击查看</span></p>
            <p>收款确认人：<span>{{ dataInfo.receiveFinanceName || '--' }}</span></p>
            <p>收款确认时间：<span>{{ dataInfo.receiveTime || '--' }}</span></p>
        </view>
    </view>
</template>

<script>
export default {
    data() {
        return {
        }
    },
    props: {
        dataInfo: {
            type: Object,
            default: () => {
                return {}
            }
        }
    },
    methods: {
        downloadFile(url) {
            uni.downloadFile({
                url,
                success: (res) => {
                    if (res.statusCode === 200) {
                        console.log('文件下载成功', res.tempFilePath);
                        uni.openDocument({
                            filePath: res.tempFilePath,
                            showMenu: true,
                            success: function (res) {
                            }
                        });
                    }
                }
            });
        }
    }
}
</script>

<style lang="scss" scoped>
.item{
    line-height: 70rpx !important;
    margin-top: 20rpx;
    p{
        white-space: wrap;
        display: flex;
        justify-content: space-between;
        font-weight: 400;
        font-size: 26rpx;
        color: #999999;
        line-height: 60rpx;
        padding: 0 20rpx;
        span{
            color: #333333;
        }
    }
    .link{
        color: #1DB17A !important;
        font-size: 26rpx;
    }
}
</style>