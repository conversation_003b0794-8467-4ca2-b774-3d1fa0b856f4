<template>
    <view style="padding-bottom: 30rpx">
        <view class="item">
            <p>车牌号：<span>{{ dataInfo.licensePlateNumber }}</span></p>
            <p>运行方向：<span>{{ dataInfo.startEndPlace }}</span></p>
            <p>采购数量：<span v-if="dataInfo.purchaseNumber">{{ dataInfo.purchaseNumber }}头</span></p>
            <p>空车重量：<span v-if="dataInfo.tareWeight">{{ dataInfo.tareWeight }}kg</span></p>
            <p>满车重量：<span v-if="dataInfo.grossWeight">{{ dataInfo.grossWeight }}kg</span></p>
            <p>净重量：<span v-if="dataInfo.netWeight">{{ dataInfo.netWeight }}kg</span></p>
            <view class="file_box">
                <p>空车视频：</p>
                <view class="file">
                    <video
                        :controls="false"
                        :show-fullscreen-btn="false"
                        :show-play-btn="false"
                        :show-center-play-btn="false"
                        @click="showVideoFn(item)" 
                        v-for="(item,index) in tareVideo" :key="index"
                        class="icon" :src="item" />
                </view>
            </view>
            <view class="file_box">
                <p>空车照片：</p>
                <view class="file">
                    <img v-for="(item, index) in tareWeightingUrl" :key="index" @click="previewImage(item)" class="icon" :src="item" />
                </view>
            </view>
            <view class="file_box">
                <p>装车视频：</p>
                <view class="file">
                    <video
                        :controls="false"
                        :show-fullscreen-btn="false"
                        :show-play-btn="false"
                        :show-center-play-btn="false"
                        @click="showVideoFn(item)" 
                        v-for="(item,index) in loadingVideo" :key="index"
                        class="icon" :src="item" />
                </view>
            </view>
            <view class="file_box">
                <p>装车照片：</p>
                <view class="file">
                    <img v-for="(item, index) in loadingUrl" :key="index" @click="previewImage(item)" class="icon" :src="item" />
                </view>
            </view>
            <view class="file_box">
                <p>满车过磅视频：</p>
                <view class="file">
                    <video
                        :controls="false"
                        :show-fullscreen-btn="false"
                        :show-play-btn="false"
                        :show-center-play-btn="false"
                        @click="showVideoFn(item)" 
                        v-for="(item,index) in grossVideo" :key="index"
                        class="icon" :src="item" />
                </view>
            </view>
            <view class="file_box">
                <p>满车过磅照片：</p>
                <view class="file">
                    <img v-for="(item, index) in grossWeightingUrl" :key="index" @click="previewImage(item)" class="icon" :src="item" />
                </view>
            </view>
        </view>
        <u-popup v-model="showVideo" mode="center" @close="showVideo = false">
			<view class="video-box" v-if="showVideo">
                <video :src="videoUrl" controls autoplay loop muted/>
            </view>
		</u-popup>
    </view>
</template>

<script>
export default {
    data() {
        return {
            showVideo: false,
            tareVideo: [],
            tareWeightingUrl: [],
            loadingVideo: [],
            loadingUrl: [],
            grossVideo: [],
            grossWeightingUrl: [],
            videoUrl: ''
        } 
    },
    props: {
        dataInfo: {
            type: Object,
            default: () => {
                return {}
            }
        }
    },
    watch: {
        dataInfo() {
            this.setUrl()
        }
    },
    created() {
        this.setUrl()
    },
    methods: {
        setUrl() {
            this.tareVideo = this.dataInfo.tareVideo ? this.dataInfo.tareVideo.split(',') : []
            this.tareWeightingUrl = this.dataInfo.tareWeightingUrl ? this.dataInfo.tareWeightingUrl.split(',') : []
            this.loadingVideo = this.dataInfo.loadingVideo ? this.dataInfo.loadingVideo.split(',') : []
            this.loadingUrl = this.dataInfo.loadingUrl ? this.dataInfo.loadingUrl.split(',') : []
            this.grossVideo = this.dataInfo.grossVideo ? this.dataInfo.grossVideo.split(',') : []
            this.grossWeightingUrl = this.dataInfo.grossWeightingUrl ? this.dataInfo.grossWeightingUrl.split(',') : []
        },
        showVideoFn(item) {
            console.log('播放视频');
            this.videoUrl = item
            this.showVideo = true
        },
        previewImage(url) {
            uni.previewImage({
                urls: [url],
            })
        }
    }
}
</script>

<style lang="scss" scoped>
.item{
    line-height: 50rpx;
    p{
        white-space: wrap;
        display: flex;
        justify-content: space-between;
        font-size: 26rpx;
        color: #999999;
        line-height: 60rpx;
        padding-right: 20rpx;
        font-weight: 400;
        span{
            color: #333333;
        }
    }
    .file_box{
        p{
            color: #999999;
            margin-bottom: 10rpx;
            font-size: 26rpx;
        }
        .file{
            display: flex;
            flex-wrap: wrap;
            img, video{
                width: 160rpx;
                height: 160rpx;
                margin-right: 10rpx;
                margin-bottom: 10rpx;
                border-radius: 20rpx;
            }
        }
    }
    .link{
        color: #2B6BFE !important;
        border-bottom: 1rpx solid #2B6BFE;
    }
}
</style>