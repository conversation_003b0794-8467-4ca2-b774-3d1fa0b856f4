<template>
    <view style="padding-bottom: 30rpx">
        <u-collapse>
            <u-collapse-item :title="item.farmersName" v-for="(item, index) in dataList" :key="index">
                <view class="item">
                    <p>养殖户联系电话：<span>{{ item.farmersPhone }}</span></p>
                    <p>养殖场地址：<span>{{ item.farmersAddress }}</span></p>
                    <p>活畜数量：<span>{{ item.chooseNumber }}头</span></p>
                    <p>打标记数量：<span>{{ item.markNumber }}头</span></p>
                    <p>牛经纪：<span>{{ item.brokerName }}</span></p>
                    <p>品控员：<span>{{ item.qualityUserName }}</span></p>
                    <p>控槽日期：<span v-if="item.kongcaoTime">{{ item.kongcaoTime }}</span></p>
                    <p>控槽时长：<span v-if="item.kongcaoHours">{{ item.kongcaoHours }}小时</span></p>
                    <view class="file_box">
                        <p>控槽视频：</p>
                        <view class="file">
                            <video
                                :controls="false"
                                :show-fullscreen-btn="false"
                                :show-play-btn="false"
                                :show-center-play-btn="false"
                                @click="showVideoFn(item)" 
                                v-for="(item,index) in item.kongcaoVideoCopy" :key="index"
                                class="icon" :src="item" />
                        </view>
                    </view>
                </view>
            </u-collapse-item>
        </u-collapse>
        <u-popup v-model="showVideo" mode="center" @close="showVideo = false">
			<view class="video-box" v-if="showVideo">
                <video :src="videoUrl" controls autoplay loop muted/>
            </view>
		</u-popup>
    </view>
</template>

<script>
import { getStorage } from '@/common/utils/storage.js'
export default {
    data() {
        return {
            showVideo: false,
            videoUrl: '',
            dataList: []
        }
    },
    mounted() {
        this.dataList = getStorage('purchaseOrderDataInfo').prepareFarmersList
        this.getData()
    },
    methods: {
        getData() {
            this.dataList.map(item => {
                item.kongcaoVideoCopy = item.kongcaoVideo ? item.kongcaoVideo.split(',') : []
                return item
            })
        },
        showVideoFn(item) {
            this.videoUrl = item
            this.showVideo = true
        }
    }
}
</script>

<style lang="scss" scoped>
.item{
    line-height: 50rpx;
    p{
        white-space: wrap;
        display: flex;
        justify-content: space-between;
        font-size: 26rpx;
        color: #999999;
        line-height: 60rpx;
        padding-right: 20rpx;
        font-weight: 400;
        span{
            width: 280rpx;
            color: #333333;
            text-align: right;
            font-size: 26rpx;
        }
    }
    .file_box{
        p{
            color: #999999;
            margin-bottom: 10rpx;
            font-size: 26rpx;
        }
        .file{
            display: flex;
            flex-wrap: wrap;
            img, video{
                width: 160rpx;
                height: 160rpx;
                margin-right: 10rpx;
                margin-bottom: 10rpx;
                border-radius: 20rpx;
            }
        }
    }
    .link{
        color: #2B6BFE !important;
        border-bottom: 1rpx solid #2B6BFE;
    }
}
</style>