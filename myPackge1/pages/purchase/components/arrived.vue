<template>
    <view style="padding-bottom: 30rpx">
        <view class="list-item">
            <p>抵达数量：<span>{{ dataInfo.arriveNumber }}头</span></p>
            <p>运转差额：<span>{{ dataInfo.transportLossNumber }}头</span></p>
            <p>实际抵达时间：<span>{{ dataInfo.arriveTime }}</span></p>
            <p>延迟时长：<span>{{ dataInfo.delayHours }}小时</span></p>
            <view class="file_box">
                <p>抵达过磅视频：</p>
                <view class="file">
                    <video
                        :controls="false"
                        :show-fullscreen-btn="false"
                        :show-play-btn="false"
                        :show-center-play-btn="false"
                        @click="showVideoFn(item)" 
                        v-for="(item,index) in arriveGrossVideo" :key="index"
                        class="icon" :src="item" />
                </view>
            </view>
            <view class="file_box">
                <p>抵达过磅照片：</p>
                <view class="file">
                    <img @click="previewImage(item)" class="icon" :src="item" v-for="(item, index) in arriveGrossWeightingUrl" :key="index" />
                </view>
            </view>
            <p>抵达重量：<span>{{ dataInfo.arriveGrossWeight }}kg</span></p>
            <view class="file_box">
                <p>卸车视频：</p>
                <view class="file">
                    <video
                        :controls="false"
                        :show-fullscreen-btn="false"
                        :show-play-btn="false"
                        :show-center-play-btn="false"
                        @click="showVideoFn(item)"
                        v-for="(item,index) in arriveUnloadingVideo" :key="index" 
                        class="icon" :src="item" />
                </view>
            </view>
            <view class="file_box">
                <p>空载照片：</p>
                <view class="file">
                    <img @click="previewImage(item)" class="icon" v-for="(item, index) in arriveTareWeightingUrl" :key="index" :src="item" />
                </view>
            </view>
            <p>空车重量：<span>{{ dataInfo.arriveTareWeight }}kg</span></p>
            <p>空车重量差：<span>{{ dataInfo.transportTareLossWeight }}kg</span></p>
            <p>抵达净重量：<span>{{ dataInfo.arriveNetWeight }}kg</span></p>
            <p>运转重量差：<span>{{ dataInfo.transportLossWeight }}kg</span></p>
            <view class="file_box">
                <p>车头带牌照片：</p>
                <view class="file">
                    <img @click="previewImage(dataInfo.arriveCarHeadUrl)" class="icon" :src='dataInfo.arriveCarHeadUrl' />
                </view>
            </view>
            <view class="file_box">
                <p>车尾带牌照片：</p>
                <view class="file">
                    <img @click="previewImage(dataInfo.arriveCarTailUrl)" class="icon" :src='dataInfo.arriveCarTailUrl' />
                </view>
            </view>
            <p>异常说明：<span>{{ dataInfo.arriveExceptionDetail || '' }}</span></p>
        </view>
        <u-popup v-model="showVideo" mode="center" @close="showVideo = false">
			<view class="video-box" v-if="showVideo">
                <video :src="videoUrl" controls autoplay loop muted/>
            </view>
		</u-popup>
    </view>
</template>

<script>
import { getStorage } from '@/common/utils/storage.js'
export default {
    data() {
        return {
            showVideo: false,
            videoUrl: '',
            dataInfo: {},
            dataList: [],
            arriveGrossWeightingUrl: [],
            arriveGrossVideo: [],
            arriveUnloadingVideo: [],
            arriveTareWeightingUrl: [],
        }
    },
    mounted() {
        this.dataInfo = getStorage('purchaseOrderDataInfo')
        console.log(this.dataInfo)
        this.getData()
    },
    methods: {
        getData() {
            this.arriveGrossWeightingUrl = this.dataInfo.arriveGrossWeightingUrl ? this.dataInfo.arriveGrossWeightingUrl.split(',') : []
            this.arriveGrossVideo = this.dataInfo.arriveGrossVideo ? this.dataInfo.arriveGrossVideo.split(',') : []
            this.arriveUnloadingVideo = this.dataInfo.arriveUnloadingVideo ? this.dataInfo.arriveUnloadingVideo.split(',') : []
            this.arriveTareWeightingUrl = this.dataInfo.arriveTareWeightingUrl ? this.dataInfo.arriveTareWeightingUrl.split(',') : []
        },
        showVideoFn(item) {
            this.videoUrl = item
            this.showVideo = true
        },
        previewImage(url) {
            uni.previewImage({
                urls: [url],
            })
        }
    }
}
</script>

<style lang="scss" scoped>
.list-item{
    line-height: 70rpx;
    p{
        white-space: wrap;
        display: flex;
        justify-content: space-between;
        font-size: 26rpx;
        color: #999999;
        line-height: 60rpx;
        padding: 0 20rpx;
        span{
            color: #333333;
        }
    }
    .file_box{
        p{
            color: #222222;
            margin-bottom: 10rpx;
            font-size: 26rpx;
        color: #999999;
        }
        .file{
            display: flex;
            flex-wrap: wrap;
            padding: 0 20rpx;
            img, video{
                width: 160rpx;
                height: 160rpx;
                margin-right: 10rpx;
                margin-bottom: 10rpx;
                border-radius: 20rpx;
            }
        }
    }
    .link{
        color: #2B6BFE !important;
        border-bottom: 1rpx solid #2B6BFE;
    }
}
</style>