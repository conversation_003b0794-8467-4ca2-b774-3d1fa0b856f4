<template>
    <view style="padding-bottom: 30rpx">
        <view class="item" v-for="(item,index) in dataList" :key="index">
            <p>费用类型：<span>{{ settlementHash[item.costType] }}</span></p>
            <p>结算状态：<span>{{ item.settlementStatus == 1 ? '已付款' : '待付款'}}</span></p>
            <p>收款人：<span>{{ item.payeeName }}</span></p>
            <p>应付金额：<span>{{ item.payableAmount || '' }}元</span></p>
            <p>发票文件：<span class="link" v-if="item.invoiceUrl" @click="downloadFile(item.invoiceUrl)">点击查看</span></p>
            <p>费用说明：<span>{{ item.costRemark || '' }}</span></p>
            <p>付款时间：<span>{{ item.payTime|| ''  }}</span></p>
            <p>实付金额：<span>{{ item.finalAmount || '' }}元</span></p>
            <p>付款人：<span>{{ item.payerName }}</span></p>
        </view>
    </view>
</template>

<script>
export default {
    data() {
        return {
            settlementHash: {
                1: '采购货款',
                2: '运输费',
                3: '服务费',
                4: '其他费用'
            }
        }
    },
    props: {
        dataList: {
            type: Array,
            default: () => {
                return []
            }
        }
    },
    methods: {
        downloadFile(url) {
            uni.downloadFile({
                url,
                success: (res) => {
                    if (res.statusCode === 200) {
                        console.log('文件下载成功', res.tempFilePath);
                        uni.openDocument({
                            filePath: res.tempFilePath,
                            showMenu: true,
                            success: function (res) {
                            }
                        });
                    }
                }
            });
        }
    }
}
</script>

<style lang="scss" scoped>
.item{
    line-height: 50rpx;
    margin-bottom: 30rpx;
    padding: 0 15rpx;
    p{
        white-space: wrap;
        display: flex;
        justify-content: space-between;
        font-size: 26rpx;
        color: #999999;
        line-height: 60rpx;
        padding: 0 16rpx;
        font-weight: 400;
        span{
            color: #333333;
        }
    }
    .link{
        color: #1DB17A !important;
        font-size: 26rpx;
    }
}
</style>