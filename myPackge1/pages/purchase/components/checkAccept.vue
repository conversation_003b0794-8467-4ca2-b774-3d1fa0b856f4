<template>
    <view style="padding-bottom: 30rpx">
        <view class="list-item">
            <view class="title">牛源验收</view>
            <p v-for="(item,index) in dataInfo.cowOptionList" :key="index" @click="selectItem(item)">{{item.title}}：<span>{{ item.showValue }}</span></p>
        </view>
        <view class="list-item">
            <view class="title">运输验收</view>
            <p v-for="(item,index) in dataInfo.transportOptionList" :key="index">{{item.title}}：<span>{{ item.showValue }}</span></p>
        </view>
        <view class="list-item">
            <view class="title">品质验收</view>
            <p v-for="(item,index) in dataInfo.qualityOptionList" :key="index">{{item.title}}：<span>{{ item.showValue }}</span></p>
        </view>
        <view class="list-item">
            <view class="title">发票验收</view>
            <p v-for="(item,index) in dataInfo.invoiceOptionList" :key="index">{{item.title}}：<span>{{ item.showValue }}</span></p>
        </view>
        <view class="list-item">
            <view class="title">验收结论</view>
            <p>验收结果：<span>{{ dataInfo.acceptanceCount }}</span></p>
        </view>
        <view class="list-item">
            <view class="title">签字确认</view>
            <view class="file_box">
                <p>验收员：</p>
                <view class="file">
                    <img class="icon" :src="dataInfo.acceptorSignUrl" />
                </view>
            </view>
            <view class="file_box">
                <p>资方：</p>
                <view class="file">
                    <img class="icon" :src="dataInfo.supervisorSignUrl" />
                </view>
            </view>
        </view>
        <VideoPopup :videoUrl="videoUrl" :pickerFilterShow="showVideo" @closeModel="showVideo = false"></VideoPopup>
    </view>
</template>

<script>
import VideoPopup from '../../arrive/videoPopup1.vue'
export default {
    components: {
        VideoPopup
    },
    data() {
        return {
            showVideo: false,
            videoUrl: '',
        }
    },
    props: {
        dataInfo: {
            type: Object,
            default: () => {
                return {}
            }
        }
    },
    methods: {
        selectItem(item){
            if (item.videoType) {
                this.videoUrl = item.videoUrl
                this.showVideo = true
            }
        }
    }
}
</script>

<style lang="scss" scoped>
.list-item{
    line-height: 40rpx;
    margin-top: 30rpx;
    .title{
        font-size: 32rpx;
        color: #222222;
        line-height: 60rpx;
        font-weight: bold;
        padding: 0 20rpx;
    }
    p{
        white-space: wrap;
        display: flex;
        justify-content: space-between;
        font-size: 26rpx;
        color: #999999;
        line-height: 60rpx;
        padding: 0 20rpx;
        span{
            color: #333333;
        }
    }
    .file_box{
        display: flex;
        p{
            color: #222222;
            margin-bottom: 10rpx;
        }
        .file{
            display: flex;
            flex-wrap: wrap;
            img{
                width: 200rpx;
                height: 120rpx;
                margin-right: 10rpx;
                margin-bottom: 10rpx;
                border-radius: 20rpx;
                background: #999;
            }
        }
    }
}
</style>