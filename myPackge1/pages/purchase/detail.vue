<template>
    <view style="padding-bottom: 40rpx;">
        <scroll-view class="main" scroll-y>
            <block>
                <section>
                    <view class="title">
                        订单详情
                    </view>
                    <view class="list-section" >
                        <view class="middle" style="width:100%';">
                            <view class="list-item">
                                <p>订单编号：<span>{{ dataInfo.purchaseOrderCode }}</span></p>
                                <p>牛源地：<span>{{ dataInfo.cowSource }}</span></p>
                                <p>活畜品种：<span>{{ dataInfo.varietiesName }} - {{ dataInfo.categoryName }}</span></p>
                                <p>活畜月龄：<span>{{ dataInfo.ageRange }}</span></p>
                                <p>活畜重量：<span>{{ dataInfo.weightRange }}</span></p>
                                <p>订单数量：<span>{{ dataInfo.demandNumber || '--' }}{{ dataInfo.demandNumber ? '头' : '' }}</span></p>
                                <p>采购要求：<span>{{ dataInfo.demandOrderRemark }}</span></p>
                                <p>牛经纪：<span>{{ dataInfo.brokerName }}</span></p>
                                <p>承运人：
                                    <span v-if="dataInfo.driverName">{{ dataInfo.driverName }} {{ dataInfo.licensePlateNumber ? '(' + dataInfo.licensePlateNumber +')' : '' }}</span>
                                    <span v-else>--</span>
                                </p>
                                <p>计划发车时间：<span>{{ dataInfo.deliveryStartTime }}</span></p>
                                <p>预计抵达时间：<span>{{ dataInfo.deliveryEndTime }}</span></p>
                                <p>品控员：<span>{{ dataInfo.qualityUserName }}</span></p>
                                <p>验收员：<span>{{ dataInfo.acceptorName }}</span></p>
                            </view>
                        </view>
                    </view>
                    <view class="title">
                        订单进度
                        <view class="btn-box">
                            <text>{{ dataInfo.showStatusName }}</text>
                            <u-button hover-class='none' custom-style="btn" type="primary" @click="exportFile">生成word文档</u-button>
                        </view>
                    </view>
                    <view class="list-section" >
                        <view class="tabs">
                            <view @click="selectItem(10)" class="tabs-item" :class="{ 'current': currentItem == 10 }">备货</view>
                            <view @click="selectItem(20)" class="tabs-item" :class="{ 'current': currentItem == 20 }">发车</view>
                            <view @click="selectItem(30)" class="tabs-item" :class="{ 'current': currentItem == 30 }">转运</view>
                            <view @click="selectItem(40)" class="tabs-item" :class="{ 'current': currentItem == 40 }">抵达</view>
                            <view @click="selectItem(41)" class="tabs-item" :class="{ 'current': currentItem == 41 }">验收</view>
                        </view>
                        <view>
                            <Stock v-if="currentItem == 10" :dataInfo="dataInfo"></Stock>
                            <Depart v-if="currentItem == 20" :dataInfo="dataInfo"></Depart>
                            <Operate v-if="currentItem == 30" :dataInfo="dataInfo"></Operate>
                            <Arrived v-if="currentItem == 40" :dataInfo="dataInfo"></Arrived>
                            <CheckAccept v-if="currentItem == 41" :dataInfo="dataInfo.acceptanceModel"></CheckAccept>
                        </view>
                    </view>
                    <view class="title">
                        费用信息
                    </view>
                    <view class="list-section" >
                        <view class="tabs">
                            <view @click="selectItemPay(1)" class="tabs-item" :class="{ 'current': currentItemPay == 1 }">购牛款</view>
                            <view @click="selectItemPay(2)" class="tabs-item" :class="{ 'current': currentItemPay == 2 }">运输费</view>
                            <view @click="selectItemPay(3)" class="tabs-item" :class="{ 'current': currentItemPay == 3 }">服务费</view>
                            <view @click="selectItemPay(4)" class="tabs-item" :class="{ 'current': currentItemPay == 4 }">其他</view>
                        </view>
                        <view>
                            <PayMent :dataList="dataList"></PayMent>
                        </view>
                    </view>
                </section>
            </block>
        </scroll-view>
    </view>
</template>
<script>
import { purchaseOrderInfo, dynamic } from '@/api/pages/purchaseOrder'
import Stock from './components/stock.vue'
import Depart from './components/depart.vue'
import Operate from './components/operate1.vue'
import Arrived from './components/arrived.vue'
import CheckAccept from './components/checkAccept.vue'
import PayMent from './components/payMent.vue'
import { setStorage, removeStorage } from '@/common/utils/storage.js'
export default {
    components: {
        Stock,
        Depart,
        Operate,
        Arrived,
        CheckAccept,
        PayMent
    },
    data() {
        return {
            dataInfo: {},
            confirmor: '',
            currentItem:'',
            currentItemPay: 1,
            dataList: [],
        }
    },
    onLoad(opation) {
        // 接收路由参数
        this.purchaseOrderId = opation.purchaseOrderId
        this.confirmor = opation.confirmor
        removeStorage('purchaseOrderDataInfo')
        this.getInfo()
    },
    methods: {
        getInfo() { 
            uni.showLoading({
                title: '加载中',
				icon: 'none'
			})
            purchaseOrderInfo({
                purchaseOrderId: this.purchaseOrderId
            }).then(response=>{
                if(response.code === 200 ) {
                    this.dataInfo =response.result || {};
                    setStorage('purchaseOrderDataInfo', this.dataInfo)
                    this.currentItem = 10
                    this.selectItemPay(1)
                }
            })
            uni.hideLoading()
        },
        selectItem(item) {
            if(this.dataInfo.showStatus >= item) {
                this.currentItem = item
            }
        },
        selectItemPay(item) {
            switch (item) {
                case 1:
                    this.dataList = this.dataInfo.purchaseCostList
                    break;
                case 2:
                    this.dataList = this.dataInfo.transportCostList
                    break;
                case 3:
                    this.dataList = this.dataInfo.serviceCostList
                    break;
                case 4:
                    this.dataList = this.dataInfo.otherCostList
                    break;
            }
            this.currentItemPay = item
        },
        exportFile() {
            dynamic({
                id: this.purchaseOrderId
            }).then(response=>{
                if(response.code == 200 ) {
                    console.log(1)
                    this.previewFiles(response.result)
                }
            })
        },
		previewFiles(url){
			wx.downloadFile({
				url,
				success: (res) => {
                    uni.saveFile({
                        tempFilePath: res.tempFilePath, //临时路径
                        success: function (res) {
                            uni.openDocument({
                                filePath: res.savedFilePath,
                                showMenu: true,
                                success: function (res) {
                                    console.log('打开文档成功');
                                }
                            });
                        }
                    })
				},
			})
		},
    },
}
</script>

<style scoped lang="scss">
@import "@/common/css/superviseHome.scss";
.list-item p{
    white-space: normal !important;
    display: flex;
    justify-content: space-between;
    font-size: 26rpx;
    color: #999;
    line-height: 60rpx !important;
    
    span{
        width: 500rpx;
        text-align: right;
        color: #333;
        font-size: 26rpx;
        font-weight: 400;
    }
}
.title{
    list-style: 50rpx;
    font-weight: 500;
    font-size: 32rpx;
    color: #222222;
    padding: 20rpx;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .btn-box{
        display: flex;
        align-items: center;
    }
    text{
        font-weight: 400;
        font-size: 28rpx;
        color: #EA501E;
        margin-right: 20rpx;
    }
}
/deep/ .u-btn{
    width: 180rpx!important;
    height: 40rpx !important;
    font-size: 24rpx!important;
    margin-left: 10rpx;
    background: linear-gradient( 305deg, #40CA8F 0%, #1AAF77 100%);
}
.btn{
    width: 200rpx!important;
    height: 40rpx !important;
    font-size: 24rpx!important;
}
.list-section{
    flex-direction: column;
}
.tabs{
    display: flex;
    margin: 20rpx;
    background: #F7F8F7;
    border-radius: 10rpx;
    height: 64rpx;
    padding: 6rpx;
    box-sizing: border-box;
    .tabs-item{
        flex: 1;
        text-align: center;
        font-size: 26rpx;
        color: #999999;
        padding: 16rpx 20rpx;
        margin: 0 8rpx;
        border-radius: 20rpx;
        width: 118rpx;
        height: 52rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 500;
        .icon{
            font-size: 24rpx;
            margin-left: 5rpx;
        }
    }
    .current{
        background-color: #1AAF77;
        color: #FFFFFF;
    }
}
</style>