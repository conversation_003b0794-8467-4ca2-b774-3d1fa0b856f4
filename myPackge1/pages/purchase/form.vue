<template>
	<view>
        <scroll-view scroll-y :scroll-with-animation="true" class="main">
            <view class="container">
                <u-form :model="form" ref="uForm" :error-type="errorType" label-width="auto" label-position="top"  :label-style="labelStyle">
                    <u-form-item label="订单性质" v-if="!demandOrderId" required="true" prop="contractFlagName" right-icon="arrow-right">
                        <u-input :custom-style="customStyle" :placeholder-style="placeholderStyle" v-model="form.contractFlagName" placeholder="请选择订单性质" disabled/>
                        <!-- <u-input :custom-style="customStyle" :placeholder-style="placeholderStyle" v-model="form.contractFlagName" placeholder="请选择订单性质" disabled @click="handleSelect('contractFlag')"/> -->
                    </u-form-item>
                    <u-form-item label="关联合同" required="true" prop="saleContractName" right-icon="arrow-right" v-if="form.contractFlag == 1 && !demandOrderId">
                        <u-input :custom-style="customStyle" :placeholder-style="placeholderStyle" v-model="form.saleContractName" placeholder="请选择关联合同" disabled @click="contractPopupShow = true"/>
                    </u-form-item>
                    <u-form-item label="关联客户" required="true" prop="nmbCompanyName" right-icon="arrow-right" v-if="form.contractFlag == 0">
                        <u-input :custom-style="customStyle" :placeholder-style="placeholderStyle" v-model="form.nmbCompanyName" placeholder="请选择关联客户" disabled @click="handleSelect('nmbCompany')"/>
                    </u-form-item>
                    <u-form-item label="关联周订单" required="true" prop="demandOrderName" right-icon="arrow-right" v-if="form.contractFlag == 1 && !demandOrderId">
                        <u-input :custom-style="customStyle" :placeholder-style="placeholderStyle" v-model="form.demandOrderName" placeholder="请选择关联周订单" disabled @click="weeklyOrdersShow = true"/>
                    </u-form-item>
                    <u-form-item label="牛源地" required="true" prop="provinceName" right-icon="arrow-right">
                        <u-input :custom-style="customStyle" :placeholder-style="placeholderStyle" v-model="form.provinceName" placeholder="请选择牛源地" disabled @click="handleSelect('provinceName')"/>
                    </u-form-item>
                    <u-form-item label="牛经纪" required="true" prop="brokerName" right-icon="arrow-right">
                        <u-input :custom-style="customStyle" :placeholder-style="placeholderStyle" v-model="form.brokerName" placeholder="请选择牛经纪" disabled @click="handleSelect('broker')"/>
                    </u-form-item>
                    <u-form-item label="采购要求" required="true" v-if="form.contractFlag == 1">
                        <u-input :custom-style="customStyle" :placeholder-style="placeholderStyle" v-model="dataInfo.remark" placeholder=" " disabled/>
                    </u-form-item>
                    <u-form-item label="活畜品种" required="true" v-if="form.contractFlag == 1">
                        <u-input :custom-style="customStyle" :placeholder-style="placeholderStyle" v-model="dataInfo.varietiesName" placeholder=" " disabled/>
                    </u-form-item>
                    <u-form-item label="品种分类" required="true" v-if="form.contractFlag == 1">
                        <u-input :custom-style="customStyle" :placeholder-style="placeholderStyle" v-model="dataInfo.categoryName" placeholder=" " disabled/>
                    </u-form-item>
                    <u-form-item label="活畜月龄" required="true" v-if="form.contractFlag == 1">
                        <u-input :custom-style="customStyle" :placeholder-style="placeholderStyle" v-model="dataInfo.ageRange" placeholder=" " disabled/>
                    </u-form-item>
                    <u-form-item label="活畜重量" required="true" v-if="form.contractFlag == 1">
                        <u-input :custom-style="customStyle" :placeholder-style="placeholderStyle" v-model="dataInfo.weightRange" placeholder=" " disabled />
                    </u-form-item>
                    <u-form-item label="采购要求" required="true" v-if="form.contractFlag == 0" prop="demandOrderRemark">
                        <u-input :custom-style="customStyle" :placeholder-style="placeholderStyle" v-model="form.demandOrderRemark" placeholder="请输入采购要求"/>
                    </u-form-item>
                    <u-form-item label="活畜品种" required="true" v-if="form.contractFlag == 0" prop="varietiesId">
                        <view class="job-type-content">
                            <p v-for="item in categoryList" :key="item.varietiesName"
                                :class="item.varietiesId == form.varietiesId ? 'policy-type-active' : ''"
                                @click="handleActive('varietiesId', item.varietiesId, 'varietiesName', item.varietiesName)">
                                {{ item.varietiesName }}
                            </p>
                        </view>
                    </u-form-item>
                    <u-form-item label="品种分类" required="true" v-if="form.contractFlag == 0" prop="categoryId">
                        <view class="job-type-content">
                            <p v-for="item in varietiesList" :key="item.categoryName"
                                :class="item.categoryId == form.categoryId ? 'policy-type-active' : ''"
                                @click="handleActive('categoryId', item.categoryId, 'categoryName', item.categoryName)">
                                {{ item.categoryName }}
                            </p>
                        </view>
                    </u-form-item>
                    <u-form-item label="活畜月龄" required="true" v-if="form.contractFlag == 0" prop="ageRange">
                        <view class="job-type-content">
                            <p v-for="item in livestockAgeList" :key="item.dictLabel"
                                :class="item.dictLabel == form.ageRange ? 'policy-type-active' : ''"
                                @click="handleActive1('ageRange', item.dictLabel)">
                                {{ item.dictLabel }}
                            </p>
                        </view>
                    </u-form-item>
                    <u-form-item label="活畜重量" required="true" v-if="form.contractFlag == 0" prop="weightRange">
                        <view class="job-type-content">
                            <p v-for="item in livestockWeightList" :key="item.dictLabel"
                                :class="item.dictLabel == form.weightRange ? 'policy-type-active' : ''"
                                @click="handleActive1('weightRange', item.dictLabel)">
                                {{ item.dictLabel }}
                            </p>
                        </view>
                    </u-form-item>
                    <u-form-item label="订单数量（头）" prop="livestockNum">
                        <u-input :custom-style="customStyle" :placeholder-style="placeholderStyle" v-model="form.livestockNum" placeholder="请输入期望供货的数量"/>
                    </u-form-item>
                    <u-form-item label="监管方" prop="supervisorCompanyName" right-icon="arrow-right">
                        <u-input :custom-style="customStyle" :placeholder-style="placeholderStyle" v-model="form.supervisorCompanyName" placeholder="请选择监管方" disabled @click="handleSelect('supervisorCompanyName')"/>
                    </u-form-item>
                    <u-form-item label="客户所在区" required="true" right-icon="arrow-right" prop="deliveryProvince">
                        <p :class="form.deliveryProvince ? '' : 'tips'" @click="pickerAreaShow = true">{{ form.deliveryProvince ? form.deliveryProvince + form.deliveryCity : '请选择客户所在区' }}</p>
                    </u-form-item>
                    <u-form-item label="收货地址" required="true" prop="deliveryDetailAddress">
                        <u-input :custom-style="customStyle" :placeholder-style="placeholderStyle" v-model="form.deliveryDetailAddress" placeholder="请输入收货地址"/>
                    </u-form-item>
					<u-form-item label="计划发车时间" required="true" prop="deliveryStartTime" right-icon="arrow-right">
						<u-input :custom-style="customStyle" :placeholder-style="placeholderStyle" v-model="form.deliveryStartTime" placeholder="请选择计划发车时间" disabled @click="handleShowTime('deliveryStartTime')"/>
					</u-form-item>
					<u-form-item label="预计抵达时间" required="true" prop="deliveryEndTime" right-icon="arrow-right">
						<u-input :custom-style="customStyle" :placeholder-style="placeholderStyle" v-model="form.deliveryEndTime" placeholder="请选择预计抵达时间" disabled @click="handleShowTime('deliveryEndTime')"/>
					</u-form-item>
                    <u-form-item label="承运人" prop="driverName" right-icon="arrow-right" >
                        <u-input :custom-style="customStyle" :placeholder-style="placeholderStyle" v-model="form.driverName" placeholder="请选择承运人" disabled @click="showDriver = true"/>
                    </u-form-item>
                    <u-form-item label="运输车辆" prop="licensePlateNumber">
                        <u-input :custom-style="customStyle" :placeholder-style="placeholderStyle" v-model="form.licensePlateNumber" placeholder="请输入车牌号"/>
                    </u-form-item>
                    <u-form-item label="销售员" required="true" prop="saleUserName" right-icon="arrow-right" v-if="form.contractFlag == 0">
                        <u-input :custom-style="customStyle" :placeholder-style="placeholderStyle" v-model="form.saleUserName" placeholder="请选择销售员" disabled @click="handleSelect('saleUserName')"/>
                    </u-form-item>
                    <u-form-item label="品控员" required="true" prop="qualityUserName" right-icon="arrow-right">
                        <u-input :custom-style="customStyle" :placeholder-style="placeholderStyle" v-model="form.qualityUserName" placeholder="请选择品控员" disabled @click="handleSelect('qualityUserName')"/>
                    </u-form-item>
                    <u-form-item label="验收员" required="true" prop="acceptorName" right-icon="arrow-right">
                        <u-input :custom-style="customStyle" :placeholder-style="placeholderStyle" v-model="form.acceptorName" placeholder="请选择验收员" disabled @click="handleSelect('acceptorName')"/>
                    </u-form-item>
				</u-form>
			</view>
			<div :style="'height:'+ (isIphonex ? 152 : 118) + 'rpx'"></div>
        </scroll-view>
		<view class="container-footer" :style="'padding:'+(isIphonex ? '40rpx 60rpx' : '15rpx 60rpx')">
			<u-button hover-class='none' :custom-style="{
				'background-color': '#40CA8F',
				'color':'white'
			}" shape="circle" @click="addPurchase">提交</u-button>
		</view>
		<u-picker :safe-area-inset-bottom="true" v-model="showTime" mode="time" :params="params" @confirm="submitTime"></u-picker>
		<u-select confirm-color='#40CA8F' v-model="showPicker" :list="rangeData" :value-name="valueName" :label-name="labelName" :cancel-text="cancelText " @cancel="cancelPicker" @confirm="submitPicker"></u-select>
		<u-popup v-model="showDriver" mode="bottom" @cancle="showDriver = false">
			<view class="popup-view">
				<view class="searchs">
					<view class="search">
						<u-search placeholder="请输入承运人名称" v-model="searchValue" @custom="search" @search="search"></u-search>
					</view>
				</view>
				<view class="picker-btn">
					<view class="left" @click="clearable">清空</view>
					<view class="right" @click="selectDriver">确定</view>
				</view>
				<picker-view :indicator-style="indicatorStyle" :value="multiIndex" @change="pickerChange">
					<picker-view-column>
						<view class="item" v-for="(item, index) in driverData" :key="index">{{ item.nickName }} ({{item.phonenumber}})</view>
					</picker-view-column>
				</picker-view>
			</view>
		</u-popup>
		<ContractList :contractPopupShow="contractPopupShow" @canel="close" @close="close" @selectContract="selectContract" />
		<WeeklyOrders :weeklyOrdersShow="weeklyOrdersShow" :saleContractId='form.saleContractId' @canel="close" @close="close" @selectOrder="selectOrder" />
        <addressPicker @submitAddress="submitAddress" :pickerAreaShow="pickerAreaShow" @addressCanel="pickerAreaShow = false" :titleShow="false" />
	</view>
</template>

<script>
import {
	mapState
} from "vuex"
import {
    getDicts,
    livestockCategory,
    animalTypeList
} from "@/api/dict.js"
import { provinceList } from '@/api/common.js'
import { demandOrderInfo, nmbUserList } from '@/api/pages/demandOrder'
import { companyList, purchaseOrderAdd } from '@/api/pages/purchaseOrder'
import ContractList from './components/contractList.vue'
import WeeklyOrders from "./components/weeklyOrders.vue"
import addressPicker from '@/components/address-picker/index.vue'
	export default {
		components: {
			ContractList,
			WeeklyOrders,
			addressPicker
		},
		data() {
			return {
                customStyle: { fontSize: '26rpx' },
                labelStyle: { color: '#333', fontSize: '26rpx' },
                placeholderStyle: 'color:#999;font-size: 26rpx;',
				showTime: false, //时间弹出选择框
				deliveryStartTime: {
					title: '结束时间',
					timestamp: 0
				}, 
				deliveryEndTime: {
					title: '起始时间',
					timestamp: 0
				},
				params: {
					year: true,
					month: true,
					day: true,
					hour: true,
					minute: true,
					second: false
				},
				form: {
					contractFlag: '1',
					contractFlagName: '有合同',
					nmbCompanyName: '',
					nmbCompanyId: '',
					provinceName: '',
					provinceId: '',
					brokerId: '',
					livestockNum: '',
					supervisorCompanyId: '',
					deliveryStartTime: '',
					deliveryEndTime: '',
					driverName: '',
					driverId: '',
					licensePlateNumber: '',
					qualityUserName: '',
					qualityUserId: '',
					acceptorName: '',
					acceptorId: '',
					saleContractName: '',
					saleContractId: '',
					demandOrderName: '',
					demandOrderId: '',
					deliveryProvince: '',
					deliveryProvinceId: '',
					deliveryCity: '',
					deliveryCityId: '',
					deliveryDetailAddress: '',
                    varietiesId: '',
                    varietiesName:  '',
                    categoryId: '',
                    categoryName:  '',
                    ageRange:  '',
                    weightRange:  '',
					saleUserId: '',
					saleUserName: '',
					demandOrderRemark: ''
				},
				valueName: '',
				labelName: '',
				errorType: ['message'],
				rules: {
					provinceName: { required: true, message: '请选择牛源地', trigger: 'blur' },
					brokerName: { required: true, message: '请选择牛经纪', trigger: 'blur' },
					deliveryStartTime: { required: true, message: '请选择计划发车时间', trigger: 'blur' },
					deliveryEndTime: { required: true, message: '请选择预计抵达时间', trigger: 'blur' },
					qualityUserName: { required: true, message: '请选择品控员', trigger: 'blur' },
					acceptorName: { required: true, message: '请选择验收员', trigger: 'blur' },
					contractFlagName: { required: true, message: '请选择订单性质', trigger: 'blur' },
					// saleContractName: { required: true, message: '请关联合同', trigger: 'blur' },
					// demandOrderName: { required: true, message: '请关联周订单', trigger: 'blur' },
					// demandOrderRemark: { required: true, message: '请输入采购要求', trigger: 'blur' },
                    // varietiesId: { required: true, message: '请选择活畜品种', trigger: 'blur' },
                    // categoryId: { required: true, message: '请选择品种分类', trigger: 'blur' },
                    // ageRange: { required: true, message: '请选择活畜月龄', trigger: 'blur' },
                    // weightRange: { required: true, message: '请选择活畜重量', trigger: 'blur' },
					// saleUserName: { required: true, message: '请选择销售员', trigger: 'blur' },
					deliveryDetailAddress: { required: true, message: '请输入收货地址', trigger: 'blur' },
					deliveryProvince: { required: true, message: '请选择客户所在区', trigger: 'blur' },
				},
				showPicker: false,
				selectType: '',
				rangeData: [],
				provinceData: [],
				brokerData: [],
				qualityUserData: [],
				acceptorNameData: [],
				driverData: [],
				rangeKey: '',
				supervisorCompanyData: [],
				showDriver: false,
				multiIndex: [0],
				isIphonex: getApp().globalData.systemInfo.isIphonex,
				indicatorStyle: `height: ${Math.round(uni.getSystemInfoSync().screenWidth / (750 / 100))}px;`,
				demandOrderId: '',
				dataInfo: {
					ageRange: '',
					weightRange: '',
					categoryName: '',
					varietiesName: '',
					remark: '',
				},
				searchValue: '',
				contractFlagList: [
					{ label: '有合同', value: '1' },
					{ label: '无合同', value: '0' }
				],
				companyCustomerData: [],
				contractPopupShow: false,
				weeklyOrdersShow: false,
				pickerAreaShow: false,
				categoryList: [],
				varietiesList: [],
				livestockAgeList: [],
				livestockWeightList: [],
				cancelText: '取消'
			}
		},
		computed: {
			...mapState({
				userInfo: (state) => state.userDetail.user,
			}),
		},
		created() {
			this.getProvinceList()
			this.getUserList('company_nmb_broker') // 牛经纪
			this.getUserList('company_nmb_quality') // 品控员
			this.getUserList('company_nmb_acceptor') // 验收员
			this.getCompanyList(3)
		},
        onReady() {
            this.$refs.uForm.setRules(this.rules)
        },
		onLoad(opation) {
			// 接收路由参数
			this.demandOrderId = opation.demandOrderId
			if (this.demandOrderId) {
				this.getInfo(this.demandOrderId)
			} else {
				this.getDict()
				this.getUserList('company_nmb_sale') // 销售员
			}
			
			if (this.form.contractFlag == 1 && !opation.demandOrderId) {
				this.rules = {
					...this.rules,
					saleContractName: { required: true, message: '请关联合同', trigger: 'blur' },
					demandOrderName: { required: true, message: '请关联周订单', trigger: 'blur' },
				}
			} else if (this.form.contractFlag == 0) {
				this.rules = {
					...this.rules,
					demandOrderRemark: { required: true, message: '请输入采购要求', trigger: 'blur' },
					varietiesId: { required: true, message: '请选择活畜品种', trigger: 'blur' },
					categoryId: { required: true, message: '请选择品种分类', trigger: 'blur' },
					ageRange: { required: true, message: '请选择活畜月龄', trigger: 'blur' },
					weightRange: { required: true, message: '请选择活畜重量', trigger: 'blur' },
					saleUserName: { required: true, message: '请选择销售员', trigger: 'blur' },
				}
			}
		},
		methods: {
			getProvinceList() { // 省会列表
				provinceList({}).then(res => {
					this.provinceData = res.result
				})
			},
			getInfo(demandOrderId) { 
				uni.showLoading({
					title: '加载中',
					icon: 'none'
				})
				demandOrderInfo({
					demandOrderId
				}).then(response=>{
					if(response.code === 200 ) {
						this.dataInfo = response.result || {};
						this.form.provinceName = this.dataInfo.provinceName
						this.form.provinceId = this.dataInfo.provinceId
						this.form.deliveryDetailAddress = this.dataInfo.deliveryDetailAddress
						this.form.deliveryCity = this.dataInfo.deliveryCity
						this.form.deliveryProvince = this.dataInfo.deliveryProvince
						this.form.deliveryProvinceId = this.dataInfo.deliveryProvinceId
						this.form.deliveryCityId = this.dataInfo.deliveryCityId
						if (this.demandOrderId) {
							this.form.demandOrderId = this.dataInfo.demandOrderId
							this.form.saleContractId = this.dataInfo.saleContractId
						}
						console.log(this.form)
					}
				})
				uni.hideLoading()
			},
            getUserList(roleKey, nickName) {
                nmbUserList({
                    pageNum: 1,
                    pageSize: 200,
                    roleKey,
					nickName
                }).then(res => {
					switch(roleKey) {
						case 'company_nmb_broker':
							this.brokerData = res.result.list
						break;
						case 'company_nmb_quality':
							this.qualityUserData = res.result.list
						break;
						case 'company_nmb_acceptor':
							this.acceptorNameData = res.result.list
						break;
						case 'company_nmb_sale':
							this.saleData = res.result.list
						break;
						case 'company_nmb_driver':
							this.driverData = res.result.list.map(item => {
								item.label = `${item.nickName} (${item.phonenumber})`
								return item
							})
						break;
					}
				})
            },
            getCompanyList(nmbCompanyType, contractFlag) {
                companyList({
                    pageNum: 1,
                    pageSize: 200,
                    nmbCompanyType,
					contractFlag
                }).then(res => {
					if (nmbCompanyType == 3) {
						this.supervisorCompanyData = res.result.list
					} else if (nmbCompanyType == 2) {
						this.companyCustomerData = res.result.list
					}
				})
            },
			getDict() {
				livestockCategory({
					pageNum: 1,
					pageSize: 100000,
					categoryType: '403292860613267456'
				}).then(res => {
					this.categoryList = res.result || []
				})
				animalTypeList({
					pageSize: 9999,
					pageNum: 1,
					categoryType: '403292860613267456'
				}).then(res => {
					this.varietiesList = res.result || []
				})
				getDicts('livestock_age').then(res => {
					this.livestockAgeList = res.data || []
				})
				getDicts("livestock_weight").then((res) => {
					this.livestockWeightList = res.data || []
				})
			},
			submitTime(val) {
				const arr = ['deliveryStartTime', 'deliveryEndTime']
				arr.map((item, index) => {
					if (item === this.typeTime) {
						this[item].timestamp = val.timestamp
						this[item].title = val.year + '-' + val.month + '-' + val.day + ' ' + val.hour + ':' + val.minute
						this.form[item] = val.year + '-' + val.month + '-' + val.day + ' ' + val.hour + ':' + val.minute
					}
				})
			},
			handleShowTime(val) {
				this.typeTime = val
				this.showTime = true
			},
			submitPicker(val) {
				const values = val[0]
				switch(this.selectType) {
					case 'provinceName':
						this.form.provinceName = values.label
						this.form.provinceId = values.value
					break
					case 'broker':
						this.form.brokerName = values.label
						this.form.brokerId = values.value
					break
					case 'supervisorCompanyName':
						this.form.supervisorCompanyName = values.label
						this.form.supervisorCompanyId = values.value
					break
					case 'qualityUserName':
						this.form.qualityUserName = values.label
						this.form.qualityUserId = values.value
					break
					case 'acceptorName':
						this.form.acceptorName = values.label
						this.form.acceptorId = values.value
					break
					case 'contractFlag':
						this.form.contractFlagName = values.label
						this.form.contractFlag = values.value
					break
					case 'nmbCompany':
						this.form.nmbCompanyName = values.label
						this.form.nmbCompanyId = values.value
					break
					case 'saleUserName':
						this.form.saleUserName = values.label
						this.form.saleUserId = values.value
					break
				}
				this.showPicker = false
			},
			handleSelect(val) {
				this.selectType = val
				this.cancelText = '取消'
				switch(val) {
					case 'provinceName':
						this.labelName = 'provinceName'
						this.valueName = 'provinceId'
						this.rangeData = this.provinceData
					break
					case 'contractFlag':
						this.labelName = 'label'
						this.valueName = 'value'
						this.rangeData = this.contractFlagList
						this.getCompanyList(2, 0)
					break
					case 'nmbCompany':
						this.labelName = 'companyName'
						this.valueName = 'nmbCompanyId'
						this.rangeData = this.companyCustomerData
					break
					case 'broker':
						this.labelName = 'nickName'
						this.valueName = 'nmbUserId'
						this.rangeData = this.brokerData
					break
					case 'supervisorCompanyName':
						this.labelName = 'companyName'
						this.valueName = 'nmbCompanyId'
						this.cancelText = '清空'
						this.rangeData = this.supervisorCompanyData
					break
					case 'qualityUserName':
						this.labelName = 'nickName'
						this.valueName = 'nmbUserId'
						this.rangeData = this.qualityUserData
					break
					case 'acceptorName':
						this.labelName = 'nickName'
						this.valueName = 'nmbUserId'
						this.rangeData = this.acceptorNameData
					break
					case 'saleUserName':
						this.labelName = 'nickName'
						this.valueName = 'nmbUserId'
						this.rangeData = this.saleData
					break
				}
				this.showPicker = true
			},
			search(val) {
				this.getUserList('company_nmb_driver', val) // 司机
			},
			pickerChange(e) {
				this.multiIndex = e.detail.value
			},
			selectDriver() {
				const currentItem = this.driverData[this.multiIndex[0]]
				this.form.driverName = currentItem.nickName
				this.form.driverId = currentItem.nmbUserId
				this.showDriver = false
			},
			addPurchase() {
                this.$refs.uForm.validate((valid) => {
                    if (valid) {
						const { contractFlag, contractFlagName, nmbCompanyName,	nmbCompanyId, provinceName,
							provinceId,	brokerId, livestockNum,	supervisorCompanyId, deliveryStartTime,
							deliveryEndTime, driverName, driverId, licensePlateNumber, qualityUserName,
							qualityUserId, acceptorName, acceptorId, saleContractName,	saleContractId,
							demandOrderName, demandOrderId,	deliveryProvince, deliveryProvinceId,	deliveryCity,
							deliveryCityId,	deliveryDetailAddress, varietiesId,  varietiesName, categoryId,
							categoryName, ageRange, weightRange, saleUserId, saleUserName, demandOrderRemark }= this.form
						let params = {}
						if (contractFlag == 1) {
							params = {
								demandOrderId: this.demandOrderId,
								contractFlag, saleContractId, demandOrderId, deliveryProvince, deliveryProvinceId,
								deliveryCity, deliveryCityId, deliveryDetailAddress, provinceId, provinceName,
								brokerId, livestockNum, supervisorCompanyId, deliveryStartTime, deliveryEndTime,
								driverId, licensePlateNumber, qualityUserId, acceptorId
							}
						}
						if (contractFlag == 0) {
							params = {
								contractFlag, nmbCompanyId, deliveryStartTime, deliveryEndTime, deliveryProvince,
								deliveryProvinceId, deliveryCity, deliveryCityId, deliveryDetailAddress,
								varietiesId, varietiesName, categoryId, categoryName, ageRange, weightRange,
								provinceId, provinceName, brokerId, livestockNum, supervisorCompanyId,
								saleUserId, demandOrderRemark, driverId, licensePlateNumber, qualityUserId,acceptorId
							}
						}
						purchaseOrderAdd(params).then((res) => {
                            if (res.code == 200) {
                                uni.showToast({
                                    title: '新建成功',
                                    icon: 'none'
                                })
                                uni.navigateBack({
                                    delta:1
                                })
                            }
                        })
                    }
                })
			},
			close() {
				this.contractPopupShow = false
				this.weeklyOrdersShow = false
			},
			selectContract(val) {
				this.form.saleContractName = val.saleContractCode
				this.form.saleContractId = val.saleContractId
				this.contractPopupShow = false
			},
			selectOrder(val) {
				this.form.demandOrderName = val.demandOrderCode
				this.form.demandOrderId = val.demandOrderId
				this.getInfo(val.demandOrderId)
				this.weeklyOrdersShow = false
			},
            submitAddress(val) {
                this.form.province = val.areaName
                let areaName = val.areaName.split('-')
                let areaId = val.areaValue.split(',')
                this.form.deliveryProvince = areaName[0] || ''
                this.form.deliveryProvinceId = areaId[0] || ''
                this.form.deliveryCity = areaName[1] || ''
                this.form.deliveryCityId = areaId[1] || ''
            },
            handleActive(key, value, lable, labelValue) {
				this.form[key] = value
				this.form[lable] = labelValue
			},
            handleActive1(key, value) {
				this.form[key] = value
			},
			clearable(){
				this.form.driverName = '';
				this.form.driverId = '';
				this.showDriver = false;
			},
			cancelPicker() {
				this.showPicker = false
				if(this.selectType === 'supervisorCompanyName') {
					this.form.supervisorCompanyName = ''
					this.form.supervisorCompanyId = ''
				}
			}
		}
	}
</script>

<style lang="less" scoped>

.container {
	margin: 30rpx;
	background: #fff;
	box-sizing: border-box;
	padding: 30rpx 32rpx 40rpx;
	border-radius: 30rpx;
        /deep/ .u-form-item {
            padding: 20rpx 20rpx !important;
        }
        .tips {
            font-size: 26rpx;
            color: #ccc;
        }


        .voucher {
            padding-bottom: 40rpx;
            font-size: 28rpx;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #999999;
        }

        .all-img {
            image {
            width: 154rpx;
            height: 154rpx;
            background: #d8d8d8;
            border-radius: 16rpx;
            margin-right: 20rpx;
            }
        }

        .manyMode {
            background: transparent;
            height: auto;
        }

        .uploadImage {
            display: flex;
            flex-wrap: wrap;
            // justify-content: space-between;
            // align-content: space-between;
            position: relative;

            .itemAlready {
            width: 140rpx;
            height: 140rpx;
            border-radius: 8rpx;
            position: relative;
            margin: 0 20rpx 10rpx 0rpx;

            image {
                width: 100%;
                height: 100%;
                border-radius: 8rpx;
            }

            .closeIcon {
                width: 32rpx;
                height: 32rpx;
                background-image: url('../../../static/modalImg/error.png');
                position: absolute;
                background-size: cover;
                top: -10rpx;
                right: -10rpx;
            }
        }

        .item {
			width: 140rpx;
			height: 140rpx;
			border-radius: 8rpx;
			position: relative;
			border: 2rpx dashed #d8d8d8;

			.uploadIcon {
				width: 100%;
				height: 120rpx;
				display: flex;
				justify-content: center;
				align-items: flex-end;
				font-size: 24rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #d8d8d8;
				background: url('https://fengtaohui.oss-cn-beijing.aliyuncs.com/uploads/image/20230105/6400955ab08a2f639e9790b815c418db63b666a9d7e40.png') no-repeat;
				background-size: 20rpx 20rpx;
				background-position: center 30rpx;
				position: absolute;
				top: 0;
				left: 0;
				bottom: 0;
				right: 0;
				margin: auto;
				z-index: 5;
			}
        }
    }
}


	.scroll-view {
		overflow-y: scroll;
	}

	uni-picker-view {
		display: block;
	}

	uni-picker-view .uni-picker-view-wrapper {
		display: flex;
		align-items: center;
		position: relative;
		overflow: hidden;
		height: 100%;
		background-color: white;
	}

	uni-picker-view[hidden] {
		display: none;
	}

	picker-view {
		width: 100%;
		// height: 600upx;
		height: 600rpx;
		margin-top: 20upx;
	}

	.item {
		line-height: 100upx;
		text-align: center;
	}

	.popup-view {
		.picker-btn {
			display: flex;
			justify-content: space-between;
			padding: 30rpx 40rpx;

			.left {
				color: #999;
				font-size: 28rpx;
				font-family: PingFang SC-Medium;
			}

			.middle {
				font-size: 32rpx;
				color: #222;
				font-family: PingFang SC-Heavy;
			}

			.right {
				color: #40CA8F;
				font-size: 32rpx;
				font-family: PingFang SC-Medium;
			}
		}
	}
    .scroll-view{
        background-color: #fff;
    }
    .submit{
        width: 100%;
        height: 80rpx;
        color: #fff;
        text-align: center;
        line-height: 80rpx;
        font-size: 32rpx;
        padding: 0 20rpx;
        margin-top: 40rpx;
        view{
            width: 100%;
            height: 80rpx;
            border-radius: 40rpx;
            background-color: #40CA8F;
        }
    }

	.container-footer {
		width: 100%;
		position: fixed;
		bottom: 0;
		background-color: #f7f7f7;
		color: #f7f7f7;
		z-index: 100;
	}
	.searchs{
		display: flex;
		justify-content: space-between;
		padding: 20rpx;
		.search{
			flex: 1;
		}
		.fifter{
			width: 100rpx;
			height: 60rpx;
			background-color: #40CA8F;
			color: white;
			text-align: center;
			line-height: 60rpx;
			border-radius: 30rpx;
		}
	}
	.popup-view{
		min-height: 600rpx;
		padding-top: 20rpx;
	}
    .job-type-content {
        display: flex;
        flex-wrap: wrap;
        p {
            color: #999;
            background-color: #F4F4F4;
            padding: 0px 35rpx;
            border-radius: 100rpx 100rpx 100rpx 100rpx;
            margin: 15rpx 30rpx 15rpx 0;
            border: 2rpx solid transparent;
        }

        .job-type-active {
            border: 2rpx solid #40CA8F;
            color: #40CA8F;
            background-color: white;
        }

        .policy-type-active {
            border: 2rpx solid #40CA8F;
            color: #40CA8F;
            background-color: white;
        }

        .ear-type-active {
            border: 2rpx solid #40CA8F;
            color: #40CA8F;
            background-color: white;
        }
    }
</style>