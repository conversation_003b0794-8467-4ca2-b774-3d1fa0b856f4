
<template>
    <view class="main">
      <u-form ref="uForm" :model="form" label-width="auto" :label-style="labelStyle" class="form-box">
        <u-form-item
        label="保养时间"
        prop="maintenanceTime"
        :custom-style="customStyle"
        style="text-align: right"
      >
        <text
          :class="form.maintenanceTime ? 'common' : 'tips'"
          @click="showTime = true"
          >{{
            form.maintenanceTime ? form.maintenanceTime : "请选择保养时间"
          }}</text
        >
      </u-form-item>
      <u-form-item label="保养地点" :required="true">
          <u-input v-model="form.maintenanceAddress" placeholder="保养地点" :custom-style="customStyle" placeholder-style="text-align:right;color:#999;font-size: 26rpx;" />
        </u-form-item>

        <u-form-item label="发车时活畜数量（头）" :required="true">
          <u-input v-model="form.deliveryNumber" type="number" placeholder="发车时活畜数量" disabled :custom-style="customStyle" placeholder-style="text-align:right;color:#999;font-size: 26rpx;" />
        </u-form-item>
        <u-form-item label="当前活畜数量（头）" :required="true">
          <u-input v-model="form.currentLivestockNumber" type="number" @input="calculateDifference" placeholder="当前活畜数量" :custom-style="customStyle" placeholder-style="text-align:right;color:#999;font-size: 26rpx;" />
        </u-form-item>
        <!-- 司机 -->
        <u-form-item label="活畜数量差额（头）" :required="true">
          <u-input v-model="form.quantityDifference" type="number" placeholder="活畜数量差额" disabled :custom-style="customStyle" placeholder-style="text-align:right;color:#999;font-size: 26rpx;" @input="calculateLivestockWeight" />
        </u-form-item>
        <u-form-item label="异常说明" :required="true">
          <u-input v-model="form.exceptionDescription"  placeholder="默认无，若有异常（死亡疾病等），请说明" :custom-style="customStyle" placeholder-style="text-align:right;color:#999;font-size: 26rpx;" />
        </u-form-item>
        <u-form-item label="车辆照片" :required="true" :border-bottom="!form.carImageUrl.length">
          <u-input placeholder=" " disabled />
          <img slot="right" style="width: 40rpx; height: 40rpx" @click="uploadFile('carImageUrl', 1)" src="../../../myPackge2/icon/photo.png" alt="" />
        </u-form-item>
        <view class="uploadImage" v-if="form.carImageUrl.length">
          <view class="itemAlready" v-for="(item, index) in form.carImageUrl" :key="index">
            <image mode="scaleToFill" :src="item" @click="previewImage(item)" />
            <view class="closeIcon" @click="deleteFile(index, 'carImageUrl')"></view>
          </view>
        </view>
        <u-form-item label="绑绳视频" :required="true" :border-bottom="!form.bindRopeVideo.length">
          <u-input placeholder=" " disabled />
          <img slot="right" style="width: 40rpx; height: 40rpx" @click="uploadVideo('bindRopeVideo', 1)" src="../../../myPackge2/icon/uploadVideo.png" alt="" />
        </u-form-item>
        <view class="uploadImage" v-if="form.bindRopeVideo.length">
          <view
            class="itemAlready"
            v-for="(item, index) in form.bindRopeVideo"
            :key="index"
          >
            <!-- <video :src="item" controls autoplay muted></video> -->
            <video :src="item" @click="showVideoFn(item)" :controls="false"
                                :show-fullscreen-btn="false"
                                :show-play-btn="false"
                                :show-center-play-btn="false"></video>
            <view
              class="closeIcon"
              @click="deleteVideo(index, 'bindRopeVideo')"
            ></view>
          </view>
        </view>
        <u-form-item label="保养视频" :required="true" :border-bottom="!form.maintenanceVideo.length">
          <u-input placeholder=" " disabled />
          <img slot="right" style="width: 40rpx; height: 40rpx" @click="uploadVideo('maintenanceVideo', 1)" src="../../../myPackge2/icon/uploadVideo.png" alt="" />
        </u-form-item>
        <view class="uploadImage" v-if="form.maintenanceVideo.length">
          <view
            class="itemAlready"
            v-for="(item, index) in form.maintenanceVideo"
            :key="index"
          >
            <!-- <video :src="item" controls autoplay muted></video> -->
            <video :src="item" @click="showVideoFn(item)" :controls="false"
                                :show-fullscreen-btn="false"
                                :show-play-btn="false"
                                :show-center-play-btn="false"></video>
            <view
              class="closeIcon"
              @click="deleteVideo(index, 'maintenanceVideo')"
            ></view>
          </view>
        </view>
      </u-form>
      <view
        class="container-footer"
        :style="'padding:' + (isIphonex ? '40rpx 60rpx' : '15rpx 60rpx')"
        ref="containerFooter"
      >
        <u-button hover-class='none' shape="circle" @click="saveWeighingData">保存</u-button>
      </view>
      <u-picker :safe-area-inset-bottom="true" v-model="showTime" mode="time" :params="params" @confirm="submitTime"></u-picker>
    <u-popup v-model="showVideo" mode="center" @close="showVideo = false">
        <view class="video-box" v-if="showVideo">
              <video :src="videoUrl" controls autoplay loop muted />
        </view>
		</u-popup>
    </view>
  </template>
  
  <script>
  import { uploadFiles } from '@/api/obsUpload/index'
import termPicker from "@/components/term-picker/term-picker"
import { updateLocation } from './components/map/getLocation.js'
import { add } from '@/api/pages/transport'
import { purchaseOrderInfo } from '@/api/pages/purchaseOrder'

  
  export default {
    components: {
        termPicker,
     },
    data() {
      return {
        isIphonex: getApp().globalData.systemInfo.isIphonex,
        form: {
          carImageUrl: [],
          bindRopeVideo: [],
          maintenanceVideo: [],
          maintenanceTime: this.getCurrentDate(),
          maintenanceAddress: '',
          deliveryNumber: '',
          quantityDifference: ''
        },
        visable: false,
        labelStyle: {
          fontSize: '26rpx',
          fontFamily: 'PingFang SC, PingFang SC',
          color: '#333',
        },
        customStyle: {
          textAlign: "right",
        },
        purchaseOrderId: '',
        showVideo: false,
        videoUrl: '',
				showTime: false, //时间弹出选择框
        params: {
					year: true,
					month: true,
					day: true,
					hour: true,
					minute: true,
					second: false
				},
      }
    },
    async onLoad(opation){
      this.purchaseOrderId = opation.purchaseOrderId
        this.getPurchaseOrderInfo(this.purchaseOrderId)
        try {
          const resAddress = await updateLocation()
          if (resAddress && resAddress.address) {
            this.form.maintenanceAddress = resAddress.address
          }
        } catch (error) {
          console.error('获取位置信息失败：', error)
        }
    },
    methods: {
      calculateDifference() {
        const departure = parseFloat(this.form.deliveryNumber) || 0
        const current = parseFloat(this.form.currentLivestockNumber) || 0
        this.form.quantityDifference = (departure - current).toString()
      },
      getPurchaseOrderInfo(purchaseOrderId){
        purchaseOrderInfo({purchaseOrderId}).then(res => {
          if(res.code === 200){
            this.form.deliveryNumber = res.result.deliveryNumber || 0
            this.form.currentLivestockNumber = res.result.deliveryNumber || 0
            this.form.quantityDifference = Number(this.form.deliveryNumber)- Number(this.form.currentLivestockNumber)
          }
        })
      },
      calculateLivestockWeight() {
        const tare = parseFloat(this.form.tareWeight) || 0
        const gross = parseFloat(this.form.grossWeight) || 0
        this.form.livestockWeight = (gross - tare).toFixed(2)
      },
      uploadVideo(type) {
        if (this.form[type].length >= 1) {
          uni.showToast({
            icon: 'none',
            title: '视频最多只能上传1个',
          })
          return
        }
        const that = this
        uni.chooseVideo({
          sourceType: ['album', 'camera'],
          maxDuration: 60,
          camera: 'back',
          success: function (res) {
            uploadFiles({
              filePath: res.tempFilePath,
            }).then((data) => {
              that.form[type].push(data)
            })
          },
          fail(e) {},
        })
      },
      uploadFile(type) {
        if (this.form[type].length >= 1) {
          uni.showToast({
            icon: 'none',
            title: '文件最多只能上传1个',
          })
          return
        }
        const that = this
        uni.chooseImage({
          count: 1,
          sizeType: ['original', 'compressed'],
          sourceType: ['album', 'camera'],
          name: 'file',
          success: function (res) {
            uploadFiles({
              filePath: res.tempFilePaths[0],
            }).then((data) => {
              that.form[type].push(data)
            })
          },
          fail(e) {},
        })
      },
      previewImage(url) {
        uni.previewImage({
          urls: [url],
        })
      },
      deleteFile(index, type) {
        this.form[type].splice(index, 1)
        this.$forceUpdate()
      },
      deleteVideo(index,type) {
        this.form[type].splice(index, 1)
        this.$forceUpdate()
      },
      saveWeighingData() {
        const params = {
          purchaseOrderId: this.purchaseOrderId,
          maintenanceTime: this.form.maintenanceTime,
          maintenanceAddress: this.form.maintenanceAddress,
          currentLivestockNumber: this.form.currentLivestockNumber,
          exceptionDescription: this.form.exceptionDescription,
          carImageUrl: this.form.carImageUrl.join(','),
          bindRopeVideo: this.form.bindRopeVideo.join(','),
          maintenanceVideo: this.form.maintenanceVideo.join(','),
        }
        add(params).then(res => {
          if (res.code === 200) {
            uni.showToast({
              title: '保存成功',
              icon: "none"
            })
            setTimeout(() => {
              uni.navigateBack({
                delta: 1,
              })
            }, 500)
          }
        })
      },
    resetField(value) {
      this.$refs.uForm.fields.forEach((e) => {
        if (e.prop == value) {
          e.resetField();
        }
      });
    },
    submitTime(val) {
      this.form.maintenanceTime = val.year + '-' + val.month + '-' + val.day + ' ' + val.hour + ':' + val.minute
		},
    getCurrentDate() {
        const date = new Date();
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, "0");
        const day = String(date.getDate()).padStart(2, "0");
        const hours = String(date.getHours()).padStart(2, "0");
        const minutes = String(date.getMinutes()).padStart(2, "0");
        return `${year}-${month}-${day} ${hours}:${minutes}`;
    },
    showVideoFn(item) {
        this.videoUrl = item
        this.showVideo = true
    }
    }
  }
  </script>
  
  <style lang="scss" scoped>
  @import "../../../myPackge2/pages/catchingCows/form.scss";
  .container-footer {
    width: 100%;
    position: fixed;
    bottom: 0;
    background-color: white;
    color: white;
    z-index: 1000;
    padding: 15rpx 60rpx;
    /deep/ .u-btn {
      color: white;
      background-color: #40CA8F;
    }
  }
  .form-box{
    overflow: auto !important;
    padding-bottom: 150rpx !important;
  }
  .uploadImage{
    display: flex !important;
    flex-wrap: wrap !important;
  }
  </style> 