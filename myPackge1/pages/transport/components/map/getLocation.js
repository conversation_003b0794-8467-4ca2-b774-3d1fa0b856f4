// 获取用户位置并解析
import amap from './amap-wx.130'
export function updateLocation() {
  return new Promise((resolve, reject) => {
    const _key = 'a164cb62a60b836927d1f3a2d9db3206'
    const locationInfo = {
      address: '',
      location: {
        latitude: 0,
        longitude: 0,
      },
    }
    wx.authorize({
      scope: 'scope.userFuzzyLocation',
      success() {
        wx.getFuzzyLocation({
          type: 'wgs84',
          success: (res) => {
            locationInfo.location.latitude = res.latitude
            locationInfo.location.longitude = res.longitude
            const map = new amap.AMapWX({
              key: _key,
            })
            map.getRegeo({
              location: `${res.longitude},${res.latitude}`,
              success: (data) => {
                resolve({
                  ...locationInfo,
                  address: data[0].regeocodeData.formatted_address,
                })
              },
              fail: (err) => {
                reject(err)
                console.log(err)
              },
            })
          },
          fail(err) {
            console.log(err, 'fail')
          },
          complete(res) {
            console.log(res, 'complete')
          },
        })
      },
    })
  })
  
  
  // return locationInfo
}

// 顶部栏高度
export function getSystemInfo() {
  const data = uni.getSystemInfoSync()
  uni.setStorageSync('modelPhone', {
    model: data.model,
  })
  if (data.system.indexOf('iOS') >= 0) {
    uni.setStorageSync('Phone', {
      system: 'ios',
    })
  } else {
    uni.setStorageSync('Phone', {
      system: 'android',
    })
  }
  const statusBar = data.statusBarHeight // 状态栏高度
  const custom = uni.getMenuButtonBoundingClientRect() // 菜单按钮
  const screenWidth = data.windowWidth
  const screenHeight = data.windowHeight
  const pixelRatio = data.pixelRatio
  const safeBottom = data.screenHeight - data.safeArea.bottom
  let isIphonex = false
  let customBar = custom.height
  if (safeBottom == 34) {
    isIphonex = true
  }
  if (data.system.indexOf('iOS') !== -1) {
    customBar = 44
  } else {
    customBar = 40
  }
  // 计算得到定义的状态栏高度
  return {
    safeBottom,
    isIphonex,
    // 菜单按钮
    statusBar,
    // 苹果paiding
    customBar: statusBar + customBar,
    // 头部导航高度
    titleHeight: customBar,
    screenWidth,
    screenHeight,
    pixelRatio,
    is_ios: data.system.indexOf('iOS') >= 0,
    platform: data.platform,
  }
}
