<template>
    <view style="padding-bottom: 40rpx;">
        <scroll-view class="main" scroll-y>
            <block>
                <section>
                    <view class="list-section" >
                        <view class="middle" style="width:100%';">
                            <view class="list-item">
                                <p>保养时间：<span>{{ dataInfo.maintenanceTime }}</span></p>
                                <p>保养地点：<span>{{ dataInfo.maintenanceAddress }}</span></p>
                                <p>订单总数量：<span>{{ dataInfo.totalLivestockNum }}</span></p>
                                <p>当前活畜总数量：<span>{{ dataInfo.currentLivestockNumber }}</span></p>
                                <p>异常说明：<span>{{ dataInfo.exceptionDescription }}</span></p>
                            </view>
                            <view class="file_box" >
                                <p>车辆照片</p>
                                <view class="file_items">
                                    <view class="file_item" v-for="(item,index) in dataInfo.carImageUrl.split(',')" :key="index">
                                        <image :src="item" mode="aspectFill" @click="previewImage(item)" class="file_img"></image>
                                    </view>
                                </view>
                            </view>
                            <view class="file_box" >
                                <p>绑绳视频</p>
                                <view class="file_items">
                                    <view class="file_item" v-for="(item,index) in dataInfo.bindRopeVideo.split(',')" :key="index">
                                        <video :src="item" mode="aspectFill" @click="previewImage(item)" class="file_img"></video>
                                    </view>
                                </view>
                            </view>
                            <view class="file_box" >
                                <p>保养视频</p>
                                <view class="file_items">
                                    <view class="file_item" v-for="(item,index) in dataInfo.maintenanceVideo.split(',')" :key="index">
                                        <video :src="item" mode="aspectFill" @click="previewImage(item)" class="file_img"></video>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </view>
                </section>
            </block>
        </scroll-view>
    </view>
</template>
<script>
import { info } from '@/api/pages/transport'
export default {
    name: '',
    data() {
        return {
            dataInfo: {},
            purchaseMaintenanceId: ''
        }
    },
    onLoad(opation) {
        // 接收路由参数
        this.purchaseMaintenanceId = opation.purchaseMaintenanceId
        this.getInfo()
    },
    methods: {
        getInfo() { 
            uni.showLoading({
                title: '加载中',
				icon: 'none'
			})
            info({
                purchaseMaintenanceId: this.purchaseMaintenanceId
            }).then(response=>{
                if(response.code === 200 ) {
                    this.dataInfo =response.result || {};
                }
            })
            uni.hideLoading()
        },
    },
}
</script>

<style scoped lang="scss">
@import "@/common/css/superviseHome.scss";
.list-item p{
    white-space: normal !important;
    display: flex;
    justify-content: space-between;
    font-size: 26rpx;
    color: #999;
    line-height: 43rpx;
    span{
        width: 500rpx;
        text-align: right;
        color: #333;
        font-size: 26rpx;
        font-weight: 400;
    }
}
.title{
    list-style: 50rpx;
    font-weight: 500;
    font-size: 32rpx;
    color: #222222;
    padding: 20rpx;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .btn-box{
        display: flex;
        align-items: center;
    }
    text{
        font-weight: 400;
        font-size: 28rpx;
        color: #EA501E;
        margin-right: 20rpx;
    }
}
.list-section{
    flex-direction: column;
}
.tabs{
    display: flex;
    margin: 20rpx;
    background: #F7F8F7;
    border-radius: 10rpx;
    height: 64rpx;
    padding: 6rpx;
    box-sizing: border-box;
    .tabs-item{
        flex: 1;
        text-align: center;
        font-size: 26rpx;
        color: #999999;
        padding: 16rpx 20rpx;
        margin: 0 8rpx;
        border-radius: 20rpx;
        width: 118rpx;
        height: 52rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 500;
        .icon{
            font-size: 24rpx;
            margin-left: 5rpx;
        }
    }
    .current{
        background-color: #1AAF77;
        color: #FFFFFF;
    }
}
</style>