<template>
    <view>
        <scroll-view class="main" scroll-y :scroll-with-animation="true"
            @scrolltolower="scrollToLower" refresher-enabled :refresher-triggered="refresherState"
            @refresherrefresh="bindrefresherrefresh" :scroll-top="scrollTop">
            <template v-if="list && list.length > 0">
                <section v-for="(item,index) in list" :key="index">
                    <view class="list-section" >
                        <view class="middle" style="width:100%'">
                            <view class="list-item" @click="details(item)">
                                <p>保养时间：{{item.maintenanceTime}}</p>
                                <p>保养地点：{{ item.maintenanceAddress }}</p>
                                <p>活畜数量差额：{{item.livestockNumberDifference}}</p>
                                <p>异常说明：{{ item.exceptionDescription }}</p>
                                <p>车辆照片：{{ item.carImageNum }}</p>
                                <p>绑绳视频：{{ item.bindRopeVideoNum }}</p>
                                <p>保养视频：{{ item.maintenanceVideoNum }}</p>
                            </view>
                        </view>
                    </view>
                </section>
            </template>
            <template v-else>
                        	<u-empty text="暂无数据" mode="data" :icon-size='150'></u-empty>
                        </template>
            <div v-if="!empty" :style="'height:'+ (isIphonex ? 152 : 118) + 'rpx'"></div>
        </scroll-view>
        <!-- 新增计划按钮 -->
		<view class="container-footer" :style="'padding:'+(isIphonex ? '40rpx 60rpx' : '15rpx 60rpx')"  v-if="$hasPermi('nmb:maintenance:add')">
			<u-button hover-class='none' :custom-style="{
				'background-color': '#40CA8F',
				'color':'white'
			}" shape="circle" @click="add">新增保养</u-button>
		</view>
    </view>
</template>

<script>

import { page } from '@/api/pages/transport'
export default {    data() {
        return {
            refresherState: false,
            noMore: false,
            isEmpty: false,
            scrollTop: 0,
            isIphonex: getApp().globalData.systemInfo.isIphonex,
            pickerFilterShow: false,
            list: [],
            pageNum: 1,
            pageSize: 10,
            searchText: '',
            filters: {
                pageNum: 1,
                pageSize: 10
            },
            purchaseOrderId: '',
        }
    },
    onLoad(opation) {
        // 接收路由参数
        this.purchaseOrderId = opation.purchaseOrderId
    },
    onShow() {
        this.getList()
    },
    methods: {
        getList(val) { 
            uni.showLoading({
                title: '加载中',
				icon: 'none'
			})
            page({
                pageNum: 1,
                pageSize: 10,
                ...this.filters,
                purchaseOrderId: this.purchaseOrderId
            }).then(response=>{
                if(response.code === 200 ) {
                    let list =response.result?.list || [];
                    let total = response.result?.total || 0;
                    if(this.pageNum >=2) {
                        this.list = this.list.concat(list);
                        this.list.length >= total ? this.noMore = true : this.noMore = false;
                    }else {
                        if(total >=1) {
                            this.isEmpty = false;
                            this.list = list;
                            this.list.length >= total ? this.noMore = true : this.noMore = false;
                        }else {
                            this.isEmpty = true
                        }
                    }
                }
            })
            uni.hideLoading()
        },
        scrollToLower() {
			if (this.noMore) return;
            this.pageNum++;
            this.isParams();
		},
        bindrefresherrefresh() {
			this.refresherState = true;
			this.pageNum = 1;
			this.noMore = false;
            this.isParams();
			setTimeout(() => {
				this.refresherState = false;
                this.$toast('刷新成功')
			}, 1000);
		},
        getPamams() {
			let params = {
				pageNum: this.pageNum,
				pageSize: this.pageSize
			}
			return params
		},
        isParams() {
            let params = this.getPamams();
            this.getList(params);
        },
        details(item) {
            uni.navigateTo({
                url: '/myPackge1/pages/transport/detail?purchaseMaintenanceId='+ item.purchaseMaintenanceId
            })
        },
        add() {
            uni.navigateTo({
                url: '/myPackge1/pages/transport/form?purchaseOrderId='+ this.purchaseOrderId
            })
        },
    },
}
</script>

<style scoped lang="scss">
    @import "@/common/css/superviseHome.scss";
    .list-item-btn {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        margin-top: 20rpx;
        .item{
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 28rpx;
            font-family: PingFang SC-Medium, PingFang SC;
            font-weight: 400;
            color: #40CA8F;
            padding-left: 10rpx;
            line-height: 50rpx;
            margin-left: 30rpx;
            img {
                width: 45rpx;
                height: 45rpx;
                margin-right: 10rpx;
            }
        }
    }
	.container-footer {
		width: 100%;
		position: fixed;
		bottom: 0;
		background-color: white;
		color: white;
	}
.list-item{
    p{
        white-space: normal!important;
    }
}
</style>