<template>
    <view>
		<CustomNavbar  :bgColor="'#08BA7E'" :titleColor="'#FFFFFF'" />
        <view class="header"></view>
        <scroll-view class="main" scroll-y :scroll-with-animation="true"
            @scrolltolower="scrollToLower" refresher-enabled :refresher-triggered="refresherState"
            @refresherrefresh="bindrefresherrefresh" :scroll-top="scrollTop">
            <template v-if="list && list.length > 0">
                <section v-for="(item,index) in list" :key="index">
                    <view class="list-section">
                        <view class="items" style="width:100%'">
                            <view class="item_title" @click="info(item)">
                                <view class="title" >{{item.purchaseOrderCode}}</view>
                            </view>
                            <view class="item_content" @click="info(item)">
                                <p class="item">运输信息：<text class="text">{{ item.driverName }} {{ item.licensePlateNumber }}</text></p>
                                <p class="item">预计抵达时间：<text class="text">{{ item.deliveryEndTime }}</text></p>
                                <p class="item">验收人：<text class="text">{{ item.acceptorName }}</text></p>
                                <p class="item">活畜数量：<text class="text">{{ item.deliveryNumber }}</text></p>`
                            </view>
                            <view class="list_btn_items">
                                <view class="btn_section">
                                    <view class="btn_item" @click="info(item)">
                                        <img class="icon" src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/yunshu/huoche.png" alt="">
                                        <text>运输保养</text>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </view>
                </section>
            </template>
            <template v-else>
				<u-empty text="暂无数据" mode="data" :icon-size='150'></u-empty>
			</template>
            <div v-if="!empty" :style="'height:'+ (isIphonex ? 152 : 118) + 'rpx'"></div>
        </scroll-view>
    </view>
</template>

<script>

import { purchaseOrderPage } from '@/api/pages/purchaseOrder'
import CustomNavbar from '@/components/uni-custom-navbar/CustomNavbar.vue'

export default {
    components: {
        CustomNavbar
    },
    name: '',
    data() {
        return {
            refresherState: false,
            noMore: false,
            isEmpty: false,
            scrollTop: 0,
            isIphonex: getApp().globalData.systemInfo.isIphonex,
            list: [],
            pageNum: 1,
            pageSize: 10,
            filters: {
                pageNum: 1,
                pageSize: 10
            },
        }
    },
    onLoad() {
        // 接收路由参数
        this.getList()
    },
    methods: {
        getList(val) { 
            uni.showLoading({
                title: '加载中',
				icon: 'none'
			})
            let params = {...this.filters}
            
            purchaseOrderPage({
                pageNum: 1,
                pageSize: 10,
                showStatus: 30,
                ...params,
            }).then(response=>{
                if(response.code === 200 ) {
                    let pendingApproval =response.result?.list || [];
                    let total = response.result?.total || 0;
                    if(this.pageNum >=2) {
                        this.list = this.list.concat(pendingApproval);
                        this.list.length >= total ? this.noMore = true : this.noMore = false;
                    }else {
                        if(total >=1) {
                            this.isEmpty = false;
                            this.list = pendingApproval;
                            this.list.length >= total ? this.noMore = true : this.noMore = false;
                        }else {
                            this.isEmpty = true
                        }
                        
                    }
                }
            })
            uni.hideLoading()
        },
        scrollToLower() {
			if (this.noMore) return;
            this.pageNum++;
            this.isParams();
		},
        bindrefresherrefresh() {
			this.refresherState = true;
			this.pageNum = 1;
			this.noMore = false;
            this.isParams();
			setTimeout(() => {
				this.refresherState = false;
                this.$toast('刷新成功')
			}, 1000);
		},
        getPamams() {
			let params = {
				pageNum: this.pageNum,
				pageSize: this.pageSize
			}
			return params
		},
        isParams() {
            let params = this.getPamams();
            this.getList(params);
        },
        info(item) {
            uni.navigateTo({
                url: '/myPackge1/pages/transport/detailList?purchaseOrderId=' + item.purchaseOrderId
            })
        }
    },
}
</script>

<style scoped lang="scss">
    @import "@/common/css/listItem.scss";
    .header{
        width: 750rpx;
        height: 727rpx;
        display: flex;
        padding-top: 120rpx;
        box-sizing: border-box;
        position: relative;
        background: url(https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/header_bg/yuanshu.png) no-repeat 100% 100%;
        background-size: 100% 100%;
        position: relative;
    }
    .main{
        margin-top: -372rpx;
    }
</style>