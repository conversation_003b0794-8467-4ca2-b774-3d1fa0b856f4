<template>
    <view class="main">
        <!-- <scroll-view class="main" scroll-y :scroll-with-animation="true"> -->
            <rich-text :nodes="commitmentInfo.protocolContent"></rich-text>
            <view class="row" style="margin-top: 20px;">
                <view>承诺人（签字）： </view>
                <view><img :src="commitmentInfo.farmerSignUrl" v-if="commitmentInfo.farmerSignUrl" alt=""></view>
            </view>
            <view class="row">养殖地址：{{ commitmentInfo.farmersAddress || '' }}</view>
            <view class="row">联系电话：{{ commitmentInfo.farmersPhone || '' }}</view>
            <view class="row">身份证号码：{{ commitmentInfo.farmersIdcard || '' }}</view>
            <view class="row" style="margin-bottom: 100rpx;">签订日期：{{ commitmentInfo.catchTime || currentDate }}</view>
            <view class="sign-btn" v-if="commitmentInfo.catchStatus == 2" @click="handleHandwriting">签字</view>
            <view :style="'height:' + (isIphonex ? 120 : 24) + 'rpx'"></view>
        <!-- </scroll-view> -->
    </view>
</template>

<script>
import { selectCommitmentInfo } from '@/api/pages/commitment'

export default {
    data() {
        return {
            isIphonex: getApp().globalData.systemInfo.isIphonex,
            context: '',
            currentDate: this.getCurrentDate(),
            shortId: '',
            commitmentInfo: {}
        }
    },
    props: {
        currentShortId: {
            type: String,
            default: ''
        }  
    },
    watch: {
        currentShortId(newVal, oldVal) {
            console.log(newVal, oldVal)
            this.shortId = newVal
            this.getSelectCommitmentInfo(this.shortId)
        }
    },
    mounted() {
        if (this.currentShortId) {
            this.getSelectCommitmentInfo(this.currentShortId)
            this.shortId = this.currentShortId
        }
    },
    onLoad(option) {
        const id = this.$route
        console.log(id)
        console.log(option)
        this.shortId = option.shortId;
        this.getSelectCommitmentInfo(this.shortId)
        uni.$on('updateCommitmentInfo', () => {
            this.getSelectCommitmentInfo(this.shortId);
        })
    },

    onUnload() {
        uni.$off('updateCommitmentInfo');
    },

    methods: {
        getCurrentDate() {
            const date = new Date();
            return date.toISOString().split('T')[0];
        },
        getSelectCommitmentInfo(shortId) {
            selectCommitmentInfo({ shortId }).then(res => {
                if (res.code == 200) {
                    this.commitmentInfo = res.result;
                }
            })
        },
        handleHandwriting() {
            uni.navigateTo({
                url: `/myPackge3/pages/handwriting?shortId=${this.shortId}`
            })
        }
    }
}
</script>
  
<style lang="less" scoped>
.main {
    width: 100%;
    // height: 100vh;
    box-sizing: border-box;
    padding: 30rpx;
    background: #fff;
    overflow: auto;
    // padding-bottom: 120rpx;

    :deep(v-html) {
        width: 100%;
        height: 100%;
        overflow-y: auto;
        word-break: break-all;
        line-height: 1.5;
        font-size: 28rpx;
        color: #333;
    }
}

.row {
    min-height: 60rpx;
    display: flex;
    align-items: center;

    img {
        width: 140rpx;
        height: 140rpx;
        border-radius: 20rpx;
        margin-left: 20rpx;
    }
}

.sign-btn {
    position: fixed;
    z-index: 9;
    left: 60rpx;
    right: 60rpx;
    bottom: 40rpx;
    height: 80rpx;
    font-size: 32rpx;
    font-weight: 500;
    line-height: 80rpx;
    text-align: center;
    border-radius: 40rpx;
    color: #fff;
    font-family: PingFangSC-Regular, PingFang SC;
    background: #40CA8F;
}
</style>