<!-- 个人中心页 -->
<template>
	<view class="mine-container">
		<!-- 自定义导航栏 -->
		<CustomNavbar :title="'我的'" :bgColor="'#4CD964'" :titleColor="'#FFFFFF'" :showBack="false" />
		<!-- 个人信息区域 -->
		<view class="header">
			<view class="content">
				<image v-if="avatarUrl" class="avatar" :src="avatarUrl" alt="" @click="unloadImage" />
				<image v-else class="avatar"
					src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/mine/touxiang.png" alt=""
					@click="unloadImage" />
				<view class="user-info">
					<text class="username">{{ name }}</text>
					<text class="company">{{ companyName }}</text>
				</view>
			</view>
			<!-- <view class="tag-container" v-if="roleList && roleList.length">
				<view class="tag-btn">
					<text>牛经纪</text>
				</view>
			</view> -->
		</view>

		<!-- 功能菜单区域 -->
		<view class="menu-content">
			<view class="menu-list">
				<view class="menu-item" @click="retrievePwd">
					<view class="menu-left">
						<image class="menu-icon"
							src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/mine/mima-2.png"></image>
						<text class="menu-text">修改密码</text>
					</view>
					<view class="menu-right">
						<u-icon name="arrow-right" color="#333" size="22"></u-icon>
					</view>
				</view>

				<view class="menu-item" @click="goToDownloadCenter">
					<view class="menu-left">
						<image class="menu-icon down"
							src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/mine/Tab_xiazaizhongxin.png">
						</image>
						<text class="menu-text">下载中心</text>
					</view>
					<view class="menu-right">
						<u-icon name="arrow-right" color="#333" size="22"></u-icon>
					</view>
				</view>

				<view class="menu-item" @click="protocol('yonghu')">
					<view class="menu-left">
						<image class="menu-icon"
							src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/mine/yonghuxieyi.png">
						</image>
						<text class="menu-text">用户协议</text>
					</view>
					<view class="menu-right">
						<u-icon name="arrow-right" color="#333" size="22"></u-icon>
					</view>
				</view>

				<view class="menu-item" @click="protocol('yinsi')">
					<view class="menu-left">
						<image class="menu-icon"
							src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/mine/yinsizhengce.png">
						</image>
						<text class="menu-text">隐私政策</text>
					</view>
					<view class="menu-right">
						<u-icon name="arrow-right" color="#333" size="22"></u-icon>
					</view>
				</view>

				<view class="menu-item" @click="exit">
					<view class="menu-left">
						<image class="menu-icon"
							src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/mine/tuichudenglu-2.png">
						</image>
						<text class="menu-text">退出登录</text>
					</view>
					<view class="menu-right">
						<u-icon name="arrow-right" color="#333" size="22"></u-icon>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>
<script>
import { saveAvatar } from '@/api/account.js'
import { uploadFiles } from '@/api/obsUpload/index'
import { getStorage } from '@/common/utils/storage.js'
import { mapState } from "vuex"
import CustomNavbar from '@/components/uni-custom-navbar/CustomNavbar.vue'

export default {
	components: {
		CustomNavbar
	},
	data() {
		return {
			avatarUrl: '',
			name: '',
			companyName: '',
			roleList: []
		}
	},
	onShow() {
		const userInfo = getStorage('sysUser')
		this.roleList = getStorage('roleList')
		this.avatarUrl = userInfo?.avatar || ''
		this.name = userInfo?.nickName || userInfo?.userName || ''
		this.companyName = userInfo?.enterpriseModel?.companyName || ''
	},
	methods: {
		/**
		 * 退出登录
		 */
		exit() {
			uni.showModal({
				title: '提示',
				content: '是否退出登录？',
				success: async (res) => {
					if (res.confirm) {
						uni.clearStorageSync();
						uni.reLaunch({
							url: '/pages/account/login/login'
						});
					}
				}
			})
		},

		/**
		 * 跳转到修改密码页面
		 */
		retrievePwd() {
			uni.navigateTo({
				url: '/pages/account/retrievePwd'
			});
		},

		/**
		 * 跳转到下载中心
		 */
		goToDownloadCenter() {
			uni.navigateTo({
				url: '/pages/downloadCenter/index'
			});
		},

		/**
		 * 跳转到协议页面
		 * @param {String} type - 协议类型：yonghu-用户协议，yinsi-隐私政策
		 */
		protocol(type) {
			uni.navigateTo({
				url: `/myPackge4/pages/protocol?type=${type}`
			});
		},

		/**
		 * 上传头像
		 */
		unloadImage() {
			const that = this
			uni.chooseImage({
				count: 1, // 默认9
				sizeType: ['original', 'compressed'], // 可以指定是原图还是压缩图，默认二者都有
				sourceType: ['album', 'camera'], // 从相册选择
				name: 'file',
				success: function (res) {
					uploadFiles({
						filePath: res.tempFilePaths[0],
					}).then((data) => {
						that.avatarUrl = data
						that.saveAvatarFn()
					})
				},
				fail(e) { },
			})
		},
		saveAvatarFn() {
			saveAvatar({
				avatar: this.avatarUrl,
			}).then((data) => {
				uni.showToast({
					title: '修改成功',
					icon: 'none',
					duration: 2000,
				})
				this.$store.dispatch('GetInfo').then(() => { })
			})
		}
	},
}
</script>
<style scoped lang="scss">
.mine-container {
	background-color: #fff;
	min-height: 100vh;
	position: relative;
}

.header {
	width: 100%;
	// height: 440rpx;
	height: 390rpx;
	background-color: #4CD964;
	display: flex;
	flex-direction: column;
	padding-top: 120rpx;
	/* 导航栏空间 */
	box-sizing: border-box;
	position: relative;
	background: url(https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/index/mine_bg.png) no-repeat 100% 100%;
	background-size: 100% 100%;

	.content {
		display: flex;
		align-items: center;
		padding: 0 30rpx;
		margin-top: 80rpx;

		.avatar {
			width: 104rpx;
			height: 104rpx;
			border-radius: 50%;
			margin-right: 30rpx;
			background-color: rgba(255, 255, 255, 0.3);
			border: 2rpx solid rgba(255, 255, 255, 0.6);
		}
	}

	.tag-container {
		padding: 16rpx 30rpx 0;

		.tag-btn {
			display: inline-block;
			padding: 4rpx 16rpx;
			border-radius: 30rpx;
			border: 2rpx solid #FFFFFF;
			background-color: rgba(255, 255, 255, 0.1);

			text {
				font-size: 24rpx;
				color: #FFFFFF;
				line-height: 32rpx;
			}
		}
	}
}

.user-info {
	display: flex;
	flex-direction: column;
	justify-content: center;

	.username {
		font-size: 32rpx;
		font-weight: bold;
		color: #FFFFFF;
		margin-bottom: 10rpx;
		font-style: normal;
		font-family: SourceHanSansSC, SourceHanSansSC;
	}

	.company {
		font-size: 26rpx;
		color: rgba(255, 255, 255, 0.9);
		font-weight: 400;
		font-style: normal;
		font-family: SourceHanSansSC, SourceHanSansSC;
	}
}

.menu-content {
	width: 100%;
	background: #FFFFFF;
	border-radius: 30rpx 30rpx 0rpx 0rpx;
	overflow: hidden;
	position: relative;
	z-index: 10;
	margin-top: -30rpx;
}

.menu-list {
	padding: 0 50rpx;
	background-color: #FFFFFF;
	padding-top: 10rpx;
}

.menu-item {
	height: 100rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;

	.menu-left {
		display: flex;
		align-items: center;

		.menu-icon {
			width: 35rpx;
			height: 35rpx;
			margin-right: 28rpx;
		}
		/* .down{
			width: 44rpx;
			margin-right: 19rpx;
		} */

		.menu-text {
			font-size: 28rpx;
			color: #333333;
			font-weight: 400;
			font-family: SourceHanSansCN, SourceHanSansCN;
			font-style: normal;
		}
	}

	.menu-right {
		display: flex;
		align-items: center;
	}
}
</style>