.bill {
	background-color: #f7f7f7;
	min-height: 100vh;
	padding: 30rpx 30rpx 0 30rpx;
	box-sizing: border-box;

	&-numtit {
		font-size: 32rpx;
		font-weight: normal;
		color: #262630;
	}
	&-numbx {
		font-size: 28rpx;
		color: #898c9d;
	}
	&-num {
		color: #ff891f !important;
		padding-right: 5rpx;
	}

	&-auth {
		width: 95%;
		margin: 0 auto;
		height: 90rpx;
		background: rgba(0, 0, 0, 0.25);
		border-radius: 100px;
		padding: 0 30rpx;
		position: fixed;
		bottom: 90px;
		left: 0;
		right: 0;
	}
	&-authworn {
		font-size: 28rpx;
		font-weight: normal;
		color: #ffffff;
		line-height: 90rpx;
	}
	&-authbtn {
		width: 150rpx;
		height: 60rpx;
		background: #ff891f;
		border-radius: 100px;
		font-size: 28rpx;
		color: #ffffff;
		line-height: 60rpx;
		text-align: center;
	}
	&-table {
		padding: 30rpx;
		box-sizing: border-box;
	}
}
.line {
	margin-top: 38rpx;
}
.table {
	font-family: PingFang SC-中等, PingFang SC;
	
	margin-top: 38rpx;

	&-item {
		padding: 30rpx;
		box-sizing: border-box;
		margin-bottom: 30rpx;
		background-color: #fff;
		border-radius: 16rpx;
	}
	.thcolor {
		color: #ff891f;
	}
	.graycolor{
		color: #878A9B;
	}
	&-bgtit {
		font-size: 28rpx;
		font-weight: normal;
		color: #262630;
		max-width: 530rpx;
	}
	&-status {
		font-size: 28rpx;
	}
	&-tagst{
		background: #FF891F;
	}
	&-common{
		font-size: 20rpx;
		font-weight: normal;
		color: #FFFFFF;
		width: 35rpx;
		height: 35rpx;
		border-radius: 50%;
		text-align: center;
		line-height:35rpx;
		margin-right: 33rpx;
	}
	&-tagzd{
		background: #00B578;
	}
	&-tagname{
		font-size: 24rpx;
		color: #4C5165;
		max-width: 500rpx;
	}
	&-tabsbx{
		margin-bottom: 34rpx;
	}
	&-tabbox{
		width: 630rpx;
		height: 150rpx;
		background: #FFFAF7;
		border-radius:8rpx;
		margin-top: 40rpx;
		padding: 26rpx 0 26rpx 30rpx;
		box-sizing: border-box;
	}
	&-jietime{
		font-size: 24rpx;
		color: #878A9B;
		margin-top: 30rpx;
	}
	&-car{
		font-size: 28rpx;
		color: #272731;
		margin-top: 30rpx;
	}
	&-carline{
		background-color: #ddd;
		width: 4rpx;
		height:40rpx;
		margin: 0 30rpx;
	}
	&-btncom{
		height: 60rpx;
		text-align: center;
		line-height: 60rpx;
		border-radius: 100rpx ;
		font-size: 28rpx;
		padding: 0 20rpx;
	}
	&-btncom:active{
		opacity: 0.6;
	}
	&-hadelbtn{
		justify-content: flex-end;
		margin-top: 40rpx;
	}
	&-closebtn{
		background: #FFF1E6;
		color: #FF891F;
		margin-right: 30rpx;
	}
	&-codebtn{
		background: #FF891F;
		color: #fff;
	}
}
.qrcode{
	font-family: PingFang SC-中等, PingFang SC;
	.qrcode-img{
		width: 598rpx;
		height: 599rpx;
		border-radius: 30rpx;
		margin-top: 162rpx;
	}
	.qrcode-dese{
		font-size: 24rpx;
		color: #888C9D;
		line-height: 48rpx;
		margin-top: 55rpx;
		text{
			display: block;
		}
	}
}
.banner{
	width: 750rpx;
	height: 708rpx;
	position: relative;
	image{
		width: 750rpx;
		height: 708rpx;
	}
	.cow{
		width: 318rpx;
		height: 357rpx;
		position: absolute;
		right: 0;
		bottom: 88rpx;
	}
}