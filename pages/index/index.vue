<!-- 首页 -->
<template>
	<view class="page-container">
		<CustomNavbar 
		title="牛贸帮" 
		:titleColor="'#FFFFFF'"
		:rightContent="true"
		:showBack="false"
		></CustomNavbar>
		<view class="banner">
			<!--  -->
			<view class="header">
				<view class="content" v-if="tradingCompanyList.length > 1">
					<view class="business">
						<view class="topitem" @click="showTenantModel = true;">
							<span class="text">{{ companyName }}</span>
							<u-icon size="24" :name="showTenantModel ? 'arrow-up' : 'arrow-down'" style="margin-left: 15rpx;"></u-icon>
						</view>
					</view>
				</view>
			</view>
			<view class="companyName">
				<view class="tag">
					<span>H</span>
					<span class="e">e</span>
					<span>l</span>
					<span>l</span>
					<span>o</span>
					<span>!</span>
				</view>
				<view class="name">{{ companyName }}</view>
			</view>
			<img :src="`${obs}/nmb-mini/index/cow.png`" alt="" class="cow"/>
		</view>
		<view class="ccontent">
			<view class="citem" @click="goURL('/myPackge1/pages/salesContract/index')" v-if="$hasPermi('nmb:saleContract:list')">
				<img class="sign" :src="`${obs}/nmb-mini/index/hetong.png`" alt="">
				<!-- <view class="">
					合同管理
				</view> -->
			</view>
			<view class="citem" @click="goURL('/myPackge1/pages/weeklyOrders/index?goType=1')" v-if="$hasPermi('nmb:demandOrder:list')">
				<img class="sign" :src="`${obs}/nmb-mini/index/zhoudingdan.png`" alt="">
				<!-- <view class="">
					周订单管理
				</view> -->
			</view>			
			<view class="citem" @click="goURL('/myPackge1/pages/purchase/index?goType=1')" v-if="$hasPermi('nmb:purchaseOrder:list')">
				<img class="sign" :src="`${obs}/nmb-mini/index/caigou.png`" alt="">
				<!-- <view class="">
					采购管理 
				</view> -->
			</view>
			<view class="citem" @click="goURL('/myPackge1/pages/sale/index')" v-if="$hasPermi('nmb:saleOrder:list')">
				<img class="sign" :src="`${obs}/nmb-mini/index/xiaoshou.png`" alt="">
				<!-- <view class="">
					销售管理
				</view> -->
			</view>
			<view class="citem" @click="goURL('/myPackge1/pages/finance/index?goType=1')" v-if="$hasPermi('nmb:financeOrder:list')">
				<img class="sign" :src="`${obs}/nmb-mini/index/caiwu.png`" alt="">
				<!-- <view class="">
					财务管理
				</view> -->
			</view>
			<!-- v-if="$hasPermi('ffs:app:supervise:query:list')" -->
			<view class="citem" @click="goURL('/myPackge1/pages/statistical/index')" v-if="$hasPermi('nmb:statsAnalysis')">
				<img class="sign" :src="`${obs}/nmb-mini/index/chaxun.png`" alt="">
				<!-- <view class="">
					查询分析
				</view> -->
			</view>
			<view class="citem" @click="goURL('/myPackge1/pages/transport/index')" v-if="$hasPermi('nmb:maintenance:list')">
				<img class="sign" :src="`${obs}/nmb-mini/index/yunshu.png`" alt="">
				<!-- <view class="">
					运输保养
				</view> -->
			</view>
			<!-- todo 需要增加权限 -->
			 <!-- 活畜管理 -->
			 <view class="citem" @click="goURL('/myPackge5/pages/index')">
				<img class="sign" :src="`${obs}/nmb-mini/index/yunshu.png`" alt="">
			 </view>
		</view>
		<u-select confirm-color='#40CA8F' v-model="showTenantModel" :list="tradingCompanyList" value-name="tenantId" label-name="companyName" @cancel="showTenantModel = false" @confirm="changeTenant"></u-select>
	</view>
</template>
<script>
import {
	GetPermissionss,
} from '@/common/utils/token.js'
import {
	info, myTradingList
} from '@/api/account.js'
import { getStorage, setStorage } from '@/common/utils/storage.js'
import CustomNavbar from '@/components/uni-custom-navbar/CustomNavbar.vue';
export default {
	components: {
		CustomNavbar
	},
	data() {
		return {
			obs:"https://xmb-new02.obs.cn-north-4.myhuaweicloud.com",
			type: "error",
			permissions: GetPermissionss(), //权限
			showTenantModel: false,
			tradingCompanyList: [],
			companyName: ''
		}
	},
	async onLoad(options) {
		// 获取交易公司列表
		this.tradingCompanyList = getStorage('tradingCompanyList')
		if (!this.tradingCompanyList.length) {
			uni.showToast({
				title: '暂无交易公司',
				icon: 'none'
			})
			this.companyName = '暂无交易公司'
			return
		}
		// 获取交易公司名称
		this.getCompanyName()
	},
	onShow() {
		this.getTradingList()
	},
	onReady() {
		this.$store.dispatch('GetInfo').then((res) => {
			console.log(res);
		})
	},
	methods: {
		getTradingList() {
			myTradingList().then(res => {
				this.tradingCompanyList = res.result
				if (res.result.length == 1) {
					setStorage('tenantId', res.result[0].tenantId)
				}
				setStorage('tradingCompanyList', res.result)
				this.getCompanyName()
			})
		},
		getCompanyName() {
			const tradingTenantId = getStorage('tenantId')
			if (tradingTenantId) {
				this.companyName = this.tradingCompanyList.find(item => item.tradingTenantId === tradingTenantId)?.companyName
				info({
				}).then(res => {
					setStorage('sysUser', res.result.sysUser)
					setStorage('roleList', res.result.roleList)
				})
			}
		},
		changeTenant(val) {
			const values = val[0]
			this.companyName = values.label
            this.$store.commit('user/SET_TENANTID',values.value)
			this.showTenantModel = false
			info({
				tradingTenantId: values.value
			}).then(res => {
				setStorage('permissions', res.result.permissions)
				setStorage('sysUser', res.result.sysUser)
				wx.startPullDownRefresh();
				setTimeout(() => {
					wx.stopPullDownRefresh();		
				})
			})
		},
		
		goURL(url){
			uni.navigateTo({
				url: url
			})
		},
		// earlywarning() {
		// 	// console.log("统计分析")
		// 	uni.navigateTo({
		// 		url: '/myPackge3/pages/statisticAnalysis/index'
		// 	})
		// },
		// regulatoryintention() {
		// 	// console.log("预警提醒")
		// 	uni.navigateTo({

		// 	})
		// },
		// 采购计划
        // purchase() {
        //     uni.navigateTo({
        //         url: '/myPackge1/pages/purchase/index?goType=1'
        //     })
        // },
        // sale() {
        //     uni.navigateTo({
        //         url: '/myPackge1/pages/sale/index'
        //     })
        // },
		// statisticanalysis() {
		// 	uni.navigateTo({
		// 		url: '/myPackge1/pages/transport/index'
		// 	})
		// },
		// fundflow() {
		// 	// console.log("统计分析")
		// 	uni.navigateTo({
		// 		url: '/myPackge1/pages/statistical/index'
		// 	})
		// },
		// livepledge() {
		// 	// console.log("销售合同")
		// 	uni.navigateTo({
		// 		url: '/myPackge1/pages/salesContract/index'
		// 	})
		// },
		// weeklyOrders() {
		// 	// console.log("资金流向")
		// 	uni.navigateTo({
		// 		url: '/myPackge1/pages/weeklyOrders/index?goType=1'
		// 	})
		// },
		// finance() {
		// 	// console.log("资金流向")
		// 	uni.navigateTo({
		// 		url: '/myPackge1/pages/finance/index?goType=1'
		// 	})
		// },
		clearCanvas() {
			// 调用 Handwriting 组件的清空画布方法
			this.$refs.handwriting.clearCanvas()
		},
		saveImage() {
			// 调用 Handwriting 组件的保存图片方法
			this.$refs.handwriting.saveImage()
		},
	},
}
</script>
<style scoped lang="scss">
.page-container{
	background-color: #fff!important;
}
.cbox {
	position: relative;
}

.badge {
	margin-left: -5rpx;
}

/**
 * 让元素平均分配
 */
.ccontent {
	display: flex;
	flex-wrap: wrap;
	justify-content: start;
	width: 750rpx;
	background: #FFFFFF;
	border-radius: 30rpx 30rpx 0rpx 0rpx;
	backdrop-filter: blur(14px);
	margin-top: -78rpx;
	padding: 0 40rpx;
	padding-top: 86rpx;
	gap: 40rpx;
}

.citem {
	width: 198rpx;
	height: 195rpx;
	margin-bottom: 40rpx;
}

.sign {
	width: 198rpx;
	height: 195rpx;
}

.header {
	font-weight: 400;
	font-size: 26rpx;
	color: #FFFFFF;
	height: 57rpx;
	line-height: 57rpx;
	margin-top: 209rpx;
}

.business {
	display: flex;
	flex-direction: row;
	align-items: center;
	margin-top: 50rpx;
	padding-left: 20rpx;
}

.topitem {
	display: flex;
	flex-direction: row;
	align-items: center;
	height: 57rpx;
	text-align: center;
	background: rgba(25,176,101,0.8);
	border-radius: 29rpx;
	padding: 0 25rpx;
}
.companyName{
	margin-top: 40rpx;
	padding-left: 45rpx;
	color: #FFFFFF;
	
	.tag{
		font-family: AlibabaPuHuiTi_3_105_Heavy;
		font-size: 106rpx;
		line-height: 106rpx;
		font-weight: bold;
		.e{
			color:#FFDD8C;
		}
	}
	.name{
		width: 378rpx;
		font-family: AlibabaPuHuiTi_3_95_ExtraBold;
		font-size: 40rpx;
		margin-top: 10rpx;
	}
}
.tick {
	width: 20rpx;
	height: 20rpx;
	margin-right: 5rpx;
}

.banner{
	width: 750rpx;
	height: 660rpx;
	position: relative;
	background: url(https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/index/index_bg.png) no-repeat 100% 100%;
	background-size: 100% 100%;
	.cow{
		width: 318rpx;
		height: 365rpx;
		position: absolute;
		right: 0;
		bottom: 32rpx;
		z-index: 1;
	}
}
</style>