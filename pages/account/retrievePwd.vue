<template>
	<view>
		<scroll-view class="main" scroll-y :scroll-with-animation="true">
			<view class="container">
				<u-form :model="form" ref="uForm" :error-type="errorType" label-width="auto" label-position="top">
					<u-form-item label="旧密码" prop="oldPassword">
						<u-input v-model="form.oldPassword" placeholder="请输入旧密码" />
					</u-form-item>
					<u-form-item label="新密码" prop="newPassword">
						<u-input v-model="form.newPassword" placeholder="请输入新密码" type="password"/>
					</u-form-item>
					<u-form-item label="确认密码" prop="confirmPassword">
						<u-input v-model="form.confirmPassword" placeholder="请输入确认密码" type="password" />
					</u-form-item>
				</u-form>
			</view>
		</scroll-view>
		<view class="add-btn" @click="submit">确 认</view>
	</view>
</template>
<script>
import { sourcePwdToRetrievePwd } from '@/api/account.js'
export default {
data() {
	return {
		form: {
			oldPassword:'',
			newPassword:'',
			confirmPassword:''
		},
		certificate: false,
		errorType: ['message'],
		rules: {
			oldPassword: [
				{
					required: true,
					message: '请输入旧密码',
					trigger: 'blur',
				},
			],
			newPassword: [
				{
					required: true,
					message: '请输入新密码',
					trigger: 'blur',
				},
				{
					pattern: /^[0-9a-zA-Z]*$/g,
					transform(value) {
						return String(value);
					},
					message: '只能包含字母或数字'
				},
				{
					min: 6, 
					message: '新密码不能少于6位数', 
					trigger: 'change'
				}
			],
			confirmPassword: [
				{
					required: true,
					message: '请输入确认密码',
					trigger: 'blur',
				},
				{
					validator: (rule, value, callback) => {
						return this.form.newPassword === value ? true : false;
					},
					message: '确认密码与新密码不一致',
					trigger: ['change','blur'],
				}
			],
		},
	}
},
onLoad(opation) {
},
onReady() {
	this.$refs.uForm.setRules(this.rules)
},
methods: {
	submit() {
		this.$refs.uForm.validate((valid) => {
			if (valid) {
				sourcePwdToRetrievePwd({
					...this.form,
				}).then(res => {
					if (res.code == 200) {
						uni.showToast({
							title: '支付成功',
							icon: 'none'
						})
						uni.navigateBack({
							delta:1
						})
					}
				})
			}
		})
	},
},
}
</script>

<style lang="less" scoped>
.container {
	margin: 30rpx;
	background: #fff;
	box-sizing: border-box;
	padding: 30rpx 32rpx 40rpx;
	border-radius: 30rpx;

	/deep/ .u-form-item {
		padding: 20rpx 20rpx !important;
	}
}
.add-btn {
	position: fixed;
	z-index: 9;
	left: 60rpx;
	right: 60rpx;
	bottom: 40rpx;
	height: 80rpx;
	font-size: 32rpx;
	font-weight: 500;
	line-height: 80rpx;
	text-align: center;
	border-radius: 40rpx;
	color: #fff;
	font-family: PingFangSC-Regular, PingFang SC;
	background: #40CA8F;
}
</style>