<template>
	<view class="login">
		<view class="login-from">
			<LoginByPsd />
		</view>
		<g-show-modal />
	</view>
</template>

<script>
	import config from '@/config/index'
	import LoginByPsd from './components/LoginByPsd.vue'
	export default {
		components: {
			LoginByPsd,
		},
		name: 'login',
		data() {
			return {
				background: {
					backgroundColor: '#FF891F',
				},
				loginType: 2,
				serverUrl: '',
				agree: false,
			}
		},
		onShow() {
			this.serverUrl = config.initEnv().BASE_URL
			console.log(this.serverUrl)
		},
		methods: {},
	}
</script>

<style scoped lang="scss">
	.dens {
		text-align: center;
		font-size: 20rpx;
		color: #999;
		margin-top: 20rpx;
	}
	.login{
		background: #fff;
		height: 100vh;
	}
	// .login-from {
	// 	 	padding: 0 40rpx;
	// }
	.login-btn {
		width: 100%;
		height: 88rpx;
		line-height: 88rpx;
		text-align: center;
		background: #FF891F;
		color: #fff;
		font-size: 32rpx;
		border-radius: 44rpx;
		margin-top: 60rpx;
	}
	.login-btn::after {
		border: none;
	}
</style>