<template>
    <view style="padding: 20rpx 40rpx; background-color: #fff;">
        <scroll-view class="main" scroll-y>
            <rich-text :nodes="context"></rich-text>
        </scroll-view>
    </view>
</template>

<script>
import { seleteProtocol } from '@/api/common.js'
export default {

    data() {
        return {
            context: ''
        }
    },
    onLoad(opation) {
        this.getContent(opation.type)
    },
    methods: {
        getContent(protocolType) {
            seleteProtocol({
                product: 'nmb',
                protocolType
            }).then(res => {
                if (res.code == 200) {
                    this.context = res.result.context
                }
            })
        }
    }
}
</script>

<style></style>