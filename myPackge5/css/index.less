
.main {
    min-height: calc(100vh - 126rpx);
    background-color: #F7F8F7;
    padding-bottom: 126rpx;
}

.container {
    margin: 25rpx;
    background: #fff;
    box-sizing: border-box;
    padding: 30rpx 32rpx 40rpx;
    border-radius: 30rpx;

    /deep/ .u-form-item {
        padding: 20rpx 20rpx !important;
    }

    .tips {
        font-size: 28rpx;
        color: #999;
    }
}

.bg-box {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 126rpx;
    background-color: #FFFFFF;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 100;
    padding: 20rpx 30rpx;
    box-sizing: border-box;
}

.add-btn {
    width: 100%;
    height: 86rpx;
    background: linear-gradient(101deg, #19AF77 0%, #40CA8F 100%);
    border-radius: 50rpx;
    font-weight: 600;
    font-size: 34rpx;
    color: #FFFFFF;
    text-align: center;
    line-height: 86rpx;
}

.tips {
    font-size: 28rpx;
    color: #c0c3ca;
}

.common {
    font-size: 28rpx;
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    color: #333;
    margin: 10rpx;
    text-align: right;
}

.cultivate-section {
    margin: 25rpx 0;
    background: #fff;
    border-radius: 30rpx;

    .section-title {
        font-size: 26rpx;
        color: #333;
        margin-bottom: 30rpx;
        font-weight: 500;
    }

    .cultivate-options {
        display: flex;
        flex-wrap: wrap;
        gap: 20rpx;

        .cultivate-item {
            padding: 16rpx 40rpx;
            border-radius: 50rpx;
            font-size: 26rpx;
            color: #666;
            background-color: #F5F5F5;
            border: 2rpx solid transparent;
            transition: all 0.3s;
            height: 60rpx;
            display: flex;
            align-items: center;
            justify-content: center;

            &.active {
                color: #1DB17A;
                background-color: #fff;
                border-color: #1DB17A;
            }

            &.disabled {
                cursor: not-allowed;
                opacity: 0.6;
            }
        }
    }
}

.upload-section {
    background: #fff;
    padding: 30rpx;
    border-radius: 30rpx;
    margin: 0 30rpx;

    .section-title {
        font-size: 26rpx;
        color: #333;
        margin-bottom: 10rpx;
        font-weight: 500;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .upload-icon {
            width: 40rpx;
            height: 40rpx;
        }
    }

    .section-subtitle {
        font-size: 26rpx;
        color: #999;
        margin-bottom: 30rpx;
        font-weight: 400;
    }

    .uploadImage {
        display: flex;
        flex-wrap: wrap;
        gap: 20rpx;

        .itemAlready {
            width: 140rpx;
            height: 140rpx;
            border-radius: 8rpx;
            position: relative;
            margin: 0 20rpx 10rpx 0rpx;

            image {
                width: 100%;
                height: 100%;
                border-radius: 8rpx;
            }

            video {
                width: 100%;
                height: 100%;
                border-radius: 8rpx;
            }


            .closeIcon {
                width: 32rpx;
                height: 32rpx;
                background-image: url('../../static/modalImg/error.png');
                position: absolute;
                background-size: cover;
                top: -10rpx;
                right: -10rpx;
            }

            .close-file {
                top: 5rpx;
                right: -20rpx;
            }
        }

        /* .itemAlready {
            position: relative;
            width: 140rpx;
            height: 140rpx;
            border-radius: 16rpx;
            overflow: hidden;

            image {
                width: 100%;
                height: 100%;
                border-radius: 16rpx;
            }

            .closeIcon {
                position: absolute;
                top: -10rpx;
                right: -10rpx;
                width: 32rpx;
                height: 32rpx;
                background-image: url('@/static/modalImg/error.png');
                background-size: cover;
                z-index: 10;
            }
        } */
    }
}