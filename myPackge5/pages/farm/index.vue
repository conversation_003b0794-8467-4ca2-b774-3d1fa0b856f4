<template>
    <view class="container">
        <CustomNavbar title="养殖场管理" :titleColor="'##333333'" />
        <scroll-view class="content" :style="{ paddingTop: navbarTotalHeight + 'px' }" scroll-y refresher-enabled
            :refresher-triggered="refreshing" @refresherrefresh="onRefresh" @scrolltolower="onLoadMore">
            <view v-for="item in farmList" :key="item.pastureId" class="farm-item">
                <view class="farm-info" @click="viewDetail(item)">
                    <view class="farm-header">
                        <text class="farm-name">{{ item.pastureName }}</text>
                        <view class="farm-nature">
                            <text class="nature-text">{{ item.pastureNatureName || '个人养殖户' }}</text>
                        </view>
                    </view>
                    <text class="farm-address">{{ item.address ||
                        `${item.provinceName}${item.cityName}${item.countyName}` }}</text>
                </view>

                <image class="edit-icon" src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/cow/edit.png"
                    @click.stop="editFarm(item)" />
            </view>

            <view v-if="loading" class="status-text">加载中...</view>
            <view v-else-if="!hasMore && farmList.length" class="status-text">没有更多数据了</view>
            <view v-else-if="!farmList.length" class="status-text">暂无数据</view>
        </scroll-view>
        <view class="container-footer"
            :style="'padding:' + (isIphonex ? '40rpx 30rpx;' : '15rpx 30rpx;') + 'background-color: #fff;'"
          >
            <u-button hover-class='none' :custom-style="{
                'background-color': '#19AF77',
                'color': 'white'
            }" shape="circle" @click="addFarm">新增养殖场</u-button>
        </view>
    </view>
</template>

<script>

import CustomNavbar from '../components/CustomNavbar.vue'
import { pasturePage } from '@/api/pages/livestock/farm'

export default {
    components: {
        CustomNavbar,
    },
    data() {
        return {
            systemInfo: uni.getSystemInfoSync(),
            isIphonex: getApp().globalData.systemInfo.isIphonex,
            farmList: [],
            loading: false,
            refreshing: false,
            hasMore: true,
            pageNum: 1,
            pageSize: 20
        }
    },
    onLoad() {
        this.loadFarmList();
        // 监听列表更新事件
        uni.$on('updateFarmList', () => {
            this.loadFarmList(true);
        });
    },
    onUnload() {
        // 移除事件监听
        uni.$off('updateFarmList');
    },
    onShow() { },
    computed: {
        navbarTotalHeight() {
            const statusBarHeight = this.systemInfo.statusBarHeight || 0;
            const navbarHeight = 64;
            return statusBarHeight + navbarHeight;
        }
    },
    methods: {
        async loadFarmList(refresh = false) {
            if (this.loading) return;

            this.loading = true;
            if (refresh) {
                this.refreshing = true;
                this.pageNum = 1;
                this.hasMore = true;
            }

            try {
                const { result } = await pasturePage({
                    pageNum: this.pageNum,
                    pageSize: this.pageSize
                });

                const list = result?.list || [];
                this.farmList = refresh ? list : [...this.farmList, ...list];
                this.hasMore = !result?.lastPage;

                if (this.hasMore) this.pageNum++;
            } catch (error) {
                uni.showToast({ title: '加载失败', icon: 'none' });
            } finally {
                this.loading = false;
                this.refreshing = false;
            }
        },

        editFarm(item) {
            uni.navigateTo({
                url: `/myPackge5/pages/farm/addFarm?pastureId=${item.pastureId}&mode=edit`
            });
        },

        viewDetail(item) {
            uni.navigateTo({
                url: `/myPackge5/pages/farm/addFarm?pastureId=${item.pastureId}&mode=detail`
            });
        },

        onRefresh() {
            this.loadFarmList(true);
        },

        onLoadMore() {
            if (this.hasMore && !this.loading) {
                this.loadFarmList();
            }
        },
        addFarm() {
            uni.navigateTo({
                url: '/myPackge5/pages/farm/addFarm'
            });
        },
    }
}
</script>

<style lang="scss" scoped>
.container {
    min-height: 100vh;
    background-color: #F7F8F7;
}

.content {
    height: calc(100vh - 126rpx);
    padding: 30rpx;
    box-sizing: border-box;
}

.farm-item {
    height: 202rpx;
    background-color: #FFFFFF;
    border-radius: 20rpx;
    margin-bottom: 30rpx;
    display: flex;
    align-items: center;
    padding: 40rpx 30rpx;
    box-sizing: border-box;
    position: relative;
}

.farm-info {
    flex: 1;

    .farm-header {
        display: flex;
        align-items: center;
        margin-bottom: 20rpx;

        .farm-name {
            font-weight: bold;
            font-size: 34rpx;
            color: #333333;
            margin-right: 18rpx;
        }

        .farm-nature {
            .nature-text {
                background: linear-gradient(140deg, #FFF9F0 0%, #FFF3D7 100%);
                color: #FF8C42;
                font-size: 26rpx;
                font-weight: 500;
                padding: 6rpx 16rpx;
                border-radius: 20rpx 0rpx 20rpx 0rpx;
                border: 1rpx solid #FFE4C4;
                line-height: 1.2;
                font-family: AlibabaPuHuiTi_3_105_Heavy;
            }
        }
    }

    .farm-address {
        font-weight: 400;
        font-size: 26rpx;
        color: #999999;
        line-height: 1.4;
    }
}

.edit-icon {
    position: absolute;
    right: 40rpx;
    width: 26rpx;
    height: 26rpx;
}

.status-text {
    text-align: center;
    padding: 40rpx 0;
    color: #999999;
    font-size: 28rpx;
}

.add-btn-container {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    width: 100%;
    height: 126rpx;
    background-color: #FFFFFF;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 100;
    padding: 20rpx 30rpx;

    .add-btn {
        width: 100%;
        height: 86rpx;
        background: linear-gradient(101deg, #19AF77 0%, #40CA8F 100%);
        border-radius: 50rpx;
        display: flex;
        align-items: center;
        justify-content: center;

        .add-btn-text {
            font-family: PingFangSC, PingFang SC;
            font-weight: 600;
            font-size: 34rpx;
            color: #FFFFFF;
        }
    }
}
</style>
