<template>
  <view class="under-care-container">
    <scroll-view
      class="under-care-list"
      scroll-y="true"
      :scroll-with-animation="true"
      @scrolltolower="scrollToLower"
      :refresher-enabled="true"
      :refresher-triggered="refresherState"
      @refresherrefresh="bindrefresherrefresh"
    >
      <view class="list-content">
        <view v-for="(item, index) in list" :key="index" class="list-item">
          <view class="item-header">
            <text class="item-time">{{ item.updateTime }}</text>
          </view>
          <view class="item-content">
            <view class="content-row">
              <text class="content-label">投喂圈舍：</text>
              <text class="content-value">{{ item.pastureName || 'xxxx' }}</text>
            </view>
            <view class="content-row">
              <text class="content-label">饲料种类：</text>
              <!-- dict-value -->
              <text class="content-value">{{ getFeedFoodText(item.feedFood) }}</text>
            </view>
            <view class="content-row">
              <text class="content-label">投喂数量：</text>
              <text class="content-value">{{ item.feedNum }}Kg</text>
            </view>
            <view class="content-row">
              <text class="content-label">投喂频次：</text>
              <text class="content-value">{{ getFeedFrequencyText(item.feedFrequency) }}</text>
            </view>
            <view class="content-row" v-if="item.manageRemark">
              <text class="content-label">备注：</text>
              <text class="content-value remark-text">{{ item.manageRemark }}</text>
            </view>
          </view>
        </view>

        <nullList v-if="isEmpty" />
        <view v-if="!noMore && list.length > 0" class="load-more">加载更多...</view>
        <view v-if="noMore && list.length > 0" class="load-more">没有更多数据了</view>
      </view>
    </scroll-view>

    <view class="fixed-add-btn" @click="addRecord">
      <img src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/xiaoshouhetong/add.png" alt="" />
    </view>
  </view>
</template>

<script>
import { feedPage } from '@/api/pages/livestock/underCare'
import { getDicts } from '@/api/dict.js'
import nullList from '@/components/null-list/index.vue'

export default {
  name: 'FeedingManagement',
  components: {
    nullList
  },
  props: {
    filterParams: {
      type: Object,
      default: () => ({})
    },
    resetSearch: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      refresherState: false,
      noMore: false,
      isEmpty: false,
      list: [],
      pageNum: 1,
      pageSize: 10,
      // 字典数据
      feedFoodDict: {
        1: '玉米饲料',
        2: '混合饲料',
        3: '青贮饲料'
      },
      feedFrequencyDict: {
        1: '每日一次',
        2: '每日两次',
        3: '每两天一次'
      }
    }
  },
  mounted() {
    this.loadFeedFoodDict()
    this.getList()
  },
  watch: {
    filterParams: {
      handler() {
        this.pageNum = 1;
        this.noMore = false;
        this.getList();
      },
      deep: true
    },
    resetSearch: {
      handler() {
        this.pageNum = 1;
        this.noMore = false;
        this.getList();
      }
    }
  },
  methods: {
    async loadFeedFoodDict() {
      try {
        const res = await getDicts('pasture_feed_food');
        if (res && res.data) {
          const newDict = {};
          res.data.forEach(item => {
            newDict[item.dictValue] = item.dictLabel;
          });
          this.feedFoodDict = { ...this.feedFoodDict, ...newDict };
        }
      } catch (error) {
        console.error('加载饲料种类字典失败:', error);
      }
    },

    getList() {
      uni.showLoading({ title: '加载中', icon: 'none' })

      const params = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        startTime: this.filterParams.startTime || '',
        endTime: this.filterParams.endTime || '',
        pastureName: this.filterParams.pastureName || '',
        feedFood: this.filterParams.feedFood || ''
      }

      feedPage(params).then(response => {
        const isSuccess = response.code === 200
        const newList = isSuccess ? (response.result?.list || []) : []
        const total = isSuccess ? (response.result?.total || 0) : 0

        this.updateList(newList, total)
      }).catch(error => {
        console.error('获取饲养管理列表失败:', error)
        this.$toast('获取数据失败')
        this.updateList([], 0)
      }).finally(() => {
        uni.hideLoading()
      })
    },

    updateList(newList, total) {
      if (this.pageNum >= 2) {
        this.list = this.list.concat(newList)
        this.noMore = this.list.length >= total
      } else {
        this.isEmpty = total < 1
        this.list = total >= 1 ? newList : []
        this.noMore = this.list.length >= total
      }
    },

    scrollToLower() {
      if (this.noMore) return
      this.pageNum++
      this.getList()
    },

    bindrefresherrefresh() {
      this.refresherState = true
      this.pageNum = 1
      this.noMore = false
      this.getList()
      setTimeout(() => {
        this.refresherState = false
        this.$toast('刷新成功')
      }, 1000)
    },

    getFeedFoodText(value) {
      return this.feedFoodDict[value] || '未知'
    },

    getFeedFrequencyText(value) {
      return this.feedFrequencyDict[value] || '未知'
    },

    addRecord() {
      this.$toast('新增饲养记录')
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/common/css/underCareList.scss';
</style>
