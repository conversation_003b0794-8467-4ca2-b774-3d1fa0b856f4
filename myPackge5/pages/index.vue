<template>
    <view class="container">
        <CustomNavbar title="活畜管理" :titleColor="'#333333'" />
        <view class="content" :style="{ paddingTop: navbarTotalHeight + 'px' }">
            <view
                v-for="(module, index) in moduleList"
                :key="index"
                class="module-item"
                @click="goURL(module.url)"
            >
                <image
                    class="module-image"
                    :src="module.image"
                    mode="widthFix"
                />
            </view>
        </view>
    </view>
</template>

<script>

import CustomNavbar from './components/CustomNavbar.vue'

export default {
    components: {
        CustomNavbar,
    },
    data() {
        const baseImageUrl = 'https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/cow/';
        const basePath = '/myPackge5/pages/';

        return {
            systemInfo: uni.getSystemInfoSync(),
            moduleList: [
                { image: `${baseImageUrl}yanghzichang.png`, url: `${basePath}farm/index` },
                { image: `${baseImageUrl}kuru.png`, url: `${basePath}ruku/index` },
                { image: `${baseImageUrl}zaiyang.png`, url: `${basePath}underCare/index` },
                { image: `${baseImageUrl}chuku.png`, url: `${basePath}chuku/index` },
                { image: `${baseImageUrl}fanzhi.png`, url: `${basePath}fanzhi/index` }
            ]
        }
    },
    onLoad() {},
    onUnload() {},
    onShow() {},
    computed: {
        navbarTotalHeight() {
            return (this.systemInfo.statusBarHeight || 0) + 44;
        }
    },
    methods: {
        goURL(url) {
            uni.navigateTo({ url });
        }
    }
}
</script>

<style lang="scss" scoped>
.container {
    min-height: 100vh;
}

.content {
    padding: 30rpx;
    background-color: #F7F8F7;
}

.module-item {
    margin-bottom: 24rpx;

    &:first-child {
        margin-top: 30rpx;
    }

    &:last-child {
        margin-bottom: 0;
    }
}

.module-image {
    width: 100%;
    display: block;
    border-radius: 16rpx;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}
</style>
