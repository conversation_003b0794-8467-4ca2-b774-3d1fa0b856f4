import request from '@/common/utils/ajax'
import {
	admin,
	ffsapp,
	nmbService,
	xmbtest
} from './base'
var checkLogin = false //checkLogin是否开启检查是否登录(默认true开启检查)
// 账号密码登录
export function loginByPsd(param) {
	return request.ajax(nmbService + 'nmb/user/login', param, 'POST', false).then((res) => res.data)
}

//  获取当前登录用户信息
export function getUserInfo(param) {
	return request.ajax(admin + 'getInfo', param, 'GET', true).then((res) => res.data)
}

// 查询userRole  xmbapp-service/ncs/user/ncsUserRole
export function getUserRole(param) {
	return request.ajax(xmbtest + 'ncs/user/ncsUserRole', param, 'POST', true).then((res) => res.data)
}


//模块权限接口
export function getPermissions(param) {
	return request.ajax(ffsapp + 'user/getPermissions', param, 'POST', true).then((res) => res.data)
}

export function sourcePwdToRetrievePwd(param) {
	return request.ajax(nmbService + 'user/sourcePwdToRetrievePwd', param, 'POST', true).then((res) => res.data)
}

export function saveAvatar(param) {
	return request.ajax(nmbService + 'nmb/user/saveAvatar', param, 'POST', true).then((res) => res.data)
}
export function info(param) {
	return request.ajax(nmbService + 'nmb/user/info', param, 'POST', true).then((res) => res.data)
}
export function myTradingList(param) {
	return request.ajax(nmbService + 'nmb/company/myTradingList', param, 'POST', true).then((res) => res.data)
}
