import request from '@/common/utils/ajax'
import {
	ffsapp,
	test,
	rabtest,
	admintest,
	xmbtest
} from './base'
import ajaxOther from '@/common/utils/ajaxOther'
// 根据字典类型查询字典数据信息
export function getDicts(dictType) {
	return request.ajax(admintest + 'system/dict/data/type/' + dictType, dictType, 'get', true).then((res) => res.data)
}
//活畜类别
export function livestockList(param) {
	return request.ajax(
		`${xmbtest}livestock/livestock/list`, param, 'POST', true
	).then((res) => res.data)
}
//活畜品种
export function livestockCategory(data) {
	return request.ajax(
		`${xmbtest}livestock/livestockVarieties/list`, data, 'POST', true
	).then(res => res.data)
}
//活畜类型
export function animalTypeList(data) {
	return request.ajax(
		`${xmbtest}livestock/livestockCategory/list`, data, 'POST', true
	).then(res => res.data)
}