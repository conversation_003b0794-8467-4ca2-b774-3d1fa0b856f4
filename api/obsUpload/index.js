import { unploadFile } from "@/api/common"
// 引入policy编码计算方法
const getPolicyEncode = require('./GetPolicy');
// 引入签名计算方法
const getSignature = require('./GetSignature');

function formatTime () {
    //value要转化的标准时间
    const dateTme = new Date()
    const Y = dateTme.getFullYear()
    const M = dateTme.getMonth() + 1 < 10 ? "0" + (dateTme.getMonth() + 1) : dateTme.getMonth() + 1
    const D = dateTme.getDate() < 10 ? "0" + dateTme.getDate() : dateTme.getDate()
    const h = dateTme.getHours()< 10 ? "0" + dateTme.getHours() : dateTme.getHours()
    const m = dateTme.getMinutes() < 10 ? "0" + dateTme.getMinutes() : dateTme.getMinutes()
    const s = dateTme.getSeconds() < 10 ? "0" + dateTme.getSeconds() : dateTme.getSeconds()
    return  Y + "-" + M + "-" + D + "T" + h +":"+ m +":" + s + "Z"
  }

  export const uploadFiles = function ({ filePath , name=''}) {
    return new Promise(async (resolve, reject) => {
        const  { code , result , message }=await unploadFile({});
        if (code != 200) {
            reject(message);
        }
        let time = formatTime ();
        let fileName =''
        name?  fileName = name : fileName=filePath.substring(filePath.lastIndexOf('/') + 1)
        const OBSPolicy = { // 设定policy内容，policy规则定义可参考步骤3中的超链接签名计算规则文档
            "expiration": time,
            "conditions": [
                { "bucket": result.bucketName},  // 桶名要和配置文件中endpoint中的桶名保持一致
                    // { "x-obs-security-token": config.SecurityToken } // 如果是临时访问秘钥鉴权，必须设置该值
                { 'key': fileName }
            ]
        }
        const policyEncoded = getPolicyEncode(OBSPolicy);    // 计算base64编码后的policy
        const signature = getSignature(policyEncoded,result.sk);     // 计算signature
        uni.uploadFile({
            url: `https://${result.bucketName}.${result.endpoint}`, // 开发者服务器的URL。
            filePath,
            name: 'file', // 必须填file。
            header: {
                'content-type': 'multipart/form-data; boundary=-9431149156168',
            },
            formData: {
                //从配置文件中获取到的AK信息、计算得到的编码后policy及signature信息
                'AccessKeyID': result.ak,
                'policy': policyEncoded,
                'signature': signature,
                'key': fileName,
                    // "x-obs-security-token": config.SecurityToken, // 如果是临时访问秘钥鉴权，必须设置该值
            },
            success: (res) => {
                if (res.statusCode === 204) {
                    let url = result.sslMode  + "://"+ result.bucketName + '.obs.cn-north-4.myhuaweicloud.com' + "/" + fileName
                    resolve(url);
                } else {
                    reject(res);
                }
            },
            fail: err => {
                reject(err);
            }
        });
    });
  };