import request from '@/common/utils/ajax'
import { nmbService } from '../base'
var checkLogin = false //checkLogin是否开启检查是否登录(默认true开启检查)

const API_PATHS = {
  CHOOSE: 'purchasePrepare/choose',// 选牛
  KONGCAO: 'purchasePrepare/kongcao',// 控槽
  QUARANTINE: 'purchaseGo/quarantine',// 检疫 
  CATCHOW: 'purchasePrepare/catchCow',// 抓牛
  INFO: 'purchasePrepare/prepareInfo' // 选牛装车详情
}


export function farmersListByPurchaseOrderId(param) {
	return request.ajax(nmbService + 'purchasePrepare/farmersListByPurchaseOrderId', param, 'POST').then((res) => res.data)
}
// 选牛
export function choosetCows(param) {
  return request.ajax(nmbService + API_PATHS.CHOOSE, param, 'POST').then((res) => res.data)
}
// 控槽
export function kongcao(param) {
  return request.ajax(nmbService + API_PATHS.KONGCAO, param, 'POST').then((res) => res.data)
}
// 检疫 
export function quarantine(param) {
  return request.ajax(nmbService + API_PATHS.QUARANTINE, param, 'POST').then((res) => res.data)
}
// 抓牛
export function catchCow(param) {
  return request.ajax(nmbService + API_PATHS.CATCHOW, param, 'POST').then((res) => res.data)
}
// 检查
export function check(param) {
  return request.ajax(nmbService + 'purchaseGo/check', param, 'POST').then((res) => res.data)
}
// 整车
export function wholeCar(param) {
  return request.ajax(nmbService + 'purchaseGo/weighting', param, 'POST').then((res) => res.data)
}
// 选牛装车详情
export function prepareInfo(param) {
  return request.ajax(nmbService + API_PATHS.INFO, param, 'POST').then((res) => res.data)
}
// 检疫、检查、整车详情
export function purchaseGoInfo(param) {
  return request.ajax(nmbService + 'purchaseGo/purchaseGoInfo', param, 'POST').then((res) => res.data)
}

