import request from '@/common/utils/ajax'
import { nmbService } from '../base'
var checkLogin = false //checkLogin是否开启检查是否登录(默认true开启检查)

const API_PATHS = {
  PAGE: '/purchaseMaintenance/page',
  INFO: '/purchaseMaintenance/info',
  ADD: '/purchaseMaintenance/add',
}
export function page(param) {
	return request.ajax(nmbService + API_PATHS.PAGE, param, 'POST').then((res) => res.data)
}
export function info(param) {
	return request.ajax(nmbService + API_PATHS.INFO, param, 'POST').then((res) => res.data)
}
export function add(param) {
	return request.ajax(nmbService + API_PATHS.ADD, param, 'POST').then((res) => res.data)
}

