import request from '@/common/utils/ajax'
import { nmbService } from '../base'

const API_PATHS = {
  PAGE: 'settlement/detailList',
  INFO: 'settlement/info',
  PURCHASEORDER: 'settlement/addPurchaseSettlement',
  SAVESETTLEMENT: 'settlement/addSettlement',
  SAVEOTHERSETTLEMENT: 'settlement/addOtherSettlement',
  PAY: 'settlement/saveSettlement',
}
export function page(param) {
	return request.ajax(nmbService + API_PATHS.PAGE, param, 'POST').then((res) => res.data)
}
export function info(param) {
	return request.ajax(nmbService + API_PATHS.INFO, param, 'POST').then((res) => res.data)
}
export function purchaseOrder(param) {
	return request.ajax(nmbService + API_PATHS.PURCHASEORDER, param, 'POST').then((res) => res.data)
}
export function saveSettlement(param) {
	return request.ajax(nmbService + API_PATHS.SAVESETTLEMENT, param, 'POST').then((res) => res.data)
}
export function saveOtherSettlement(param) {
	return request.ajax(nmbService + API_PATHS.SAVEOTHERSETTLEMENT, param, 'POST').then((res) => res.data)
}
export function pay(param) {
	return request.ajax(nmbService + API_PATHS.PAY, param, 'POST').then((res) => res.data)
}

