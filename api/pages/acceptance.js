import request from '@/common/utils/ajax'
import { nmbService } from '../base'

const API_PATHS = {
  ACCEPTANCEAdd: 'purchaseAcceptance/add',
  ACCEPTANCEINFO: 'purchaseAcceptance/info',
  ACCEPTANCESAVESIGN: 'purchaseAcceptance/saveSign',
  SAVEACCEPTANCE: 'purchaseAcceptance/saveAcceptance',
}
export function add(param) {
	return request.ajax(nmbService + API_PATHS.ACCEPTANCEAdd, param, 'POST').then((res) => res.data)
}
export function info(param) {
	return request.ajax(nmbService + API_PATHS.ACCEPTANCEINFO, param, 'POST').then((res) => res.data)
}
export function saveSign(param) {
	return request.ajax(nmbService + API_PATHS.ACCEPTANCESAVESIGN, param, 'POST').then((res) => res.data)
}
export function saveAcceptance(param) {
	return request.ajax(nmbService + API_PATHS.SAVEACCEPTANCE, param, 'POST').then((res) => res.data)
}


