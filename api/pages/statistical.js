import request from '@/common/utils/ajax'
import { nmbService } from '../base'

const API_PATHS = {
    COUNT: 'statsData/home/<USER>', // 首页单项统计
    PRICEANALYSIS: 'statsData/home/<USER>/priceAnalysis', // 价格分析双Y轴折线图
    SALES: 'statsData/salesAnalysis', // 销售分析 - 单项统计 && 客户销售列表
    PROFIT: 'statsData/profitAnalysis', // 利润分析 - 单项统计 && 客户成本核算
    PRICE: 'statsData/priceAnalysis', // 价格分析
}
export function singleCount(param) {
	return request.ajax(nmbService + API_PATHS.COUNT, param, 'POST').then((res) => res.data)
}
export function priceAnalysis(param) {
    return request.ajax(nmbService + API_PATHS.PRICEANALYSIS, param, 'POST').then((res) => res.data)
}
export function salesAnalysis(param) {
    return request.ajax(nmbService + API_PATHS.SALES, param, 'POST').then((res) => res.data)
}
// 利润分析
export function profitAnalysis(param) {
    return request.ajax(nmbService + API_PATHS.PROFIT, param, 'POST').then((res) => res.data)
}
export function statsDataPriceAnalysis(param) {
    return request.ajax(nmbService + API_PATHS.PRICE, param, 'POST').then((res) => res.data)
}
// 价格详情 
export function priceAnalysisDetail(param) {
    return request.ajax(nmbService + 'statsData/priceAnalysisDetail', param, 'POST').then((res) => res.data)
}

export function customerSalesDetailList(param) {
    return request.ajax(nmbService + 'statsData/customerSalesDetailList', param, 'POST').then((res) => res.data)
}
// 客户单项统计
export function customerSingleCount(param) {
    return request.ajax(nmbService + 'statsData/customerSingleCount', param, 'POST').then((res) => res.data)
}
// 利润分析明细
export function profitAnalysisDetailList(param) {
    return request.ajax(nmbService + 'statsData/profitAnalysisDetailList', param, 'POST').then((res) => res.data)
}
// 成本分析 
export function costAnalysis(param) {
    return request.ajax(nmbService + 'statsData/costAnalysis', param, 'POST').then((res) => res.data)
}
// 成本分析明细
export function costAnalysisDetail(param) {
    return request.ajax(nmbService + 'statsData/costAnalysisDetail', param, 'POST').then((res) => res.data)
}

// 运输分析
export function transportAnalysis(param) {
    return request.ajax(nmbService + 'statsData/transportAnalysis', param, 'POST').then((res) => res.data)
}
// 运输详情
export function transportAnalysisDetail(param) {
    return request.ajax(nmbService + 'statsData/transportAnalysisDetail', param, 'POST').then((res) => res.data)
}

// 运输详情列表
export function transportAnalysisDetailList(param) {
    return request.ajax(nmbService + 'statsData/transportAnalysisDetailList', param, 'POST').then((res) => res.data)
}

// 牛源分析首页
export function cowSourceAnalysis(param) {
    return request.ajax(nmbService +'statsData/cowSourceAnalysis', param, 'POST').then((res) => res.data)
}
// 牛源分析-客户列表
export function cowSourceCustomerAnalysisList(param) {
    return request.ajax(nmbService + 'statsData/cowSourceCustomerAnalysisList', param, 'POST').then((res) => res.data)
}
// 牛源分析 - 牛经纪列表
export function cowSourceBrokerAnalysisList(param) {
    return request.ajax(nmbService +'statsData/cowSourceBrokerAnalysisList', param, 'POST').then((res) => res.data)
}
// 牛源分析 - 养殖户列表 
export function cowSourceFarmersAnalysisList(param) {
    return request.ajax(nmbService +'statsData/cowSourceFarmersAnalysisList', param, 'POST').then((res) => res.data)
}
// 牛源分析 - 司机列表 
export function cowSourceDriversAnalysisList(param) {
    return request.ajax(nmbService +'statsData/cowSourceDriversAnalysisList', param, 'POST').then((res) => res.data)
}
