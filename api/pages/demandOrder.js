import request from '@/common/utils/ajax'
import { nmbService } from '../base'
var checkLogin = false //checkLogin是否开启检查是否登录(默认true开启检查)

const API_PATHS = {
  ADD: 'demandOrder/add',
  PAGE: 'demandOrder/page',
  INFO: 'demandOrder/info',
  USERLIST: 'nmb/user/list',
}
export function demandOrderAdd(param) {
	return request.ajax(nmbService + API_PATHS.ADD, param, 'POST').then((res) => res.data)
}
export function demandOrderPage(param) {
	return request.ajax(nmbService + API_PATHS.PAGE, param, 'POST').then((res) => res.data)
}
export function demandOrderInfo(param) {
	return request.ajax(nmbService + API_PATHS.INFO, param, 'POST').then((res) => res.data)
}
export function nmbUserList(param) {
	return request.ajax(nmbService + API_PATHS.USERLIST, param, 'POST').then((res) => res.data)
}

