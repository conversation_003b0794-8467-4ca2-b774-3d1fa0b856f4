import request from '@/common/utils/ajax'
import { nmbService } from '../base'
var checkLogin = false //checkLogin是否开启检查是否登录(默认true开启检查)

const API_PATHS = {
  PAGE: 'saleOrder/page',
  INFO: 'saleOrder/info',
  COMFIRM: 'saleOrder/confirm',
  RECEIPT: 'saleOrder/receipt',
}
export function saleOrderPage(param) {
	return request.ajax(nmbService + API_PATHS.PAGE, param, 'POST').then((res) => res.data)
}
export function saleOrderInfo(param) {
	return request.ajax(nmbService + API_PATHS.INFO, param, 'POST').then((res) => res.data)
}
export function confirm(param) {
	return request.ajax(nmbService + API_PATHS.COMFIRM, param, 'POST').then((res) => res.data)
}
export function receipt(param) {
	return request.ajax(nmbService + API_PATHS.RECEIPT, param, 'POST').then((res) => res.data)
}

