import request from '@/common/utils/ajax'
import { nmbService } from '../base'
var checkLogin = false //checkLogin是否开启检查是否登录(默认true开启检查)

const API_PATHS = {
	ADD: 'saleContract/add',
	PAGE: 'saleContract/page',
	INFO: 'saleContract/info',
	LIST: 'saleContract/list',
}
export function saleContractAdd(param) {
	return request.ajax(nmbService + API_PATHS.ADD, param, 'POST').then((res) => res.data)
}
export function saleContractPage(param) {
	return request.ajax(nmbService + API_PATHS.PAGE, param, 'POST').then((res) => res.data)
}
export function saleContractInfo(param) {
	return request.ajax(nmbService + API_PATHS.INFO, param, 'POST').then((res) => res.data)
}

export function saleContractList(param) {
	return request.ajax(nmbService + API_PATHS.LIST, param, 'POST').then((res) => res.data)
}

// 查询需求方公司
export function companyCustomer(param) {
	return request.ajax(nmbService + 'nmb/company/customer/page', param, 'POST').then((res) => res.data)
}

// 员工列表
export function tradingPage(param) {
	return request.ajax(nmbService + 'nmb/user/list', param, 'POST').then((res) => res.data)
}