import request from '@/common/utils/ajax'
import { nmbService } from '../base'
var checkLogin = false //checkLogin是否开启检查是否登录(默认true开启检查)

const API_PATHS = {
  PAGE: 'settlementOrder/page',
  INFO: 'settlementOrder/info',
  SETTLEMENT: 'settlementOrder/settlement',
  ISSETTLEMENT: 'settlementOrder/settlementStatus',
}
export function settlementOrderPage(param) {
	return request.ajax(nmbService + API_PATHS.PAGE, param, 'POST').then((res) => res.data)
}
export function settlementOrderInfo(param) {
	return request.ajax(nmbService + API_PATHS.INFO, param, 'POST').then((res) => res.data)
}
export function settlement(param) {
	return request.ajax(nmbService + API_PATHS.SETTLEMENT, param, 'POST').then((res) => res.data)
}
export function isSettlement(param) {
	return request.ajax(nmbService + API_PATHS.ISSETTLEMENT, param, 'POST').then((res) => res.data)
}

