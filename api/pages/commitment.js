import request from '@/common/utils/ajax'
import { nmbService, xmbtest } from '../base'
var checkLogin = false //checkLogin是否开启检查是否登录(默认true开启检查)

// 承诺书内容
export function seleteProtocol(param) {
    return request.ajax(xmbtest + 'protocol/seleteProtocol', param, 'POST', false).then((res) => res.data)
}
// 承诺书详情
export function selectCommitmentInfo(param) {
    return request.ajax(nmbService + 'purchasePrepare/selectCommitmentInfo', param, 'POST', false).then((res) => res.data)
}
// 确认签字
export function commitmentSign(param) {
    return request.ajax(nmbService + 'purchasePrepare/commitmentSign', param, 'POST', false).then((res) => res.data)
}