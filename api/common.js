import request from '@/common/utils/ajax'
import {
	ffsapp,
	nmbService
} from './base'
export function unploadFile(param){
    return request.ajax(nmbService + 'files/obs/fileToken', param, 'POST', true).then((res) => res.data)
}
export function provinceList(param){
    return request.ajax(nmbService + 'baseArea/provinceList', param, 'POST', true).then((res) => res.data)
}

export function getAddress(params){
	return request.ajax('/traceapp-service/baseArea/list',params,'POST',true).then((res)=>res.data)
}

export function seleteProtocol(params){
	return request.ajax('/nmb-service/protocol/selectProtocol',params,'POST',false).then((res)=>res.data)
}


