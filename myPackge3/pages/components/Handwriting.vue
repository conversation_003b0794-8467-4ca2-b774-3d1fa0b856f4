<template>
  <view>
    <view class="main-content" @touchmove.stop.prevent="">
      <canvas class="mycanvas" id="mycanvas" canvas-id="mycanvas" @touchstart="touchstart" @touchmove="touchmove"
        @touchend="touchend"></canvas>
      <canvas class="mycanvas"
        :style="{ 'z-index': -1, width: `${screenWidth}px`, height: `${(screenWidth * screenWidth) / screenHeight}px` }"
        id="rotatCanvas" canvas-id="rotatCanvas"></canvas>
      <cover-view class="button-line">
        <u-button hover-class='none' type="default" :plain="true" text="取消" @click="cancle">取消</u-button>
        <u-button hover-class='none' type="primary" :plain="true" text="完成签字" @click="finish">完成签字</u-button>
        <u-button hover-class='none' type="error" :plain="true" text="清空" @click="clear">清空</u-button>
      </cover-view>
    </view>
  </view>
</template>
 
<script>
import { uploadFiles } from '@/api/obsUpload/index'
export default {
  data() {
    return {
      ctx: '', //绘图图像
      points: [], //路径点集合
      screenWidth: '',
      screenHeight: '',
      img: '',
      isDraw: false,
    };
  },
  mounted() {
    this.createCanvas();
    uni.getSystemInfo({
      success: e => {
        this.screenWidth = e.screenWidth;
        this.screenHeight = e.screenHeight;
      }
    });
  },
  methods: {
    //创建并显示画布
    createCanvas() {
      this.showCanvas = true;
      this.ctx = uni.createCanvasContext('mycanvas', this);
      //设置画笔样式
      this.ctx.lineWidth = 3;
      this.ctx.lineCap = 'round';
      this.ctx.lineJoin = 'round';
    },
    //触摸开始，获取到起点
    touchstart(e) {
      this.isDraw = true
      let startX = e.changedTouches[0].x;
      let startY = e.changedTouches[0].y;
      let startPoint = {
        X: startX,
        Y: startY
      };
      this.points.push(startPoint);
      //每次触摸开始，开启新的路径
      this.ctx.beginPath();
    },
    //触摸移动，获取到路径点
    touchmove(e) {
      let moveX = e.changedTouches[0].x;
      let moveY = e.changedTouches[0].y;
      let movePoint = {
        X: moveX,
        Y: moveY
      };
      this.points.push(movePoint); //存点
      let len = this.points.length;
      if (len >= 2) {
        this.draw(); //绘制路径
      }
    },
    // 触摸结束，将未绘制的点清空防止对后续路径产生干扰
    touchend() {
      this.points = [];
    },
    draw() {
      let point1 = this.points[0];
      let point2 = this.points[1];
      this.points.shift();
      this.ctx.moveTo(point1.X, point1.Y);
      this.ctx.lineTo(point2.X, point2.Y);
      this.ctx.stroke();
      this.ctx.draw(true);
    },
    //清空画布
    clear() {
      this.isDraw = false
      this.ctx.clearRect(0, 0, this.screenWidth, this.screenHeight);
      this.ctx.draw(true);
    },
    //完成绘画并保存到本地
    finish() {
      if (this.isDraw) {
        uni.canvasToTempFilePath({
          canvasId: 'mycanvas',
          success: res => {
            this.$emit('submit', { url: res.tempFilePath })
            uni.navigateBack({
              delta: 1
            });
          },
          complete: com => { }
        });
      } else {
        uni.showToast({
          title: '请完成签名',
          duration: 2000,
          icon: 'none'
        });
      }
    },
    rotate(tempFilePath) {
      let that = this
      wx.getImageInfo({
        src: tempFilePath,
        success: (res1) => {
          console.log('==== rotate :', res1)
          let canvasContext = wx.createCanvasContext('rotatCanvas')
          let rate = res1.height / res1.width
          let width = 300 / rate
          let height = 300
          canvasContext.translate(height / 2, width / 2)
          canvasContext.rotate((270 * Math.PI) / 180)
          canvasContext.drawImage(tempFilePath, -width / 2, -height / 2, width, height)
          canvasContext.draw(false, () => {
            wx.canvasToTempFilePath({
              canvasId: 'rotatCanvas',
              fileType: 'png',
              quality: 1, //图片质量
              success(res) {
                uni.$emit("imageUrl", {
                  imageUrl: res.tempFilePath,
                });
              }
            })
          })
        }
      })
    },
    cancle() {
      uni.navigateBack({
        delta: 1,
      })
    }
  }
};
</script>
<style lang="scss" scoped>
.main-content {
  width: 100vw;
  height: 100vh;
  position: fixed;
  top: 0rpx;
  left: 0rpx;
  // z-index: 9999;
  background-color: #ffffff;
}

.mycanvas {
  width: 100%;
  height: calc(100vh - 200rpx);
  background-color: #fafafa;
  position: fixed;
  left: 0rpx;
  top: 0rpx;
  z-index: 2;
}

.button-line {
  // transform: rotate(90deg);
  position: fixed;
  bottom: 100rpx;
  left: 0;
  height: 80rpx;
  z-index: 999;
  font-size: 34rpx;
  font-weight: 600;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-around;
}

.save-button {
  color: #ffffff;
  width: 150rpx;
  height: 80rpx;
  text-align: center;
  line-height: 80rpx;
  border-radius: 10rpx;
  background-color: #007aff;
}

.clear-button {
  color: #ffffff;
  width: 150rpx;
  height: 80rpx;
  text-align: center;
  line-height: 80rpx;
  border-radius: 10rpx;
  background-color: #F56C6C;
}

.cancle-button {
  background-color: #ccc;
}
</style>
