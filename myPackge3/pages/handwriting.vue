<template>
    <view>
        <Handwriting @submit="submit"></Handwriting>
    </view>
</template>

<script>
import Handwriting from './components/Handwriting'
import { commitmentSign } from '@/api/pages/commitment'
export default {

    data() {
        return {
            shortId: ''
        }
    },
    components: {
        Handwriting
    },
    onLoad(opation) {
        console.log(opation)
        this.shortId = opation.shortId
    },
    methods: {
        submit(value) {
            console.log(value.url)
            const params = {
                shortId: this.shortId,
                farmerSignUrl: value.url
            }
            console.log(params)
            commitmentSign(params).then(res => {
                if (res.code == 200) {
                    uni.showToast({
                        title: '签名成功',
                        icon: 'none'
                    })
                    setTimeout(() => {
                        uni.$emit('updateCommitmentInfo')
                        uni.navigateBack({
                            delta: 1
                        })
                    }, 500)

                }
            })
        }
    }
}
</script>

<style></style>