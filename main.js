import Vue from 'vue'
import App from './App'

import store from './store'
import config from './config/index'
import * as filters from './common/utils/filters.js'
import initModal from '@/components/g-show-modal/initModal.js'
import gShowModal from '@/components/g-show-modal/g-show-modal'
import {
	hasPermi
} from './utils/permissions'
import {
	picPath
} from "@/utils/common.js"
import 'uview-ui/index.scss';

Vue.component('g-show-modal', gShowModal)
// 引入全局uView
import uView from 'uview-ui'
Vue.use(uView)

Vue.prototype.$store = store
Vue.prototype.$config = config.initEnv()
App.mpType = 'app'

initModal(Vue)

// #ifdef H5
//引入调试器
import VConsole from 'vconsole'
/* if (process.env.NODE_ENV === 'development') {
	const vConsole = new VConsole()
} */
// new VConsole()
// #endif

//配置全局过滤器
Object.keys(filters).forEach((key) => {
	Vue.filter(key, filters[key])
})

//统一提示方便全局修改
const toast = (title, duration = 1500, mask = false, icon = 'none') => {
	if (Boolean(title) === false) {
		return
	}
	uni.showToast({
		title,
		duration,
		mask,
		icon,
	})
}

//统一处理路由navigateTo和switchTab跳转
const gRouter = (url, data = '') => {
	const tabRouter = ['pages/index/index', 'account/user/user'] //配置switchTab菜单
	tabRouter.map((ietm) => {
		if (url.includes(ietm)) {
			uni.switchTab({
				url: url,
			})
			return
		}
	})
	uni.navigateTo({
		url: url + data,
	})
}
const showModal = (content, confirmText = '确定', title) => {
	return new Promise((reslove, reject) => {
		uni.showModal({
			title,
			content,
			confirmText,
			confirmColor: '#0CAD83',

			success: (res) => {
				if (res.confirm) {
					reslove(true)
				} else if (res.cancel) {
					reslove(false)
				}
			},
		})
	})
}
Vue.mixin({
	methods: {
		// isJump未登录时是否自动跳转登录页面
		cklogin(isJump = true, url = '/pages/basics/account/login/login', showCancel = false, content =
			'检测到您还没有登录，请先去登录哦~') {
			console.log('-------------')
			const token = getToken() || ''
			return new Promise((resolve, reject) => {
				if (!token) {
					if (!isJump) {
						resolve(false)
						return
					}
					this.$showModal({
						content: content,
						modalIcon: 'worn',
						showCancel: false,
						confirmText: '去登录',
						success: (res) => {
							uni.navigateTo({
								url,
							})
						},
					})
					resolve(false)
				}
				resolve(true)
			})
		},
	},
})

Vue.prototype.$gRouter = gRouter
Vue.prototype.$toast = toast
Vue.prototype.$showModal = showModal
Vue.prototype.$hasPermi = hasPermi;
Vue.prototype.$picPath = picPath;
Vue.prototype.$bus = new Vue();
Vue.config.productionTip = false
const app = new Vue({
	store,
	...App,
})
app.$mount()