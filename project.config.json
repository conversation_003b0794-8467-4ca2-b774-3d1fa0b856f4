{"appid": "wxce54e1f3fa85a840", "compileType": "miniprogram", "libVersion": "2.29.2", "packOptions": {"ignore": [], "include": []}, "setting": {"coverView": true, "es6": true, "postcss": true, "minified": true, "enhance": true, "showShadowRootInWxmlPanel": true, "packNpmRelationList": [], "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "condition": false}, "condition": {}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}, "miniprogramRoot": "unpackage/dist/dev/mp-weixin/", "srcMiniprogramRoot": "unpackage/dist/dev/mp-weixin/"}