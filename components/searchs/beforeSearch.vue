<template>
	<view>
		<u-popup v-model="showPicker" mode="bottom" border-radius="14" :closeable="true" @close="canel">
			<scroll-view scroll-y="true" class="main">
				<view class="job-types">
					<h3 class="job-type-title">工作类型</h3>
					<view class="job-type-content">
						<p v-for="(item, index) in workType" :key="index" :class="item.active ? 'job-type-active' : ''"
							@click="handleActive(item)">
							{{ item.label }}
						</p>
					</view>
				</view>
				<view class="job-types">
					<u-form-item label="监管区域" label-position="top">
						<p class="pleaseCheck" @click="handleSelectArea">{{form.area?form.area:'请选择'}}</p>
					</u-form-item>
				</view>
				<div style="height: 150rpx;"></div>
			</scroll-view>
			<view class="button-group">
				<view class="button-group-view">
					<view class="button-group-reset" @click="resetForm">
						重置
					</view>
					<view class="button-group-submit" @click="submitForm">
						确认
					</view>
				</view>
			</view>
		</u-popup>
		<addressPicker @submitAddress="submitAddress" :pickerAreaShow="pickerAreaShow" @addressCanel="addressCanel" />
	</view>

</template>

<script>
	import addressPicker from "@/components/address-picker/index.vue"
	import {
		getStorage
	} from '@/common/utils/storage.js'
	export default {
		components: {
			addressPicker
		},
		props: [
			'filtrate',
			'superviseType'
		],
		data() {
			return {
				showPicker: false,
				pickerAreaShow: false,
				form: {},
				otherData: [{
					label: '意向单',
					value: '1',
					active: false,
				}, {
					label: '调研表',
					value: '2',
					active: false,
				}, {
					label: '设备安装方案',
					value: '3',
					active: false,
				}, {
					label: '监管单',
					value: '4',
					active: false,
				}, {
					label: '安装设备',
					value: '5',
					active: false,
				}, {
					label: '盘点',
					value: '6',
					active: false
				}, {
					label: '补充相关协议',
					value: '7',
					active: false,
				}, {
					label: '放款',
					value: '8',
					active: false,
				}],
				flowData: [{
						label: '指派',
						value: '10',
						active: false,
					},
					{
						label: '业务员完善',
						value: '11',
						active: false,
					}, {
						label: '省风控完善',
						value: '12',
						active: false,
					}, {
						label: '本行评估',
						value: '13',
						active: false,
					}, {
						label: '支行评估',
						value: '14',
						active: false,
					}
				],
				workType: [ //工作类型
				],
				areaRange: '', //区域权限
			}
		},
		watch: {
			filtrate: {
				handler(newVal) {
					this.showPicker = newVal; // 手动更新子组件的message值
					console.log(this.showPicker);
				},
				deep: true
			}
		},
		methods: {
			addressCanel() {
				this.pickerAreaShow = false
			},
			handleSelectArea() {
				console.log(this.userInfo, );
				// console.log(this.pickerAreaShow)
				if (!this.areaRange) {
					uni.showToast({
						title: '抱歉，暂无数据',
						icon: 'none'
					})
					return
				}
				this.pickerAreaShow = true
			},
			canel() {
				this.$emit('update:filtrate', false)
			},
			submitAddress(val) {
				console.log(val);
				this.form.area = val.area
				let areaId = val.areaDataRange.split(",");
				this.form.fpProvinceId = areaId[1] || "";
				this.form.fpCityId = areaId[2] || "";
				this.form.fpCountyId = areaId[3] || "";
			},
			submitForm() {
				let arr = []
				this.workType.forEach(item => {
					if (item.active) {
						arr.push(item.value)
					}
				})
				let form = {
					...this.form,
					arrays: arr
				}
				console.log(form);
				this.$emit('filtrates', form)
			},
			resetForm() {
				this.workType.forEach(item => {
					item.active = false
				})
				this.form = {
					area: "",
					fpCityId: "",
					fpCountyId: "",
					fpProvinceId: "",
				}
				this.$emit('filtrates', {
					arrays:[],
					fpCityId: "",
					fpCountyId: "",
					fpProvinceId: "",
				})
			},
			handleActive(item) {
				item.active = !item.active
			}
		},
		mounted() {
			if (this.superviseType) {
				this.workType = this.otherData
			} else {
				this.workType = this.flowData
			}
			this.areaRange = getStorage('userInfo').sysUser.areaDataRange
		},
	}
</script>
<style lang="less" scoped>
	.popups {
		background-color: #ffffff;

		.statusList {}

		.tags {}
	}

	.statusList {
		display: flex;

	}

	.job-type {
		padding: 0rpx 29rpx;
		margin-top: 50rpx;

		.job-type-title {
			font-size: 28rpx;
			color: #333;
			font-weight: 400;
			padding-bottom: 24rpx;
			font-family: PingFang SC-Bold, PingFang SC;
		}

		.job-type-content {
			display: flex;
			flex-wrap: wrap;

			p {
				color: #999;
				background-color: #F4F4F4;
				padding: 17rpx 27rpx;
				border-radius: 100rpx 100rpx 100rpx 100rpx;
				margin: 0 30rpx 30rpx 0;
				border: 2rpx solid transparent;
			}

			.pleaseCheck {
				color: #999;
			}

			.job-type-active {
				border: 2rpx solid #40CA8F;
				color: #40CA8F;
				background-color: white;
			}

			.policy-type-active {
				border: 2rpx solid #40CA8F;
				color: #40CA8F;
				background-color: white;
			}

			.ear-type-active {
				border: 2rpx solid #40CA8F;
				color: #40CA8F;
				background-color: white;
			}
		}
	}

	.job-types {
		padding: 0rpx 29rpx;
		margin-top: 20rpx;

		.pleaseCheck {
			color: #999;
		}

		.job-type-title {
			font-size: 28rpx;
			color: #333;
			font-weight: 400;
			padding-bottom: 24rpx;
			font-family: PingFang SC-Bold, PingFang SC;
		}

		.job-type-content {
			display: flex;
			flex-wrap: wrap;

			p {
				color: #999;
				background-color: #F4F4F4;
				padding: 17rpx 27rpx;
				border-radius: 100rpx 100rpx 100rpx 100rpx;
				margin: 0 18rpx 30rpx 0;
				border: 2rpx solid transparent;
			}

			.job-type-active {
				border: 2rpx solid #40CA8F;
				color: #40CA8F;
				background-color: white;
			}

			.policy-type-active {
				border: 2rpx solid #40CA8F;
				color: #40CA8F;
				background-color: white;
			}

			.ear-type-active {
				border: 2rpx solid #40CA8F;
				color: #40CA8F;
				background-color: white;
			}
		}
	}

	.button-group {
		width: 100%;
		padding: 29rpx;
		position: fixed;
		bottom: 0;
		background-color: white;
		z-index: 10;
		// margin-top: 53rpx;

		.button-group-view {
			border-radius: 100rpx 100rpx 100rpx 100rpx;
			display: flex;
			background-color: #40CA8F;

			.button-group-reset {
				padding: 25rpx 200rpx 25rpx 79rpx;
				background: #FFFFFF;
				border-radius: 100rpx 0rpx 120rpx 100rpx;
				border: 2rpx solid #40CA8F;
				font-size: 30rpx;
				color: #40CA8F;
				font-family: PingFang SC-Bold, PingFang SC;
			}

			.button-group-submit {
				padding: 25rpx 142rpx;
				background: #40CA8F;
				font-size: 30rpx;
				border-radius: 0rpx 100rpx 100rpx 0rpx;
				color: #FFFFFF;
			}
		}

	}
</style>