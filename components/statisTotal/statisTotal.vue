<template>
  <view class="statistics-card">
    <view class="total-cost">
      <text class="cost-number">{{ totalInfo.totalCost || 0 }}</text>
      <text class="cost-unit">总费用 (万元)</text>
    </view>
    <view class="cost-details">
      <view class="cost-item" v-for="(item, index) in totalInfo.costItems" :key="index">
        <view class="cost-bar">
          <!-- item.percentage*10+'%' -->
          <view class="bar-fill" :style="{ width: '100%', backgroundColor: item.color }"></view>
        </view>
        <view class="cost-info">
          <text class="cost-name">{{ item.name }}:</text>
          <text class="cost-value">{{ item.value }}，占{{ item.percentage }}%</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>

export default {
  props: {
    totalInfo: {
      type: Object,
      default: () => {
        return {
          totalCost: 0,
          costItems: [
            {
              name: '销售费用',
              value: 0,
              percentage: '0%',
              color: '#08BA7E'
            },
            {
              name: '采购成本',
              value: 0,
              percentage: '0%',
              color: '#FFA500'
            },
            {
              name: '运输费用',
              value: 0,
              percentage: '0%',
              color: '#FF4500'
            },
            {
              name: '其他费用',
              value: 0,
              percentage: '0%',
              color: '#FF6347'
            }
          ]
        }
      }
    }
  },
  data() {
    return {
      
    }
  },
  computed: {
  },
  methods: {}
};
</script>

<style lang="scss" scoped>
.statistics-card {
  height: auto;
  background-color: #fff;
  border-radius: 20rpx;
  margin: 20rpx 20rpx 0 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
}

.total-cost {
  display: flex;
  flex-direction: column;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 20rpx;
  margin-bottom: 20rpx;
  
  .cost-number {
    font-size: 80rpx;
    font-weight: bold;
    color: #08BA7E;
  }
  
  .cost-unit {
    font-size: 28rpx;
    color: #666;
    margin-top: -10rpx;
  }
}

.cost-details {
  .cost-item {
    margin-bottom: 20rpx;
    
    .cost-bar {
      height: 10rpx;
      background-color: #f0f0f0;
      border-radius: 10rpx;
      margin-bottom: 10rpx;
      
      .bar-fill {
        height: 100%;
        border-radius: 10rpx;
      }
    }
    
    .cost-info {
      display: flex;
      justify-content: space-between;
      font-size: 28rpx;
      
      .cost-name {
        color: #333;
      }
      
      .cost-value {
        color: #666;
      }
    }
  }
}
</style> 