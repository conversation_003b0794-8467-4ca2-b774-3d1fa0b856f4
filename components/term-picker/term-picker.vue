<template>
    <view class="pickerMask" v-if="visable" @click="maskClick">
        <view class="picker-box">
            <view class="operate-box">
                <view class="operate">
                    <view class="cancel" @click.prevent.stop="confirm(1)">取消</view>
                    <view class="determine" @click.prevent.stop="confirm(2)">确定</view>
                </view>
                <picker-view :value="pickerValue" @change="pickerChange" class="picker-view" :indicator-style="indicatorStyle" @tap.stop="returnHandle">
                    <picker-view-column>
                        <view class="picker-item" v-for="(item, index) in years" :key="index">{{item}}年</view>
                    </picker-view-column>
                    <picker-view-column>
                        <view class="picker-item" v-for="(item, index) in months" :key="index">{{ item }}月</view>
                    </picker-view-column>
                    <picker-view-column v-if="days.length > 0">
                        <view class="picker-item" v-for="(item, index) in days" :key="index">{{ item }}日</view>
                    </picker-view-column>
                </picker-view>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    props: {
        visable: {
            type: Boolean,
            default: false
        },
        defaultDate: {
		    type: String,
            default: ''
        },
        minYear: {
            type: Number,
            default: 1990,
        },
        timeLimit: {
            type: Boolean,
            default: false
        },
        deferYear: {
            type: Number,
            default: 0,
        },
    },
    data() {
        const date = new Date();
        const years = [];
        const year = date.getFullYear();
        const months = [];
        const month = date.getMonth() + 1;
        const day = date.getDate();
        const maxYear = this.timeLimit ? year : year + this.deferYear
        for (let i = this.minYear; i <= maxYear; i++) {
                years.push(i);
        }
        for (let i = 1; i <= 12; i++) {
            months.push(i);
        }
        return {
                indicatorStyle: 'height: 100rpx;',
                touchIndex: 0,
                year,
				month,
				day,
				years,
                months,
                days: [],
				pickerValue: [],
				resultDate:'',
                popUp:true
        };
    },
    mounted() {
		this.setDate()
	},
    methods: {
        setDate() {
		    if (this.defaultDate.length > 0) {
					let date = this.defaultDate
					this.resultDate = this.defaultDate
					this.setPicker(date)
			} else {
					let month = this.month < 10 ? '0' + this.month : this.month
					let day = this.day < 10 ? '0' + this.day : this.day
					let nowTime = this.year + '-' + month + '-' + day
					this.resultDate = [nowTime]
					this.setPicker(nowTime)
			}
		},
        setPicker(date) {
				const splitVal = date.split('-')
				let year = this.years.indexOf(Number(splitVal[0]))
				let month = Number(splitVal[1]) - 1
				let day = Number(splitVal[2]) - 1
				this.pickerChange({
					detail: {
						value: [year, month, day]
					}
				})
		},
        pickerChange(e) {
			    const currents = e.detail.value;
			    if (currents[1] + 1 === 2) {
			        this.days = []
			        if (
			            ((currents[0] + this.minYear) % 4 === 0 &&
			                (currents[0] + this.minYear) % 100 !== 0) ||
			            (currents[0] + this.minYear) % 400 === 0
			        ) {
			            for (let i = 1; i < 30; i++) {
			                this.days.push(i)
			            }
			        } else {
			            for (let i = 1; i < 29; i++) {
			                this.days.push(i)
			            }
			        }
			    } else if ([4, 6, 9, 11].some((item) => currents[1] + 1 === item)) {
			        this.days = []
			        for (let i = 1; i < 31; i++) {
			            this.days.push(i)
			        }
			    } else if (
			        [1, 3, 5, 7, 8, 10, 12].some((item) => currents[1] + 1 === item)
			    ) {
			        this.days = []
			        for (let i = 1; i < 32; i++) {
			            this.days.push(i)
			        }
			    }
				this.pickerValue = currents
				this.getDateTime(currents)
			},
            getDateTime(date) {
				let year = this.years[date[0]]
				let month = this.months[date[1]]
				let day = this.days[date[2]]
				if (month < 10) {
					month = '0' + month
				}
				if (day < 10) {
					day = '0' + day
				}
				this.resultDate =  year + '-' + month + '-' + day
			},
            confirm(e) {
                let value = ''
                if(e === 1){
                   // value = this.defaultDate == ''? '' : this.resultDate
				   this.$emit('update:visable', false)
				   return
                }else {
                    value = this.resultDate
                }
                this.$emit('changeTime', value);
            },
			maskClick(){
				this.$emit('update:visable', false)
			}
    },
}
</script>

<style lang="scss" scoped>
    .pickerMask {
        position: fixed;
        z-index: 999;
        top: 0;
        right: 0;
        left: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        .picker-box {
            position: fixed;
            bottom: 0;
            left: 0;
            transition: all 0.3s ease;
            z-index: 1000;
            width: 100%;
            .operate-box {
                background: #ffffff;
                padding: 24rpx;
            }
            .operate {
              display: flex;
              justify-content: space-between;
              font-size: 30rpx; 
              .determine {
                color: #40CA8F;
              }
            }
            .picker-view {
                width: 100%;
                height: 200px;
                margin-top: 10px;
                .picker-item {
                    height: 100rpx;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    text-align: center;
                }
            }
        }
    }
</style>