<template>
  <div class="search-container">
    <!-- 使用 u-search 组件 -->
	 <u-input v-model="keyword"  @input="handleSearch"  :placeholder="foSubjectType == 1? '请输入企业名称' :'请输申请人姓名'" >
  
    </u-input>
    <!-- 搜索结果列表 -->
    <div v-if="showResults" class="search-results">
      <div v-for="(result, index) in searchResults" :key="index" class="search-result" @click="selectResult(result)">
        {{ result.companyName||result.userName }}
      </div>
    </div>
  </div>
</template>

<script>
	import {
		searchUser,
		listEnterprise
	} from "@/api/dict.js"
	
export default {
	props: {
		foSubjectType: {
			type: String,
			default: '1'
		},
		
	},
  data() {
    return {
      keyword: '', // 搜索关键字
      searchResults: [], // 搜索结果
      showResults: false, // 控制搜索结果显示
	  userSelected :false
    };
  },
  methods: {
    // 处理搜索
    handleSearch() {
		if (!this.userSelected) {
			if (this.keyword.trim() === '') {
			  this.searchResults = []; // 清空搜索结果
			  this.showResults = false; // 隐藏搜索结果
			  return;
			}
			// 模拟远程搜索，实际应替换为真实的远程搜索接口调用
			if(this.foSubjectType==1){
				listEnterprise({
					approveStatus:1,
					companyTypeList : [1, 2, 5, 6, 7, 8, 9, 10, 12, 100],
					companyName: this.keyword
				}).then(res => {
					console.log('res',res)
					if(res.code==200){
						this.searchResults =res.result.list||[]
						this.showResults = true;
					}
				})
			}else{
				searchUser({
					phonenumber: this.keyword
				}).then(res => {
					console.log('res',res)
					if(res.code==200){
						this.searchResults =res.result||[]
						this.showResults = true;
					}
				})
			}
			
			
		}
      this.userSelected = false; // 重置标志位
    },
    // 选择搜索结果
    selectResult(result) {
      console.log('选中的搜索结果：', result);
      // 处理选中搜索结果的逻辑，例如填充搜索关键字到输入框等
      this.keyword = result.companyName||result.userName
	  this.$emit('getFoApplyerInfo',result)
      this.searchResults = []; // 清空搜索结果
      this.showResults = false; // 隐藏搜索结果
	  this.userSelected = true; 
    }
  }
};
</script>

<style scoped>
.search-container {
  position: relative;
}

.search-results {
  position: absolute;
  width: 80%;
  top: 110%;
  left: 0;
  z-index: 999;
  background-color: #fff;
  border: 1px solid #ccc;
  /* border-top: none; */
  max-height: 200rpx;
  overflow-y: auto;
}

.search-result {
  padding: 8rpx 10rpx;
  cursor: pointer;
}

.search-result:hover {
  background-color: #f0f0f0;
}
</style>
