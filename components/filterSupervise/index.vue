<template>
	<view>
		<u-popup v-model="showPicker" mode="bottom" border-radius="14" :closeable="true" @close="canel">
			<scroll-view scroll-y="true" class="scroll-view">
				<u-form :model="form" ref="form"  :label-style="labelStyle">
					<view class="regulatory-area">
						<h3 class="regulatory-area-title">需求方公司名称</h3>
						<u-form-item prop="companyName">
							<u-input :custom-style="customStyle" :placeholder-style="placeholderStyle"  v-model="form.companyName" placeholder="输入需求方公司名称" />
						</u-form-item>
					</view>
					<view class="regulatory-area">
						<h3 class="regulatory-area-title">项目经理</h3>
						<u-form-item prop="projectManagerName">
							<u-input :custom-style="customStyle" :placeholder-style="placeholderStyle"  v-model="form.projectManagerName" placeholder="输入项目经理姓名或联系电话" />
						</u-form-item>
					</view>
					<view class="regulatory-area">
						<h3 class="regulatory-area-title">订单日期范围</h3>
						<view class="time-view">
							<view class="start-time" :style="startTime.title === '起始时间' ? 'color:#999' : 'color:#222'"
								@click="handleShowTime('startTime')">
								{{ startTime.title }}
							</view>
							-
							<view class="start-time" :style="endTime.title === '结束时间' ? 'color:#999' : 'color:#222'"
								@click="handleShowTime('endTime')">
								{{ endTime.title }}
							</view>
						</view>
					</view>
					<view class="regulatory-area">
						<h3 class="regulatory-area-title">活畜单价区间</h3>
						<view class="time-view">
							<u-input :custom-style="customStyle" :placeholder-style="placeholderStyle"  v-model="form.unitPriceMin" placeholder="最小值" clearable="false" input-align="center" />
							-
							<u-input :custom-style="customStyle" :placeholder-style="placeholderStyle"  v-model="form.unitPriceMax" placeholder="最大值" clearable="false" input-align="center"/>
						</view>
					</view>
				</u-form>
				<view :style="'height:'+ (isIphonex ? 48 : 24) + 'rpx'"></view>
			</scroll-view>
			<view class="button-group">
				<view class="button-group-view box">
					<view class="button-group-reset flex" @click="resetForm">
						重置
					</view>
					<view class="button-group-submit flex" @click="submitForm">
						确认
					</view>
				</view>
			</view>
		</u-popup>
		<u-picker :safe-area-inset-bottom="true" v-model="showTime" mode="time" @confirm="submitTime"></u-picker>
		
		<u-calendar range-bg-color="rgba(64,202,143,0.13)" active-bg-color="#40CA8F"  range-color="#40CA8F" btn-type="success" v-model="showData" :mode="`range`" @change='changeData'></u-calendar>
		
	</view>
</template>

<script>
	import {
		mapState
	} from "vuex"
	export default {
		components: {
		},
		props: {
			pickerFilterShow: {
				type: Boolean,
				default: false
			}
		},
		created() {
		},
		data() {
			return {
                customStyle: { fontSize: '26rpx' },
                placeholderStyle: ';color:#999;font-size: 26rpx;',
				showData :false,
				showTime: false, //时间弹出选择框
				endTime: {
					title: '结束时间',
					timestamp: 0
				}, 
				startTime: {
					title: '起始时间',
					timestamp: 0
				},
				form: {
					startTime: '',
					endTime: '',
					unitPriceMin: '',
					unitPriceMax: '',
					companyName: '',
					projectManagerName:''
				},
				showPicker: false,
				typeTime: ''
			}
		},
		watch: {
			superviseStatus: {
				handler(newValue, oldValue) {
					this.status = newValue
				},
				immediate: true,
				deep: true
			},
			pickerFilterShow: {
				handler(newValue, oldValue) {
					console.log(newValue, 'newValue')
					this.showPicker = newValue
				},
				immediate: true,
				deep: true
			}
		},
		computed: {
			...mapState({
				userInfo: (state) => state.userDetail.user,
			}),
		},
		methods: {
			canel() {
				this.$emit('canel')
			},
			submitTime(val) {
				const arr = ['startTime', 'endTime']
				arr.map((item, index) => {
					if (item === this.typeTime) {
						this[item].timestamp = val.timestamp
						if (index === 0 || index === 1) {
							if (this.startTime.timestamp > this.endTime.timestamp && this.endTime.timestamp !== 0) {
								uni.showToast({
									title: '结束时间不能小于起始时间',
									icon: 'none'
								})
								return false
							}
						}
						this[item].title = val.year + '-' + val.month + '-' + val.day
						this.form[item] = val.year + '-' + val.month + '-' + val.day
					}
				})
			},
			handleShowTime(val) {
				this.showData = true;
				/* if (this.startTime.timestamp  === 0 && val === 'endTime') {
					uni.showToast({
						title: '请选择起始日期',
						icon: 'none'
					})
					return
				}
				this.typeTime = val
				this.showTime = true */
				
			},
			changeData(e){
				this.startTime.title = e.startDate;
				this.endTime.title = e.endDate;
				this.form.startTime = e.startDate;
				this.form.endTime = e.endDate;
				this.showData = false;
			},
			submitForm() {
				this.$emit('submitForm', this.form)
			},
			reset() {
				this.endTime.title = "起始时间"
				this.startTime.title = "结束时间"
				this.endTime.timestamp = 0
				this.startTime.timestamp = 0
				this.form = {
					startTime: '',
					endTime: '',
					unitPriceMin: '',
					unitPriceMax: '',
					companyName: '',
					projectManagerName:''
				}
			},
			resetForm() {
				this.$refs.clientSelectRef.resetClick()
				this.reset()
				this.$emit('resetSearch', false)
				this.$emit('canel')
			}
		}
	}
</script>

<style lang="less" scoped>
	.box{
		display: flex;
		align-items: start;
	}
	.flex{
		flex: 1;
	}
	.button-group {
		width: 100%;
		padding: 29rpx;
		position: relative;
		background-color: white;
		z-index: 10;
		// margin-top: 53rpx;

		.button-group-view {
			border-radius: 100rpx 100rpx 100rpx 100rpx;
			display: flex;
			background-color: #40CA8F ;
            text-align: center;
			
			.button-group-reset {
				padding: 20rpx 0;
				background: #FFFFFF;
				border-radius: 100rpx 0rpx 120rpx 100rpx;
				border: 2rpx solid #40CA8F ;
				font-size: 32rpx;
				color: #40CA8F ;
				font-family: PingFang SC-Bold, PingFang SC;
			}

			.button-group-submit {
				padding: 20rpx 0;
				background: #40CA8F ;
				font-size: 32rpx;
				border-radius: 0rpx 100rpx 100rpx 0rpx;
				color: #FFFFFF;
			}
		}

	}

	.regulatory-area {
		padding: 29rpx;

		.regulatory-area-title {
			font-size: 28rpx;
			color: #333;
			font-weight: 400;
			padding-bottom: 24rpx;
			font-family: PingFang SC-Bold, PingFang SC;
		}

		.time-view {
			display: flex;
			justify-content: space-between;
			line-height: 50rpx;

			.start-time {
				width: 50%;
				border-bottom: 1px solid #F4F4F4;
				text-align: center;
				color: #999;
			}

		}
	}

	.scroll-view {
		height: 1000rpx;
		overflow-y: scroll;
	}

	.job-other-type {
		padding: 8rpx 29rpx;
		// padding-top: 0;

		.job-type-title {
			font-size: 28rpx;
			color: #333;
			font-weight: 400;
			padding-bottom: 24rpx;
			font-family: PingFang SC-Bold, PingFang SC;
		}

		.job-type-content {
			display: flex;
			flex-wrap: wrap;

			p {
				color: #999;
				background-color: #F4F4F4;
				padding: 17rpx 47rpx;
				border-radius: 100rpx 100rpx 100rpx 100rpx;
				margin: 0 21rpx 30rpx 0;
				border: 2rpx solid transparent;
			}

			.policy-type-active {
				border: 2rpx solid #40CA8F ;
				color: #40CA8F ;
				background-color: white;
			}

			.ear-type-active {
				border: 2rpx solid #40CA8F ;
				color: #40CA8F ;
				background-color: white;
			}
		}
	}

	.job-type {
		padding: 29rpx;
		margin-top: 10rpx;

		.job-type-title {
			font-size: 28rpx;
			color: #333;
			font-weight: 400;
			padding-bottom: 24rpx;
			font-family: PingFang SC-Bold, PingFang SC;
		}

		.job-type-content {
			display: flex;
			flex-wrap: wrap;

			p {
				color: #999;
				background-color: #F4F4F4;
				padding: 17rpx 47rpx;
				border-radius: 100rpx 100rpx 100rpx 100rpx;
				margin: 0 30rpx 30rpx 0;
				border: 2rpx solid transparent;
			}

			.job-type-active {
				border: 2rpx solid #40CA8F ;
				color: #40CA8F ;
				background-color: white;
			}

			.policy-type-active {
				border: 2rpx solid #40CA8F ;
				color: #40CA8F ;
				background-color: white;
			}

			.ear-type-active {
				border: 2rpx solid #40CA8F ;
				color: #40CA8F ;
				background-color: white;
			}
		}

	}

	uni-picker-view {
		display: block;
	}

	uni-picker-view .uni-picker-view-wrapper {
		display: flex;
		align-items: center;
		position: relative;
		overflow: hidden;
		height: 100%;
		background-color: white;
	}

	uni-picker-view[hidden] {
		display: none;
	}

	picker-view {
		width: 100%;
		// height: 600upx;
		height: 600rpx;
		margin-top: 20upx;
	}

	.item {
		line-height: 100upx;
		text-align: center;
	}

	.popup-view {
		.picker-btn {
			display: flex;
			justify-content: space-between;
			padding: 30rpx 40rpx;

			.left {
				color: #999;
				font-size: 28rpx;
				font-family: PingFang SC-Medium;
			}

			.middle {
				font-size: 32rpx;
				color: #222;
				font-family: PingFang SC-Heavy;
			}

			.right {
				color: #40CA8F ;
				font-size: 32rpx;
				font-family: PingFang SC-Medium;
			}
		}
	}
</style>