<template>
    <view class="filter-wrapper" :style="{ height: height + 'rpx', top: top,'border-top':border?'1rpx solid #f2f2f2':'none' }"  @touchmove.prevent>
        <view class="inner-wrapper"  @touchmove.prevent>
            
            <view class="navs">
                <view class="c-flex-align" :class="{ 'c-flex-center': index > 0, actNav: index === actNav }" v-for="(item, index) in navData" :key="index" @click="navClick(index)">
                    <view v-for="(child, childx) in item" :key="childx" v-if="child.select&&index==0">{{'区域级别:'+ child.text }}</view>
					<view v-for="(child, childx) in item" :key="childx" v-if="child.select&&index==1">{{ '所属区域:'+child.text }}</view>
                    <image src="https://i.loli.net/2020/07/15/QsHxlr1gbSImvWt.png" mode="" class="icon-triangle" v-if="index === actNav"></image>
                    <image src="https://i.loli.net/2020/07/15/xjVSvzWcH9NO7al.png" mode="" class="icon-triangle" v-else></image>
                </view>


                
            </view>
			<view  v-if='actNav==1' @touchmove.stop>
			    <view  style="display: flex;"   @touchmove.stop>
					<scroll-view scroll-y="true"  class="popup" :class="popupShow ? 'popupShow' : ''" @touchmove.stop>
						<view class="" style="display: flex;flex-direction: column;align-items: center;" @touchmove.stop>
							<text @touchmove.stop style="padding: 20rpx 0;width: 100%;justify-content: center;" class="c-flex-align" :class="item.value==provinceId ? 'actOptArea' : ''"   v-for="(item, index) in area" @click="selectProvince(item)" :key="index">
								{{ item.text }}
								
							</text>
						</view>
					</scroll-view>
					<scroll-view scroll-y="true"  class="popup" :class="popupShow ? 'popupShow' : ''"  :scroll-top="scrollTop" @scroll="scroll" @touchmove.stop.prevent="discard">
						<view style="display: flex;flex-direction: column;align-items: center;">
							<text style="padding: 20rpx 0;width: 100%;justify-content: center;" class="c-flex-align" :class="child.value==cityId ? 'actOptArea' : ''" v-for="(child, childx) in citys" @click='selectCity(child)' :key="childx" v-if="child">{{ child.text }}</text>
						</view>
					</scroll-view>
			   	
					
			    </view>
				<view class="" style="display: flex;    justify-content: space-evenly;padding: 30rpx 0;background-color: #fff;height: 50rpx;">
					<view class="btn" style="" @tap="onSubmit">确定</view>
					<view  class="btn" @tap="onReset">重置</view>
				</view>
			</view>
			
            <scroll-view v-else scroll-y="true" class="popup" :class="popupShow ? 'popupShow' : ''" @touchmove.stop.prevent="discard">
				
				
				
					<view class="item-opt c-flex-align" :class="item.select ? 'actOpt' : ''" v-for="(item, index) in navData[actNav]" :key="index" @click="handleOpt(index)">
					    {{ item.text }}
					</view>
			
                
            </scroll-view>
			<view class="mask" :class="showMask ? 'show' : 'hide'" @tap="tapMask">
				
				
				
			</view>
        </view>
    </view>
</template>

<script>
// import { getCurDateTime } from '@/libs/utils.js';
import provincialLevel from '@/utils/provincialLevel.js'
export default {
    props: {
        height: {
            type: Number,
            default: 108
        },
        top: {
            type: String,
            default: 'calc(var(--window-statsu-bar) + 44px)'
        },
        border: {
            type: Boolean,
            default: false
        },
        filterData: {
            //必填
            type: Array,
            default: () => {
                return [];
            }
            // default: () => {
            //     return [
            //         [{ text: '全部状态', value: '' }, { text: '状态1', value: 1 }, { text: '状态2', value: 2 }, { text: '状态3', value: 3 }],
            //         [{ text: '全部类型', value: '' }, { text: '类型1', value: 1 }, { text: '类型2', value: 2 }, { text: '类型3', value: 3 }]
            //     ];
            // }
        },
        defaultIndex: {
            //默认选中条件索引,超出一类时必填
            type: Array,
            default: () => {
                return [0];
            }
        }
    },
    data() {
        return {
			show:true,
			
			
            navData: [],
            popupShow: false,
			
            showMask: false,
            actNav: null,
            selDate: '选择日期',
            selIndex: [], //选中条件索引
			area:[],
			citys:[],
			provinceId:"",
			cityId:"",
			scrollTop: 0,
			old: {
				scrollTop: 0
			},
			activeCity:{},
			activeProvince:{"value": "","text": "全国"}
			
			
        };
    },
    created() {
		// console.log(this.area)
		if(this.area.length==0){
			let list=provincialLevel.result
			if(list[0].text!='全国'){
				list.unshift({"value": "","text": "全国",children:[]})
			}
			
			this.area=list
			
			// this.area.unshift({"value": "","text": "全国",children:[]})
		}
		
		this.citys=this.area[0]?.children
		
		this.provinceId=this.area[0].value
		
		
		this.cityId=this.citys[0]?.value
		// console.log(this.area)
        this.navData = this.filterData;
        this.selIndex = this.defaultIndex;
        this.keepStatus();
    },
    mounted() {
        // this.selDate = getCurDateTime().formatDate;
    },
    methods: {
		scroll(e) {
			// console.log(e)
			this.old.scrollTop = e.detail.scrollTop
		},
		selectProvince(item){
			// console.log(item.value)
			this.activeProvince=item
			this.activeCity=item.children[0]
			
			this.citys=item.children
			
			this.provinceId=item.value
			// console.log(this.citys)
			if(this.citys){
				this.cityId=this.citys[0].value
				
				this.scrollTop = this.old.scrollTop
				this.$nextTick(function() {
					this.scrollTop = 0
				});
			}else{
				this.citys=[]
			}
			
		},
		selectCity(item){
			this.cityId=item.value
			this.activeCity=item
		},
        keepStatus() {
            this.navData.forEach(itemnavData => {
                itemnavData.map(child => {
                    child.select = false;
                });
                return itemnavData;
            });
            for (let i = 0; i < this.selIndex.length; i++) {
                let selindex = this.selIndex[i];
                this.navData[i][selindex].select = true;
            }
        },
        navClick(index) {
			// console.log(index)
			
            if (index === this.actNav) {
                this.tapMask();
                return;
            }
            this.popupShow = true;
			
			this.$emit('onPopup', 'hidden');
            this.showMask = true;
            this.actNav = index;
        },
		
        handleOpt(index) {
            this.selIndex[this.actNav] = index;
            this.keepStatus();
            setTimeout(() => {
                this.tapMask();
            }, 100);
            let data = [];
            let res = this.navData.forEach(item => {
                let sel = item.filter(child => child.select);
                data.push(sel);
            });
            // console.log(data);
            this.$emit('onSelected', data);
        },
        dateClick() {
            this.tapMask();
        },
        tapMask() {
            this.showMask = false;
            this.popupShow = false;
			this.$emit('onPopup', 'auto');

            this.actNav = null;
        },
		onSubmit(){
			// this.selIndex[this.actNav] = index;
			
			
			// console.log(this.activeCity)
			// console.log(this.activeProvince)
			// this.$nextTick(function(){
			// 	this.filterData[1]=[{text:'A/b',value:'111'}]
			// })
			if(this.activeProvince.text=='全国'){
				this.$set(this.filterData,1,[{text:'全国',value:'111',select:true}])
			}else{
				this.$set(this.filterData,1,[{text:this.activeProvince.text+'/'+this.activeCity.text,value:'111',select:true}])
				
			}

			this.$emit('onSubmit', {activeCity:this.activeCity,activeProvince:this.activeProvince});
			this.showMask = false;
			this.popupShow = false;
			this.$emit('onPopup', 'auto');
			this.actNav = null;
		},
		onReset(){
			this.$set(this.filterData,1,[{text:'全国',value:'111',select:true}])
			this.$emit('onSubmit', {activeProvince:{text:'全国'}});
			this.showMask = false;
			this.popupShow = false;
			this.$emit('onPopup', 'auto');
			this.actNav = null;
		},
        handleDate(e) {
            let d = e.detail.value;
            this.selDate = d;
            this.$emit('dateChange', d);
        },
        discard() {}
    }
};
</script>

<style lang="scss" scoped>
page {
    font-size: 24rpx;
}
.c-flex-align {
    display: flex;
    align-items: center;
	
	font-size: 24rpx;
}
.c-flex-center {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
}
.filter-wrapper {
    // position: fixed;
    left: 0;
    width: 750rpx;
    z-index: 999;
    .inner-wrapper {
		display: flex;
		flex-direction: column;
        // position: relative;
        .navs {
            position: relative;
            height: 110rpx;
            padding: 0 40rpx;
            display: flex;
            align-items: center;
            justify-content: space-between;
            background-color: #fff;
            border-bottom: 2rpx solid #f5f6f9;
            color: #222222;
            z-index: 999;
            box-sizing: border-box;
            & > view {
                flex: 1;
                height: 100%;
                flex-direction: row;
                z-index: 999;
            }
            .date {
                justify-content: flex-end;
            }
            .actNav {
                color: #4d7df9;
                font-weight: bold;
            }
        }
        .mask {
            // z-index: 666;
			width: 100%;
			height: 1000rpx;
            // position: fixed;
            // top: calc(var(--status-bar-height) + 44px);
            // left: 0;
            // right: 0;
            // bottom: 0;
            background-color: rgba(0, 0, 0, 0);
            transition: background-color 0.15s linear;
            &.show {
                background-color: rgba(0, 0, 0, 0.4);
            }
            &.hide {
                display: none;
            }
        }
        .popup {
            position: relative;
            max-height: 500rpx;
            background-color: #fff;
            // border-bottom-left-radius: 20rpx;
            // border-bottom-right-radius: 20rpx;
            overflow: scroll;
            z-index: 999;
            transition: all 1s linear;
            opacity: 0;
            display: none;
            .item-opt {
                height: 100rpx;
                padding: 0 40rpx;
                color: #8b9aae;
                border-bottom: 2rpx solid #f5f6f9;
            }
            .actOpt {
                color: #4d7df9;
                font-weight: bold;
                position: relative;
                &::after {
                    content: '✓';
                    font-weight: bold;
                    font-size: 24rpx;
                    position: absolute;
                    right: 40rpx;
                }
            }
        }
        .popupShow {
            display: block;
            opacity: 1;
        }
    }

    .icon-triangle {
        width: 16rpx;
        height: 16rpx;
        margin-left: 10rpx;
    }
	.actOptArea{
		color: #40CA8F;
		background-color: rgba(3, 3, 3, 0.1);
	}
}
.btn{
	background-color: #426CF6;color: #fff;padding: 5rpx 30rpx;border-radius: 25rpx;
	line-height: 50rpx;height: 50rpx;
	
}
</style>
