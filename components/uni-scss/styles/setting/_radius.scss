@mixin radius($r,$d:null ,$important: false){
  $radius-value:map-get($uni-radius, $r) if($important, !important, null);
  // Key exists within the $uni-radius variable
  @if (map-has-key($uni-radius, $r) and  $d){
		@if $d == t {
				border-top-left-radius:$radius-value;
				border-top-right-radius:$radius-value;
		}@else if $d == r {
				border-top-right-radius:$radius-value;
				border-bottom-right-radius:$radius-value;
		}@else if $d == b {
				border-bottom-left-radius:$radius-value;
				border-bottom-right-radius:$radius-value;
		}@else if $d == l {
				border-top-left-radius:$radius-value;
				border-bottom-left-radius:$radius-value;
		}@else if $d == tl {
				border-top-left-radius:$radius-value;
		}@else if $d == tr {
				border-top-right-radius:$radius-value;
		}@else if $d == br {
				border-bottom-right-radius:$radius-value;
		}@else if $d == bl {
				border-bottom-left-radius:$radius-value;
		}
  }@else{
		border-radius:$radius-value;
  }
}

@each $key, $child in $uni-radius {
	@if($key){
		.uni-radius-#{"" + $key} {
				@include radius($key)
		}
	}@else{
		.uni-radius {
				@include radius($key)
		}
	}
}

@each $direction in t, r, b, l,tl, tr, br, bl {
	@each $key, $child in $uni-radius {
		@if($key){
			.uni-radius-#{"" + $direction}-#{"" + $key} {
				@include radius($key,$direction,false)
			}
		}@else{
			.uni-radius-#{$direction} {
				@include radius($key,$direction,false)
			}
		}
	}
}
