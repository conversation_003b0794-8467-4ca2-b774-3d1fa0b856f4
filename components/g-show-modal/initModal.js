import Vuex from 'vuex'
import store from '../../store/index'
export default function initModal(v) {
  // 挂在store到全局Vue原型上
  v.prototype.$modalStore = new Vuex.Store({
    state: {
      closeTime: "", //是否显示自动关闭倒计时，有值就会自动倒计时，且时间为0自动关闭弹窗
      show: false, //弹窗显示影藏
      autoClose: true, //点击遮罩是否自动关闭模态框
      title: "", //标题
      modalIcon: "worn", //ask  succese  worn 
      contentOther: '', //内容的其他文本
      content: '',
      showCancel: true, //是否显示取消按钮，默认为 true
      cancelText: "取消",
      cancelColor: "#888888", //取消按钮的文字颜色，默认为"#888888"
      confirmText: "确定",
      confirmColor: "#0157DB", //确定按钮的文字颜色
      hideTabBar: false, //是否隐藏tabBar在APP和小程序中隐藏tabBar 可以让遮罩层全屏
      success: null, //接口调用成功的回调函数
    },
    mutations: {
      hideModal(state) {
        // 小程序导航条页面控制
        // #ifndef H5
        if (state.hideTabBar) {
          uni.showTabBar();
        }
        // #endif
        state.show = false
      },
      showModal(state, data) {
        state = Object.assign(state, data)
        state.show = true
      },
      success(state, res) {
        let cb = state.success
        let resObj = {
          cancel: false,
          confirm: false
        }
        res == "confirm" ? resObj.confirm = true : resObj.cancel = true
        cb && cb(resObj)
      }
    }
  })
  // 注册$showModal到Vue原型上，以方便全局调用
  v.prototype.$showModal = function(option) {
    if (typeof option === 'object') {
      // #ifndef H5
      if (option.hideTabBar) {
        wx.hideTabBar();
      }
      // #endif
      v.prototype.$modalStore.commit('showModal', option)
    } else {
      throw "配置项必须为对象传入的值为：" + typeof option;
    }
  }
}
