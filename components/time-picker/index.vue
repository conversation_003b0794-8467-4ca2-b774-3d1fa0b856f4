<template>
	<view :class="{'pickerMask':visable}" @click="maskClick" @touchmove.stop.prevent="returnHandle">
		<view class="picker-box" :class="{'picker-show':visable}">
            <view class="operate-box">
                <view class="title">选择日期</view>
                <view class="time-box" @touchmove.stop.prevent="returnHandle" @tap.stop="returnHandle">
                    <view class="time-item"  @click="touchSelect(0)">
                        <text :class="startTime ? 'text' : 'tips' ">{{startTime ? startTime :'开始时间'}}</text>
                    </view>
                    <view class="to">-</view>
                    <view class="time-item" @click="touchSelect(1)" >
                        <text :class="endTime ? 'text' : 'tips' ">{{endTime  ? endTime  :'结束时间'}}</text>
                    </view>
                </view>
                <picker-view :value="pickerValue" @change="pickerChange" class="picker-view" :indicator-style="indicatorStyle" @tap.stop="returnHandle">
                    <picker-view-column>
                        <view class="picker-item" v-for="(item, index) in years" :key="index">{{item}}年</view>
                    </picker-view-column>
                    <picker-view-column>
                        <view class="picker-item" v-for="(item, index) in months" :key="index">{{ item }}月</view>
                    </picker-view-column>
                    <picker-view-column v-if="days.length > 0">
                        <view class="picker-item" v-for="(item, index) in days" :key="index">{{ item }}日</view>
                    </picker-view-column>
                </picker-view>
                <view class="operate" @touchmove.stop.prevent="returnHandle" @tap.stop="returnHandle">
                    <view class="cancel" @click="cancelDate"><view class="cancelDate" >重置</view></view>
                   
                    <view class="pickerConfirm" @click="pickerConfirm">确定</view>
                </view>    
            </view>
		</view>
	</view>
</template>
<script>
    export default {
        name: 'termPicker',
        props: {
			visable: {
				type: Boolean,
				default: false
			},
			defaultDate: {
				type: Array,
				default: () => []
			},
            minYear: {
                type: Number,
                default: 1990,
            },
			timeLimit: {
				type: Boolean,
				default: false
			},
			deferYear: {
				type: Number,
				default: 0,
			},
			themeColor:{
				type: String,
				default: '#10BE9D'
			},
			startText: {
				type: String,
				default: '开始时间'
			},
			endText: {
				type: String,
				default: '结束时间'
			}
        },
        data() {
            const date = new Date();
            const years = [];
            const year = date.getFullYear();
            const months = [];
            const month = date.getMonth() + 1;
            const day = date.getDate();
			const maxYear = this.timeLimit ? year : year + this.deferYear
            for (let i = this.minYear; i <= maxYear; i++) {
                years.push(i);
            }
            for (let i = 1; i <= 12; i++) {
                months.push(i);
            }
            return {
                indicatorStyle: 'height: 100rpx;',
                touchIndex: 0,
                year,
				month,
				day,
				years,
                months,
                days: [],
				pickerValue: [],
				resultDate: [],
                startTime:'',
                endTime:'',
                isflag:false
            };
        },
		mounted() {
			this.setDate()
		},
        methods: {
			returnHandle(){},
			setDate() {
				if (this.defaultDate.length > 0) {
					let date = this.defaultDate[0]
					this.resultDate = this.defaultDate
					this.setPicker(date)
				} else {
					let month = this.month < 10 ? '0' + this.month : this.month
					let day = this.day < 10 ? '0' + this.day : this.day
					let nowTime = this.year + '-' + month + '-' + day
					this.resultDate = [nowTime, nowTime]
					this.setPicker(nowTime)
				}
			},
			setPicker(date) {
				const splitVal = date.split('-')
				let year = this.years.indexOf(Number(splitVal[0]))
				let month = Number(splitVal[1]) - 1
				let day = Number(splitVal[2]) - 1
				this.pickerChange({
					detail: {
						value: [year, month, day]
					}
				})
			},
			touchSelect(val) {
				let date = this.resultDate[val]
				this.touchIndex = val
				this.setPicker(date)
                this.isflag = true
			},
			getDateTime(date) {
				let year = this.years[date[0]]
				let month = this.months[date[1]]
				let day = this.days[date[2]]
				if (month < 10) {
					month = '0' + month
				}
				if (day < 10) {
					day = '0' + day
				}
				this.resultDate[this.touchIndex] =  year + '-' + month + '-' + day
                if(this.isflag) {
                    if(this.touchIndex === 0 ) this.startTime = this.resultDate[this.touchIndex]
                    if(this.touchIndex === 1) this.endTime = this.resultDate[this.touchIndex] 
                }
			},
			pickerChange(e) {
			    const currents = e.detail.value
			    if (currents[1] + 1 === 2) {
			        this.days = []
			        if (
			            ((currents[0] + this.minYear) % 4 === 0 &&
			                (currents[0] + this.minYear) % 100 !== 0) ||
			            (currents[0] + this.minYear) % 400 === 0
			        ) {
			            for (let i = 1; i < 30; i++) {
			                this.days.push(i)
			            }
			        } else {
			            for (let i = 1; i < 29; i++) {
			                this.days.push(i)
			            }
			        }
			    } else if ([4, 6, 9, 11].some((item) => currents[1] + 1 === item)) {
			        this.days = []
			        for (let i = 1; i < 31; i++) {
			            this.days.push(i)
			        }
			    } else if (
			        [1, 3, 5, 7, 8, 10, 12].some((item) => currents[1] + 1 === item)
			    ) {
			        this.days = []
			        for (let i = 1; i < 32; i++) {
			            this.days.push(i)
			        }
			    }
				this.pickerValue = currents
                console.log(this.pickerValue);
				this.getDateTime(currents)
			},
			maskClick() {
				this.$emit('update:visable', false)
			},
            pickerConfirm() {
				const { resultDate, timeLimit } = this
				if(!this.startTime){
					uni.showToast({
						title: '请选择开始时间',
						icon: 'none'
					})
					return
				}
				if(!this.endTime){
					uni.showToast({
						title: '请选择结束时间',
						icon: 'none'
					})
					return
				}
				let startTime = new Date(resultDate[0]).getTime()
				let endTime = new Date(resultDate[1]).getTime()
				let nowTime = timeLimit ? new Date().getTime() : endTime
				if (startTime <= endTime && endTime <= nowTime) {
                    let time = [];
                    time[0]= this.startTime
                    time[1]= this.endTime
					this.$emit('confirm', time)
					this.maskClick()
				} else {
					uni.showToast({
						title: '时间范围不正确！',
						icon: 'none'
					})
				}
                // this.$emit('pickerConfirm', )
            },
			cancelDate(){
               this.startTime = '';
               this.endTime =''
			   this.maskClick()
			   // this.resultDate=[]
			   this.$emit('resetTime')
				// let resultDate = ""
				// this.$emit('cancel', resultDate)
			}
        }
    }
</script>

<style lang="scss" scoped>
    .pickerMask {
        position: fixed;
        z-index: 998;
        top: 0;
        right: 0;
        left: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.6);
    }
    .picker-box {
        position: fixed;
        bottom: 0;
        left: 0;
        width: 100%;
        transition: all 0.3s ease;
        transform: translateY(100%);
        z-index: 998;
    	.operate-box {
    	    /* display: flex;
    		align-items: center;
    		justify-content: space-between; */
    	    padding: 31rpx 30rpx 19rpx;
    	    background-color: #FFFFFF;
    	    text-align: center;
    		border-bottom: 2rpx solid #e5e5e5;
            border-radius: 20rpx 20rpx 0 0;
            box-sizing: border-box;
            .title {
                width: 100%;
                font-size: 30rpx;
                font-family: PingFang SC-Bold, PingFang SC;
                font-weight: 550;
                color: #333333; 
                text-align: center;
                padding-bottom: 39rpx;
                border-bottom: 2rpx solid #f5f5f5;
            }
            .time-box {
                display: flex;
                align-items: center;
                margin: 40rpx 0;
                justify-content: center;
                .time-item {
                    padding: 20rpx 0rpx;
                    width:330rpx ;
                    background:#F4F4F4 ;
                    border-radius: 100rpx 100rpx 100rpx 100rpx;
                }
                .to {
                    color: #999999; 
                    margin: 0 8rpx;
                }
                .tips {
                    font-size: 28rpx;
                    font-family: PingFang SC-Medium, PingFang SC;
                    font-weight: 400;
                    color: #999999;
                }
                .text {
                    font-size: 28rpx;
                    font-family: PingFang SC-Medium, PingFang SC;
                    font-weight: 400;
                }
            }
         
    	}
    }
    .picker-show {
        transform: translateY(0);
    }
    .picker-view {
    	width: 750rpx;
    	height: 500rpx;
    	background-color: #FFFFFF;
    	.picker-item {
    		height: 100rpx;
    		display: flex;
    		align-items: center;
    		justify-content: center;
    		text-align: center;
    	}
      
    }
    .operate {
        display: flex;
        margin-bottom: 19rpx;
        .cancel {
            width: 345rpx;
            height: 80rpx;
            background: #40CA8F;
            border: 2rpx solid #40CA8F;
            border-radius: 100rpx 0rpx 0rpx 100rpx;
            line-height: 80rpx;
        }
        .cancelDate {
            padding: 2rpx 0;
            height: 100%;
            background: #FFFFFF;
            border-radius: 100rpx 0rpx 150rpx 100rpx;
            font-size: 30rpx;
            font-family: PingFang SC-Bold, PingFang SC;
            font-weight: 400;
            color:#40CA8F;
        }
        .pickerConfirm {
            width: 345rpx;
            height: 80rpx;
            background: #40CA8F;
            border-radius:0rpx 100rpx 100rpx 0rpx;
            font-size: 30rpx;
            font-family: PingFang SC-Bold, PingFang SC;
            font-weight: 400;
            color: #FFFFFF;
            line-height: 80rpx;
        }
        .picker-view {
            width: 750rpx;
            height: 500rpx;
            background-color: #FFFFFF;
            .picker-item {
                height: 100rpx;
                display: flex;
                align-items: center;
                justify-content: center;
                text-align: center;
            }
        
        }
    }
</style>