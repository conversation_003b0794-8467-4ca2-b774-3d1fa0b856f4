<template>
	<view class="picker">
		<u-tabs :list="tabs" :class="{initTabs: !current}" :current="current" @change="changeTab"></u-tabs>
		<template v-if="loading">
			<view class="picker-loading">
				<uni-icons type="refreshempty" size="51" color="#c2c2c2"></uni-icons>
			</view>
		</template>

		<template v-else>
			<scroll-view class="picker-content" :scroll-y="true" :scroll-top="scrollTop">
				<view v-for="(item, index) in listReal" :key="index" class="picker-content-list"
					@click="handleClick(item)">
					<view :style="item.isSel?'color:#007aff':''">{{ item.deptName }}</view>
					<uni-icons v-show="item.isSel" type="checkmarkempty" color="#007aff" />
				</view>
			</scroll-view>
		</template>

		<view v-if="showButton" class="picker-bottom">
			<view class="picker-bottom-button " @click="resetClick">
				重新选择
			</view>
			<view class="picker-bottom-button primary" @click="confirmClick">
				确定
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			list: {
				type: [Array, String],
				required: true
			},
			lazy: {
				type: Boolean,
				default: false
			},
			showButton: {
				type: Boolean,
				default: false
			}
		},
		emits: ['click'],
		data() {
			return {
				showTrue: false,
				tabs: [{
					name: '总行'
				}],
				current: 0,
				active: 1,
				listReal: [],
				listArray: [],
				selectedArray: [],
				selectedList: [],
				scrollTop: 0,
				loading: false,
				depIdMap: {},
				tabList: [{
						name: '总行'
					},
					{
						name: '省行'
					},
					{
						name: '市行/支行'
					},
					{
						name: '支行'
					}
				]
			};
		},

		onReady() {
		},
		methods: {
			changeTab(index) {
				if (!index) {
					this.init()
					return
				}
				const tempObj = JSON.parse(JSON.stringify((this.depIdMap)))
				const arr = Object.entries(tempObj).slice(0, index)
				const arr1 = Object.fromEntries(arr)
				const tenantId = this.depIdMap[index]
				console.log(this.selectedList)
				
			},
			handleClick(item) {
				const parentFlag = JSON.parse(JSON.stringify((this.active)))
				const current = JSON.parse(JSON.stringify((this.current)))
				const tenantId = item.deptId
			},

			resetClick() {
				this.init()
			},

			confirmClick() {
				this.$emit('getTenantIdValue', this.selectedList)
			},

		}
	};
</script>

<style lang="scss" scoped>
	.picker {
		background-color: $uni-text-color-inverse;

		&-content {
			height: 40vh;

			&-list {
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 3vw 4vw;
			}
		}

		&-bottom {
			cursor: pointer;
			padding: 6vw 3vw;
			display: flex;
			align-items: center;
			justify-content: space-around;
			background-color: $uni-text-color-inverse;

			&-button {
				flex: 1;
				text-align: center;
				white-space: nowrap;
				padding: 3vw;
				margin: 0 3vw;
				font-size: 4vw;
				border-radius: 2vw;
				background: $uni-text-color-inverse;
				color: $uni-color-primary;
				border: 1px solid $uni-color-primary;
			}

			.primary {
				color: $uni-text-color-inverse;
				background: $uni-color-primary;
			}
		}

		&-loading {
			height: 40vh;
			display: flex;
			align-items: center;
			justify-content: center;
			animation: loading 0.8s linear infinite;
		}
	}

	@keyframes loading {
		100% {
			transform: rotate(360deg);
			/* 360度旋转 */
		}
	}
</style>