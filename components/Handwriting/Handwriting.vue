<template>
  <view style="display: flex;flex-direction: column">
    <canvas class="mycanvas" canvas-id="mycanvas" @touchstart="touchstart" @touchmove="touchmove" @touchend="touchend"></canvas>
		<view class="middle">
			
		</view>
		<p class="fontit">请在页面空白处签字</p>
    <view class="footer">
      <view class="left" @click="cancle">取消</view>
			<view class="mid" @click="clear">重签</view>
			<view class="right" @click="finish">确认签字</view>
    </view>
  </view>
</template>

<script>
var x = 20
var y = 20
import { uploadFiles } from '@/api/obsUpload/index'
export default {
  data() {
    return {
      ctx: '', //绘图图像
      points: [], //路径点集合
    }
  },
  onReady() {
    // console.log('this.ctx')
    this.ctx = uni.createCanvasContext('mycanvas', this) //创建绘图对象
    // console.log('this.ctx', this.ctx)

    //设置画笔样式
    this.ctx.lineWidth = 4
    this.ctx.lineCap = 'round'
    this.ctx.lineJoin = 'round'
  },
  onLoad() {},
  methods: {
    //触摸开始，获取到起点
    touchstart: function (e) {
      let startX = e.changedTouches[0].x
      let startY = e.changedTouches[0].y
      let startPoint = { X: startX, Y: startY }
      this.points.push(startPoint)
      // 触摸开始，开启新路径
      this.ctx.beginPath()
    },

    //触摸移动，获取到路径点
    touchmove: function (e) {
      let moveX = e.changedTouches[0].x
      let moveY = e.changedTouches[0].y
      let movePoint = { X: moveX, Y: moveY }
      this.points.push(movePoint) //存点
      let len = this.points.length
      if (len >= 2) {
        this.draw() //绘制路径
      }
    },

    // 触摸结束，将未绘制的点清空防止对后续路径产生干扰
    touchend: function () {
      this.points = []
    },

    draw: function () {
      // console.log(this.ctx)
      let point1 = this.points[0]
      let point2 = this.points[1]
      this.points.shift()
      this.ctx.moveTo(point1.X, point1.Y)
      this.ctx.lineTo(point2.X, point2.Y)
      this.ctx.stroke()
      this.ctx.draw(true)
    },

    //清空画布
    clear: function () {
      let that = this
      uni.getSystemInfo({
        success: function (res) {
          let canvasw = res.windowWidth
          let canvash = res.windowHeight
          that.ctx.clearRect(0, 0, canvasw, canvash)
          that.ctx.draw(true)
        },
      })
    },

    //完成绘画并保存到本地
    finish: function () {
      console.log('保存')
      const that = this
      uni.canvasToTempFilePath(
        {
          canvasId: 'mycanvas',
          success: function (res) {
            console.log(res)
            uploadFiles({
                filePath: res.tempFilePath,
            }).then((data) => {
                that.$emit('submit', {url: data})
            })
          },
          fail: function (err) {
            console.log(err)
          },
        },
        this
      )
    },
		cancle(){
			uni.navigateBack({
					delta:1,//返回层数，2则上上页
				})
		}
  },
}
</script>

<style>
.title {
  height: 50upx;
  line-height: 50upx;
  font-size: 16px;
}
.mycanvas {
  width: 100%;
  height: calc(100vh - 200rpx);
  background-color: #FFFFFF;
	position: relative;
}
.footer {
	width: 100%;
  font-size: 16px;
  display: flex;
	flex: 1;
  justify-content: space-around;
  align-items: center;
	background: #FFFFFF;
}
.left,
.mid,
.right {
  line-height: 80rpx;
  height: 80rpx;
  width: 220rpx;
  text-align: center;
  font-weight: bold;
	border-radius: 8rpx 8rpx 8rpx 8rpx;
	font-size: 32rpx;
	font-family: Source Han Sans CN-Regular, Source Han Sans CN;
	font-weight: 400;
	color: #FFFFFF;
}
.left {
  background: #DDDDDD;
}
.right {
  background: #40CA8F;
}
.mid{
	background: #FF4F34;
}
.middle{
	width: 100%;
	height: 30rpx;
	background-color: #F7F7F7;
}
.fontit{
	position: absolute;
	font-size: 28rpx;
	font-family: Source Han Sans CN-Normal, Source Han Sans CN;
	font-weight: 400;
	color: #BBBBBB;
	line-height: 38rpx;
	margin-top: 44rpx;
	margin-left: 30rpx;
}

</style>
