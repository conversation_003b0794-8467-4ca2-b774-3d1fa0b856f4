<template>
  <view>
    <view class="box-top" v-for="(item, index) in operList" :key="index">
      <!-- 左边 -->
      <view class="line"
        :class="[{ active: item.markFlag === 1, notactive: item.markFlag === 2, disabled: item.markFlag === 3 }, { none: index === operList.length - 1 }]">
        <!-- 中线 -->
        <view class="dot"
          :class="[{ active: item.markFlag === 1, notactive: item.markFlag === 2, disabled: item.markFlag === 3 }]">{{ index +
            1 }}</view>
        <!-- 圆点 -->
      </view>

      <view class="right-box-top" @click="handleClick(item)">
        <!-- 右边 -->
        <view
          :class="{ 'active-name': item.markFlag === 1, 'notactive-name': item.markFlag === 2, 'disabled-name': item.markFlag === 3 }">
          {{ item.name }}</view>
        <view class="time" v-if="item.desc">{{ item.desc }}</view>
        <view class="time" v-if="item.updateTime">操作时间：{{ item.updateTime }}</view>
      </view>
    </view>
  </view>
</template>

<script>
import { purchaseOrderFlow } from '@/api/pages/purchaseOrder'

export default {
  props: {
    purchaseOrderId: {
      type: String,
    },
    operList: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  computed: {
  },
  data() {
    // todo: 流程控制
    return {
      purchasePrepareId: '',
    }
  },
  watch: {
    operList: {
      handler(newVal, oldVal) {
        console.log(newVal)
      },
      deep: true,
      immediate: true
    }
  },
 

  
  methods: {
    handleClick(item) {
      /**
       * 1、跳转列表
       * 2、跳转操作
       *      跳转添加
       *      跳转详情
       */
      /**
       * markFlag, 全部可操作，不做处理
       * markFlag
       *  1 可操作  添加
       *  2 不可操作(无记录)  禁用
       *  3 不可操作（有记录） 查看详情
       * 
       * 
       * 
       *  1,2,4：
       * 
       */
      let flag = (item.desc && item.updateTime)? 3 : 1
      // 添加
      if (item.markFlag == 1) {
        if(item.processType == 1 || item.processType == 2 || item.processType == 4) {
          uni.navigateTo({
            url: `${item.path}?processType=${item.processType}&purchaseOrderId=${this.purchaseOrderId}&markFlag=${item.markFlag}`,
          })
        } else {
          uni.navigateTo({
           url: `${item.path}?processType=${item.processType}&purchaseOrderId=${this.purchaseOrderId}&markFlag=${flag}`,
          })
        }
      } 
      // 编辑
      else if(item.markFlag==3) {
        if (item.processType == 1 || item.processType == 2 || item.processType == 4) {
          uni.navigateTo({
            url: `${item.path}?processType=${item.processType}&purchaseOrderId=${this.purchaseOrderId}&markFlag=${item.markFlag}`,
          })
        } else if(item.processType == 3) {
          // 检疫编辑
          uni.navigateTo({
            url: `/myPackge2/pages/catchingCows/quarantine?purchaseOrderId=${this.purchaseOrderId}&markFlag=${flag}`,
          })
        } else if(item.processType == 5) {
          // 检查编辑
          uni.navigateTo({
            url: `/myPackge2/pages/catchingCows/weighing?purchaseOrderId=${this.purchaseOrderId}&markFlag=${flag}`,
          })
        } else if(item.processType == 6) {
          // 整车编辑
          uni.navigateTo({
            url: `/myPackge2/pages/catchingCows/departure?purchaseOrderId=${this.purchaseOrderId}&markFlag=${flag}`,
          })
        }
      } 
    },
  },
}
</script>

<style lang="scss" scoped>
.box-top {
  width: 100%;
  min-height: 200rpx;
  box-sizing: border-box;
  display: flex;
  flex-direction: row;

  .left-box-top {
    width: 180rpx;
    text-align: center;
    color: rgba(198, 198, 198, 1);
    font-size: 20rpx;
  }

  .line {
    width: 4rpx;
    background-color: rgba(228, 231, 237, 1);
    margin: 0 20rpx 0 20rpx;

    .dot {
      width: 50rpx;
      height: 50rpx;
      background-color: rgba(228, 231, 237, 1);
      border-radius: 50%;
      position: relative;
      left: -25rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      color: #fff;
    }
  }

  .right-box-top {
    flex: 1;
    padding: 0 0 20rpx 30rpx;

    .time {
      font-size: 24rpx;
      color: rgb(107, 105, 105);
      margin-top: 10rpx;
    }
  }
}

//激活元素
.active {
  background-color: #08BA7E !important;
}

.notactive {
  background-color: #F0F0F0 !important;
  color: #999999 !important;
}

.disabled {
  background-color: #B0E6D9 !important;
  // color: #B0E6D9 !important;
}

.active-name {
  color: #08BA7E !important;
}

.notactive-name {
  color: #999999 !important;
}

.disabled-name {
  color: #B0E6D9 !important;
}

// 隐藏元素
.none {
  background-color: rgba(0, 0, 0, 0) !important;
}</style>
