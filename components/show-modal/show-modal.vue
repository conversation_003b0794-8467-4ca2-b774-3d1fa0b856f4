<template>
  <view class="_showModal" v-show="show">
    <view class="_shade"></view>
    <view class="_modalBox modalMove" :class="[move]" @click="closeModal" @touchmove.stop.prevent="">

      <view class="_modal">
        <slot name="title">
          <view class="title" v-show="title">{{title}}</view>
          <image v-if="modalImg()!=''" :src="modalImg()" class="modalImg"></image>
        </slot>
        <view v-if="closeTime" class='closeTime'>{{closeTime}}秒后自动关闭</view>
        <slot name="content">
          <view class="content">
            {{content}}
            <view class="contentOther" v-if="contentOther!=''">{{contentOther}}</view>
          </view>
        </slot>
        <slot name="btn">
          <view class="btnbox">
            <view class="cancel btn" v-show="showCancel" :style="cancelColor" @click.stop="clickBtn('cancel')">{{cancelText}}</view>
            <view class="confirm btn" :style="confirmColor" @click.stop="clickBtn('confirm')">{{confirmText}}</view>
          </view>
        </slot>
      </view>
    </view>
  </view>
</template>

<script>
  var timer = null;
  export default {
    data() {
      return {
        modalIcon: "",
        closeTime: "",
        move: ""
      };
    },
    created() {
      this.modalIcon = this.$modalStore.state.modalIcon;
    },
    watch: {
      show(val) {
        let offTiem = this.$modalStore.state.closeTime;
        if (val && offTiem != '') {
          this.closeTime = offTiem;
          this.downTime(offTiem);
        }
      }
    },
    computed: {
      //图标
      modalImg() {
        return function() {
          let value = this.$modalStore.state.modalIcon;
          if (value === "succese") {
            return "/static/modalImg/succese.png"
          };
          if (value === "ask") {
            return "/static/modalImg/ask.png"
          };
          if (value === "worn") {
            return "/static/modalImg/worn.png"
          }
		  if (value === "error") {
		    return "/static/modalImg/error.png"
		  }
          if (value === "" || value == "none") {
            return ''
          }
        }
      },
      //弹窗显示隐藏
      show() {
        return this.$modalStore.state.show;
      },
      //标题
      title() {
        return this.$modalStore.state.title;
      },
      //内容
      content() {
        return this.$modalStore.state.content;
      },
      //内容的其他文本
      contentOther() {
        return this.$modalStore.state.contentOther;
      },
      //取消按钮
      showCancel() {
        return this.$modalStore.state.showCancel;
      },
      //取消按钮文字
      cancelText() {
        return this.$modalStore.state.cancelText;
      },
      //取消按钮颜色
      cancelColor() {
        return "color:" + this.$modalStore.state.cancelColor;
      },
      //确认按钮文字
      confirmText() {
        return this.$modalStore.state.confirmText;
      },
      //确认按钮颜色
      confirmColor() {
        return "color:" + this.$modalStore.state.confirmColor;
      }
    },
    methods: {
      closeModal() {
        if (this.$modalStore.state.autoClose) return
        this.$modalStore.commit('hideModal')
      },
      clickBtn(res) {
        this.move = 'modalMoveOff';
        this.$nextTick(() => {
          setTimeout(() => {
            this.move = '';
            this.$modalStore.state.cancelText = "取消";
            this.$modalStore.state.confirmText = "确定";
            this.$modalStore.state.content = "";
            this.$modalStore.state.title = "";
            this.$modalStore.state.contentOther = "";
            this.$modalStore.commit('hideModal')
            this.$modalStore.commit('success', res)
          }, 150)
        })
      },
      downTime(offTiem) {
        timer = setInterval(() => {
          offTiem--;
          if (offTiem <= 0) {
            clearInterval(timer);
            this.$modalStore.commit('hideModal');
            this.closeTime = this.$modalStore.state.closeTime;
          } else {
            this.closeTime = offTiem;
          }
        }, 1000);
      }
    },
    // vue实例销毁时执行
    beforeDestroy() {
      clearInterval(timer);
      this.$modalStore.commit('hideModal')
    }
  };
</script>

<style scoped>
  .modalImg {
    width: 85rpx;
    height: 85rpx;
    display: block;
    margin: 0 auto;
    margin-top: 10rpx;
  }

  .closeTime {
    padding: 20rpx 0;
    color: #888888;
    text-align: center;
    font-size: 28rpx;
  }

  ._showModal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 10077;
  }

  ._showModal .contentOther {
    color: red;
    font-size: 28rpx;
    padding-top: 30rpx;
    border-top: 1rpx solid #D1D1D1;
    margin-top: 30rpx;
    line-height: 40rpx;
  }

  ._showModal ._shade {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    background: #000;
    opacity: 0.3;
    z-index: 11000;
  }

  @keyframes fadeio {
    0% {
      opacity: 0.1;
      transform: scale(0.5);
    }

    100% {
      opacity: 1;
      transform: scale(1);
    }
  }

  @keyframes fadeio2 {
    0% {
      opacity: 1;
      transform: scale(1);
    }

    100% {
      opacity: 0.1;
      transform: scale(0.5);
    }
  }

  ._showModal .modalMove {
    animation: fadeio 0.2s linear;
    animation-fill-mode: forwards;
  }

  ._showModal .modalMoveOff {
    animation: fadeio2 0.2s linear;
    animation-fill-mode: forwards;
  }

  ._showModal ._modalBox {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 12000;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  ._showModal ._modalBox ._modal {
    flex: none;
    width: 85%;
    background: #fff;
    border-radius: 16rpx;
    padding-top: 30rpx;
  }

  ._showModal ._modalBox ._modal .title {
    height: 40rpx;
    line-height: 40rpx;
    text-align: center;
    font-size: 32rpx;
    font-weight: bold;
    padding: 15rpx 0 0;
  }

  ._showModal ._modalBox ._modal .content {
    padding: 35rpx 30rpx;
    font-size: 28rpx;
    color: #333333;
    line-height: 50rpx;
    text-align: center;
    word-wrap: break-word;
    word-break: normal;
  }

  ._showModal ._modalBox ._modal .btnbox {
    display: flex;
  }

  ._showModal ._modalBox ._modal .btnbox .btn {
    font-size: 32rpx;
    text-align: center;
    flex: auto;
    line-height: 85rpx;
    border-top: 1rpx solid #e1e1e1;
    border-right: 1rpx solid #e1e1e1;
	padding: 10rpx 0;
  }

  ._showModal ._modalBox ._modal .btnbox .btn:last-child {
    border-right: none;
  }
</style>
