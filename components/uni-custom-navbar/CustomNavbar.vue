<template>
  <view class="custom-navbar" >
    <!-- 状态栏占位 -->
    <view class="status-bar" :style="statusBarStyle"></view>
    
    <!-- 导航栏内容 -->
    <view class="navbar-content" :style="contentStyle">
      <!-- 返回按钮 -->
      <view v-if="showBack" class="nav-left" @click="navBack">
        <u-icon size="36" name="arrow-left" color="#000"></u-icon>
      </view>
      
      <!-- 标题 -->
      <view class="nav-title" :style="titleStyle">{{ title }}</view>
      
      <!-- 右侧操作区 -->
      <view class="nav-right" v-if="rightContent">
        <slot name="right"></slot>
      </view>
    </view>
  </view>
    <!-- 使用示例 -->
    <!-- 使用自定义导航栏 -->
    <!-- <CustomNavbar 
      :title="pageTitle" 
      :bgColor="'#007AFF'" 
      :titleColor="'#FFFFFF'"
      :rightContent="true"
    >-->
      <!-- 右侧内容插槽 -->
      <!-- <template #right>
        <view class="right-btn" @click="handleRightClick">
          <text style="color: #FFFFFF">更多</text>
        </view>
      </template>
    </CustomNavbar> --> 
</template>

<script>
export default {
  name: 'CustomNavbar',
  props: {
    // 是否显示返回按钮
    showBack: {
      type: Boolean,
      default: true
    },
    // 导航栏标题
    title: {
      type: String,
      default: ''
    },
    // 导航栏背景颜色
    bgColor: {
      type: String,
      default: '#ffffff'
    },
    // 标题颜色
    titleColor: {
      type: String,
      default: '#000000'
    },
    // 是否显示右侧内容
    rightContent: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // 设备信息
      systemInfo: uni.getSystemInfoSync(),
      // 状态栏高度
      statusBarHeight: 0,
      // 导航栏高度
      navbarHeight: 44
    }
  },
  computed: {
    // 导航栏整体样式
    navbarStyle() {
      return `background-color: ${this.bgColor};`;
    },
    // 状态栏样式
    statusBarStyle() {
      return `height: ${this.statusBarHeight}px;`;
    },
    // 导航栏内容样式
    contentStyle() {
      return `height: ${this.navbarHeight}px; line-height: ${this.navbarHeight}px;`;
    },
    // 标题样式
    titleStyle() {
      return `color: ${this.titleColor};`;
    }
  },
  created() {
    // 获取设备状态栏高度
    this.statusBarHeight = this.systemInfo.statusBarHeight;
  },
  methods: {
    // 返回上一页
    navBack() {
      uni.navigateBack({
        delta: 1
      });
    }
  }
}
</script>

<style scoped>
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 999;
  width: 100%;
}

.status-bar {
  width: 100%;
}

.navbar-content {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 0 15px;
  box-sizing: border-box;
}

.nav-left {
  width: 40px;
  height: 100%;
  display: flex;
  align-items: center;
}

.back-icon {
  width: 20px;
  height: 20px;
}

.nav-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 500;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.nav-right {
  width: 40px;
  height: 100%;
  text-align: right;
}
</style>  