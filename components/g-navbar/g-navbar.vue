<template>
	<view>
		<uni-nav-bar statusBar='true' :height="statusBarHeight" right-text="退出" fixed='true' :title="title"   @clickRight='escLogin'/>
	</view>
</template>

<script>
	export default {
		name:"g-navbar",
		props: {
			
			title: {
				type: String,
				default: ''
			}
		},
		data() {
			return {
				statusBarHeight:'44px'
			};
		},
		created() {
			this.statusBarHeight = uni.getSystemInfoSync().statusBarHeight + 'px'
		},
		methods:{
			escLogin() {
				console.log(11)
				try{
					this.$showModal({
						content: "您确定要退出登录吗？",
						modalIcon: "ask",
						showCancel: true,
						confirmText: "确定",
						cancelText: "取消",
						success: (res) => {
							console.log(333)
							if (res.confirm) {
								// uni.$u.toast('退出登录成功');
								this.$store.commit("user/REMOVE_USER");
								setTimeout(() => {
									uni.reLaunch({
										url: '/pages/basics/account/login/login',
										// type: 'reLaunch'
									})
								}, 1000)
							} else {
								
							}
						}
					})
				}catch(e){
					console.log(e)
				}
				
			},
		}
	}
</script>

<style  lang="scss" scoped>

</style>