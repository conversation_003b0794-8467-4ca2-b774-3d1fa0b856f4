<template>
  <view>
    <view class="searchs">
      <view class="search">
        <u-search v-model="searchText" @search="handleSearch" :placeholder="placeholder"
          bg-color="rgba(255, 255, 255, 0.95)"
          :show-action="false"
          search-icon-color="#839F93;"
          input-style="color: #A5B2AC; font-size: 28rpx;"
          placeholder-color="rgba(102, 102, 102, 0.8)"
          border-color="transparent"
          :height="70">
        </u-search>
      </view>
    </view>
    <view class="regulatory-area">
      <view class="time-view">
        <view class="start-time" :style="startTime.title === '开始时间' ? 'color:#999' : 'color:#222'"
          @click="handleShowTime('startTime')">
          <view class="time-box">
            {{ startTime.title }}
          </view>
        </view>
        <view class="line">-</view>
        <view class="start-time" :style="endTime.title === '结束时间' ? 'color:#999' : 'color:#222'"
          @click="handleShowTime('endTime')">
          <view class="time-box">
            {{ endTime.title }}
          </view>
        </view>
      </view>
      <view class="reset-btn" @click.stop="handleReset">
        重置
      </view>
    </view>
    <!-- u-picker :safe-area-inset-bottom="true" v-model="showTime" mode="time" @confirm="submitTime"></u-picker> -->
	<u-calendar range-bg-color="rgba(64,202,143,0.13)" active-bg-color="#40CA8F"  range-color="#40CA8F" btn-type="success" v-model="showData" :mode="`range`" @change='changeData'></u-calendar>
  </view>
</template>

<script>
export default {
  props: {
    placeholder: {
      type: String,
      default: '请输入客户名称'
    }
  },
  data() {
    return {
      searchText: '',
      showData: false, //时间弹出选择框
      endTime: {
        title: '结束时间',
        timestamp: 0
      },
      startTime: {
        title: '开始时间',
        timestamp: 0
      },
      typeTime: '',
      form: {
        startTime: "",
        endTime: "",
      }
    };
  },
  methods: {
    handleSearch() {
      console.log('handleSearch');
      this.$emit('search', {
        customerName: this.searchText,
        startTime: this.form.startTime,
        endTime: this.form.endTime
      });
    },
    submitTime(val) {
      const arr = ['startTime', 'endTime']
      arr.map((item, index) => {
        if (item === this.typeTime) {
          this[item].timestamp = val.timestamp
          if (index === 0 || index === 1) {
            if (this.startTime.timestamp > this.endTime.timestamp && this.endTime.timestamp !== 0) {
              uni.showToast({
                title: '结束时间不能小于开始时间',
                icon: 'none'
              })
              return false
            }
          }
          this[item].title = val.year + '-' + val.month + '-' + val.day
          this.form[item] = val.year + '-' + val.month + '-' + val.day
          
          // Emit search event with updated values
          console.log('this.form', this.form);
          if(this.form.startTime && this.form.endTime) {
            this.handleSearch();
          }
        }
      })
    },
    handleShowTime(val) {
     this.showData = true;
	 /* if (this.startTime.timestamp === 0 && val === 'endTime') {
        uni.showToast({
          title: '请选择起始日期',
          icon: 'none'
        })
        return
      }
      this.typeTime = val
      this.showTime = true */
    },
	changeData(e){
		this.startTime.title = e.startDate;
		this.endTime.title = e.endDate;
		this.form.startTime = e.startDate;
		this.form.endTime = e.endDate;
		this.showData = false;
	},
    handleReset() {
      console.log('重置')
      this.searchText = ''
      this.startTime = {
        title: '开始时间',
        timestamp: 0
      }
      this.endTime = {
        title: '结束时间',
        timestamp: 0
      }
      this.form.startTime = ''
      this.form.endTime = ''
      this.handleSearch()
    }
  }
};
</script>

<style lang="scss" scoped>
.search {
  width: 100% !important;
  border-radius: 25rpx;
  overflow: hidden;
}

.searchs {
  background: transparent;
  padding: 0 30rpx !important;
}

.regulatory-area {
  padding: 30rpx;
  background-color: transparent;
  display: flex;
  justify-content: space-between;
  align-items: center;
  // margin-top: 33rpx;

  .regulatory-area-title {
    font-size: 28rpx;
    color: #333;
    font-weight: 400;
    padding-bottom: 24rpx;
    font-family: PingFang SC-Bold, PingFang SC;
  }

  .time-view {
    display: flex;
    justify-content: space-between;
    line-height: 60rpx;

    .start-time {
      width: 50%;
      text-align: center;
      color: #fff;
    }

    .time-box {
      width: 240rpx;
      border: 1px solid rgba(255, 255, 255, 0.6);
      border-radius: 25rpx;
      background-color: rgba(255, 255, 255, 1);
      backdrop-filter: blur(10rpx);
      font-size: 26rpx;
      font-weight: 500;
      color: #A5B2AC;
    }
    .line{
      margin: 0 20rpx;
      color: #333333;
    }
  }
  .reset-btn {
    text-align: right;
     background: linear-gradient( 305deg, #8FDB8C 0%, #1CC271 100%) !important;
      border-radius: 30rpx !important;
      width: 132rpx !important;
      height: 60rpx !important;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #FFFFFF;
      font-size: 26rpx;
      font-weight: bold;
      font-family: SourceHanSansSC, SourceHanSansSC;
    /deep/ .u-btn {
      background: linear-gradient( 305deg, #8FDB8C 0%, #1CC271 100%) !important;
      border-radius: 30rpx !important;
      width: 132rpx !important;
      height: 60rpx !important;
 /*      border: 1px solid rgba(255, 255, 255, 0.6) !important;
      color: #fff !important;
      border-radius: 20rpx !important;
      font-weight: 500 !important;
      backdrop-filter: blur(10rpx) !important; */
    }
  }
}
/deep/ .u-drawer{
  z-index: 99999999 !important;
}
:deep(.statis-search-popup) {
  z-index: 100000 !important;
}
</style>