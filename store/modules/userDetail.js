import { getUserInfo } from '@/api/account'
import { getToken, setToken, removeToken } from '@/common/utils/token.js'
import { getStorage, setStorage, removeStorage } from '@/common/utils/storage.js'

const userDetail = {
  state: {
    token: getToken(),
    avatar: '',
    name: '',
    roles: [],
    permissions: [],
    user: {},
  },
  mutations: {
    SET_NAME: (state, name) => {
      state.name = name
    },
    SET_AVATAR: (state, avatar) => {
      state.avatar = avatar
    },
    SET_ROLES: (state, roles) => {
      state.roles = roles
    },
    SET_PERMISSIONS: (state, permissions) => {
      state.permissions = permissions
    },
    SET_USERINFO: (state, user) => {
      state.user = user
    },
  },
  actions: {
    // 获取用户信息
    GetInfo({ dispatch, commit, state }) {
      return new Promise((resolve, reject) => {
        getUserInfo()
          .then((res) => {
            const user = res.user
            let avatar = user.avatar || ''
            if (res.roles && res.roles.length > 0) {  // 验证返回的roles是否是一个非空数组
              commit('SET_ROLES', res.roles)
              commit('SET_PERMISSIONS', res.roles)
            } else {
              commit('SET_ROLES', ['ROLE_DEFAULT'])
            }
            commit('SET_NAME', user.nickName || user.userName)
            commit('SET_AVATAR', avatar)
            commit('SET_USERINFO', user)
            resolve(res)
          })
          .catch((error) => {
            reject(error)
          })
      })
    },
  },
}

export default userDetail
