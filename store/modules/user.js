import { getUserInfo } from '@/api/account'
import { getToken, setToken, removeToken } from '@/common/utils/token.js'
import { getStorage, setStorage, removeStorage } from '@/common/utils/storage.js'
const loginPath = '/pages/account/login/login'
const getDefaultState = () => {
  return {
    token: getToken() || '',
    userInfo: {},
    tagList: [],
    tenantId: getStorage('tenantId') || '',
  }
}

const state = getDefaultState()

const mutations = {
  SET_TOKEN: (state, token) => {
    setToken(token)
    state.token = token
  },
  SET_TENANTID: (state, tenantId) => {
    setStorage('tenantId', tenantId)
    state.tenantId = tenantId
  },
  SET_USER: (state, userInfo) => {
    setStorage('userInfo', userInfo)
    state.userInfo = userInfo
  },
  REMOVE_USER: (state, userInfo) => {
    state.userInfo = {}
    state.token = ''
    removeToken()
    removeStorage('userInfo')
  },
  SET_TAGLIST: (state, tagList) => {
    state.tagList = tagList
  },
  REMOVETAGLIST: (state) => {
    state.tagList = []
}
}

const actions = {
  wxCode() {
    return new Promise((resolve, reject) => {
      uni.login({
        success: (loginRes) => {
          resolve(loginRes.code)
        },
        fail: (err) => {
          reject('获取code失败:' + err)
        },
      })
    })
  },

  

  //检查是否登录 toLogin未登陆是否自动跳转去登录
  cklogin({ state }, toLogin = true) {
    return new Promise((resolve, reject) => {
      if (state.token == '') {
        resolve(false)
        if (toLogin) {
          uni.showToast({
            title: '请先去登录哦',
            duration: 1500,
            mask: true,
            icon: 'loading',
          })
          setTimeout(() => {
            uni.navigateTo({
              url: loginPath,
            })
          }, 1500)
        }
        return
      }
      resolve(true)
    })
  },
  userInfo({commit}, data) {
      commit('SET_USER', data);
  },
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
}
