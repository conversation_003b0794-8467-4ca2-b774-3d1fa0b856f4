<template>
    <view>
      <section v-for="(item, index) in list" :key="index">
        <view class="list-section" @click="handleDetails(item)">
          <view class="middle">
            <view class="top-explain">
              <view class="title">{{ item.name }}</view>
              <view class="status">运输中</view>
            </view>
            <view class="num-explain explain">
              <p>运输信息：<text>{{item.phone}}</text></p>
              <p>预计抵达时间：<text>{{item.num}}</text></p>
              <p>验收人：<text>{{item.time}}</text></p>
              <p>活畜总数量：<text>{{item.time}}</text></p>
            </view>
            <view style="display: flex; justify-content: space-between;" v-if="showInfo">
              <view class="text">合同文件：</view>
              <view class="img-box">
                <img src="../../../static/icon-home/存货质押.png" alt="" />
                <img src="../../../static/icon-home/存货质押.png" alt="" />
                <img src="../../../static/icon-home/存货质押.png" alt="" />
                <img src="../../../static/icon-home/存货质押.png" alt="" />
              </view>
            </view>
            <view class="item" @click="libraryRecords(item)" v-if="showOper">
              <!-- <img class="icon" src="../icon/consumption.png" alt="" /> -->
              <text>周订单</text>
            </view>
          </view>
        </view>
      </section>
    </view>
  </template>
  
  <script>
  export default {
    props: ['list', 'showOper', 'showInfo', 'processType'],
    data() {
      return {}
    },
    methods: {
        handleDetails(item) {
            uni.navigateTo({
                url: `/myPackge2/pages/maintenance/maintenanceDetail`
            })
        }
    },
  }
  </script>
  <style lang="scss" scoped>
  @import '@/common/css/superviseHome.scss';
  .explain {
    font-size: 26rpx !important;
    display: block !important;
    color: #999999;
    width: 100%;
    font-family: PingFang SC-Medium;
    p {
      white-space: nowrap;
      margin-bottom: 25rpx;
    }
    text {
      color: #333;
    }
    .text {
      width: 160rpx;
    }
    .img-box {
      width: 100%;
      img {
        width: 85rpx;
        height: 85rpx;
        border-radius: 20rpx;
        margin-right: 20rpx;
      }
    }
  }
  .item {
    width: 100%;
    display: flex;
    justify-items: center;
    padding: 30rpx 0 0 0;
    text-align: center;
    border-top: 2rpx solid #f2f2f2;
    margin-top: 20rpx;
    .icon {
      width: 50rpx;
      height: 50rpx;
    }
  
    text {
      font-size: 28rpx;
      font-family: PingFang SC-Medium, PingFang SC;
      font-weight: 400;
      color: #40CA8F;
      padding-left: 10rpx;
      height: 50rpx;
      line-height: 50rpx;
    }
  
    &:nth-child(4n) {
      width: 17%;
    }
  }
  .middle{
    width: 100% !important;
  }
  .status{
    width: 100rpx;
    font-size: 28rpx;
    color: #999999;
  }
  </style>
  