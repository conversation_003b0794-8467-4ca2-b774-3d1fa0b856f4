<template>
    <view>
      <section v-for="(item, index) in list" :key="index">
        <view class="list-section" @click="handleDetails(item)">
          <view class="items">
            <view class="item_title">
              <view class="title">{{ item.farmersName }}</view>
              <view>
                <template v-if="processType==2">
                  <text class="status" :class="item.kongcaoStatus==1 ? 'tips': 'tips-text'">
                    {{item.kongcaoStatus==1?'已控槽': '待控槽'}}
                  </text>
                </template>
                <template v-if="processType==4">
                 <text  class="status" :class="item.catchStatus==0 || item.catchStatus==2? 'tips-text': 'tips'">
                  {{item.catchStatus==0? '待收购': item.catchStatus==1? '已签字': '待签字'}}
                 </text>
                </template>
              </view>
            </view>
            <view class="copy" @click.stop="handleCopy(item)" v-if="processType==4 && item.catchStatus!=0">
              复制链接 
            </view>
            <view class="item_content">
              <p>养殖户联系电话：<text class="text">{{item.farmersPhone}}</text></p>
              <p>活畜数量：<text class="text">{{item.chooseNumber}}</text></p>
              <p>甄选日期：<text class="text">{{item.updateTime}}</text></p>
            </view>
            <view style="display: flex; justify-content: space-between;" v-if="showInfo">
              <view class="text">合同文件：</view>
              <view class="img-box">
                <!--  -->
              </view>
            </view>
            <view class="item" @click="libraryRecords(item)" v-if="showOper">
              <!-- <img class="icon" src="../icon/consumption.png" alt="" /> -->
              <text>周订单</text>
            </view>
          </view>
        </view>
      </section>
    </view>
  </template>
  
  <script>
  export default {
    name: 'cellList',
    props: ['list', 'showOper', 'showInfo', 'processType', 'markFlag'],
    data() {
      return {}
    },
    methods: {
        handleDetails(item) {
          console.log(item)
            console.log(this.processType)
            /**
             * markFlag
             * 1: 添加
             * 2:
             * 3: 编辑
             */
            if(this.processType==1){
              // 甄选
                uni.navigateTo({
                url: `/myPackge2/pages/catchingCows/cowDetails?purchasePrepareId=${item.purchasePrepareId}&markFlag=${this.markFlag}`
             })
            } else if(this.processType==2) {
              // 控槽
              uni.navigateTo({
                  url: `/myPackge2/pages/catchingCows/controlSlot?purchasePrepareId=${item.purchasePrepareId}&kongcaoStatus=${item.kongcaoStatus}`,
                })
            } else if(this.processType==4) {
              uni.navigateTo({
                    url: `/myPackge2/pages/catchingCows/catchCows?purchasePrepareId=${item.purchasePrepareId}&purchaseOrderId=${item.purchaseOrderId}&catchStatus=${item.catchStatus}`
                 })
              }
          /*   // 控槽
            if(this.processType==2){
              // 已控槽
              if(item.kongcaoStatus==1) {
                uni.navigateTo({
                  url: `/myPackge2/pages/catchingCows/details/controlSlotDetail?purchasePrepareId=${item.purchasePrepareId}`,
                })
              } else {
                // 待控槽
                uni.navigateTo({
                  url: `/myPackge2/pages/catchingCows/controlSlot?purchasePrepareId=${item.purchasePrepareId}`,
                })
              }
            } else if(this.processType==3) {  // 检疫
                uni.navigateTo({
                    url: `/myPackge2/pages/catchingCows/quarantine?purchaseOrderId=${item.purchaseOrderId}`
             })
            } else if(this.processType==4){
              if(item.catchStatus==0) {
                uni.navigateTo({
                    url: `/myPackge2/pages/catchingCows/catchCows?purchasePrepareId=${item.purchasePrepareId}&purchaseOrderId=${item.purchaseOrderId}`
                 })
              } else {
                uni.navigateTo({
                    url: `/myPackge2/pages/catchingCows/details/catchCowsDetail?purchasePrepareId=${item.purchasePrepareId}&purchaseOrderId=${item.purchaseOrderId}`
                 })
              }
            } */
        },
        // 复制
        handleCopy(item) {
            uni.setClipboardData({
                data: item.signShortUrl,
                success: function () {
                    uni.showToast({
                        title: '复制成功',
                        icon: 'none'
                    })
                }
            })
        },
    },
  }
  </script>
  <style lang="scss" scoped>
  @import '@/common/css/listItem.scss';
  .item {
    width: 100%;
    display: flex;
    justify-items: center;
    padding: 30rpx 0 0 0;
    text-align: center;
    border-top: 2rpx solid #f2f2f2;
    margin-top: 20rpx;
    .icon {
      width: 50rpx;
      height: 50rpx;
    }
  
    text {
      font-size: 28rpx;
      font-family: PingFang SC-Medium, PingFang SC;
      font-weight: 400;
      color: #40CA8F;
      padding-left: 10rpx;
      height: 50rpx;
      line-height: 50rpx;
    }
  
    &:nth-child(4n) {
      width: 17%;
    }
  }
  .status{
    width: 100rpx;
    font-size: 28rpx;
    color: #999999;
  }
  .tips-text{
    color: #E8824E;
  }
  .tips {
    color: #37BA7E;
  }
  </style>
  