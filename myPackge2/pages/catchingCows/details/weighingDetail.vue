<template>
    <view class="main">
      <view class="setupbox">
        <scroll-view class="main" scroll-y :scroll-with-animation="true">
          <view class="container">
            <view class="title">检查详情</view>
            <view class="row">
              <view class="label">养殖户姓名</view>
              <view class="value">{{ form.farmersName }}</view>
            </view>
            <view class="row">
              <view class="label">身份证号</view>
              <view class="value">{{ form.farmersIdcard }}</view>
            </view>
            <view class="row">
              <view class="label">身份证正面照片</view>
            </view>
            <view class="photo" v-if="form.idcardFrontUrl">
              <view class="item-phone">
                <image @click="previewImage(form.idcardFrontUrl)" :src="form.idcardFrontUrl"></image>
              </view>
            </view>
            <view class="row">
              <view class="label">身份证反面照片</view>
            </view>
            <view class="photo" v-if="form.idcardBackUrl">
              <view class="item-phone">
                <image @click="previewImage(form.idcardBackUrl)" :src="form.idcardBackUrl"></image>
              </view>
            </view>
            <view class="row">
              <view class="label">养殖场地址</view>
              <view class="value">{{ form.farmersAddress }}</view>
            </view>
          </view>
        </scroll-view>
      </view>
    </view>
  </template>
  
  <script>
  import { prepareInfo } from '@/api/pages/catchingCows'
  export default {
    data() {
      return {
        form: {},
        labelStyle: {
          fontSize: '28rpx',
          fontFamily: 'PingFang SC, PingFang SC',
          color: '#666666',
        },
        purchasePrepareId: '', // 订单id
      };
    },
    onLoad(options) {
      this.purchasePrepareId = options.purchasePrepareId;
      this.getInfo(this.purchasePrepareId)
    },
    methods: {
      getInfo(purchasePrepareId) {
        prepareInfo({ purchasePrepareId }).then(res => {
          this.form = res.result
          console.log(this.form)
        })
      },
      // 切换 Tab
      changeTab(index) {
        this.currentTab = index;
      },
      // 预览图片
      previewImage(url) {
        uni.previewImage({
          urls: [url],
        });
      },
    },
  }
  </script>
  
  <style lang="scss" scoped>
  /deep/ .placeholderClass {
    text-align: right;
    color: #bbbbbb;
    font-size: 28rpx;
  }
  
  .container-footer {
    width: 100%;
    position: fixed;
    bottom: 0;
    background-color: white;
    color: white;
    z-index: 1000;
  
    /deep/ .u-btn {
      color: white;
      background-color: #40CA8F;
    }
  
    /deep/ .u-btn--default--disabled {
      background-color: #a5b5f5;
    }
  }
  
  .main {
    width: 100vw;
    height: 100vh;
    background-color: #f8f8ff;
  
    .uploadImage {
      display: flex;
      flex-wrap: wrap;
      position: relative;
      padding: 10rpx;
      padding-left: 0;
  
      .itemAlready {
        width: 140rpx;
        height: 140rpx;
        border-radius: 8rpx;
        position: relative;
        margin: 0 20rpx 10rpx 0rpx;
  
        image {
          width: 100%;
          height: 100%;
          border-radius: 8rpx;
        }
  
        video {
          width: 100%;
          height: 100%;
          border-radius: 8rpx;
        }
  
        .closeIcon {
          width: 32rpx;
          height: 32rpx;
          background-image: url('@/static/modalImg/error.png');
          position: absolute;
          background-size: cover;
          top: -10rpx;
          right: -10rpx;
        }
      }
  
      .item {
        width: 140rpx;
        height: 140rpx;
        border-radius: 8rpx;
        position: relative;
        border: 2rpx dashed #d8d8d8;
  
        .uploadIcon {
          width: 100%;
          height: 120rpx;
          display: flex;
          justify-content: center;
          align-items: flex-end;
          font-size: 24rpx;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #d8d8d8;
          background: url('https://fengtaohui.oss-cn-beijing.aliyuncs.com/uploads/image/20230105/6400955ab08a2f639e9790b815c418db63b666a9d7e40.png') no-repeat;
          background-size: 20rpx 20rpx;
          background-position: center 30rpx;
          position: absolute;
          top: 0;
          left: 0;
          bottom: 0;
          right: 0;
          margin: auto;
          z-index: 5;
        }
      }
    }
  
    .container-content {
      margin: 20rpx;
      margin-top: 0;
      background-color: #fff;
      border-radius: 15rpx 15rpx 15rpx 15rpx;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 43rpx 33rpx 36rpx 29rpx;
  
      &-left {
        &-title {
          color: #333;
          font-size: 32rpx;
          font-weight: 400;
          font-family: PingFang SC-Bold;
          padding-bottom: 21rpx;
        }
  
        &-time {
          font-weight: 400;
          color: #999;
          font-family: PingFang SC-Medium;
          font-size: 24rpx;
        }
  
        &-price {
          padding-top: 44rpx;
  
          text:first-child {
            font-weight: 400;
            color: #878d96;
            font-family: PingFang SC-Medium;
            font-size: 28rpx;
          }
  
          text:last-child {
            font-weight: 400;
            color: #333333;
            font-family: PingFang SC-Medium;
            font-size: 28rpx;
          }
        }
      }
    }
  }
  
  .OptionOrder {
    background: #ffffff;
    height: 120rpx;
    margin-bottom: 20rpx;
  
    ul {
      li {
        display: flex;
      }
    }
  }
  
  .info {
    font-size: 22rpx;
    font-family: PingFang SC-Medium, PingFang SC;
    font-weight: 400;
    color: #40CA8F;
    line-height: 26rpx;
  }
  
  .fill {
    margin-top: 18rpx;
    margin-bottom: 18rpx;
  }
  
  .oneInfo {
    color: #bbbbbb;
  }
  
  .twoInfo {
    color: #40CA8F;
  }
  
  /deep/.u-radio-group {
    display: flex;
    justify-content: space-between;
  }
  
  /deep/.u-form-left__content__label data-v-5e7216f1 {
    margin-left: 28rpx;
  }
  
  .titHtwo {
    display: flex;
    justify-content: space-around;
  }
  
  .buttons {
    width: 150rpx;
    height: 60rpx;
    border-radius: 30rpx;
    background-color: #f4f4f4;
    color: #999999;
    text-align: center;
    line-height: 60rpx;
  }
  
  .checked {
    width: 150rpx;
    height: 60rpx;
    border-radius: 30rpx;
    background-color: #f4f4f4;
    color: #5670fe;
    text-align: center;
    line-height: 60rpx;
    border: 1rpx solid #5670fe;
  }
  
  .infos {
    margin-bottom: 10rpx;
    margin-left: 5rpx;
    font-size: 32rpx;
    font-family: PingFang SC, PingFang SC;
    font-weight: normal;
    color: #222222;
  }
  
  .setupbox {
    background: #fff;
  }
  
  /deep/ .u-tab-item {
    width: 50% !important;
  }
  
  .pages {
    width: 100%;
    height: 100%;
    box-sizing: border-box;
  }
  
  .container {
    box-sizing: border-box;
    background: #fff;
    border-radius: 20rpx;
    padding: 0rpx 20rpx 20rpx;
    margin: 20rpx 20rpx 0;
  
    .title {
      font-size: 28rpx;
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      color: #222222;
      position: relative;
      padding-left: 16rpx;
      padding-top: 29rpx;
      margin: 0rpx 0 50rpx;
  
      &:before {
        content: '';
        width: 9rpx;
        height: 25rpx;
        background: #40CA8F;
        position: absolute;
        top: 35rpx;
        left: -19rpx;
      }
    }
  
    .row {
      display: flex;
      justify-content: space-between;
  
      padding: 0 19rpx 30rpx;
  
      .label {
        width: 230rpx;
        font-size: 28rpx;
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        color: #999999;
      }
  
      .value {
        width: 66%;
        font-size: 28rpx;
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        color: #222222;
        text-align: right;
        // display: flex;
        // align-items: center;
      }
  
      img {
        width: 40rpx;
        height: 40rpx;
        margin-left: 20rpx;
        position: relative;
        top: 8rpx;
      }
  
    }
  
  }
  
  em {
    font-size: 32rpx;
    font-family: DIN, DIN;
    font-weight: 700;
    color: #40CA8F;
    display: inline-block;
  }
  
  .photo {
    display: flex;
    padding-left: 20rpx;
  
    .item-phone {
      width: 130rpx;
      height: 130rpx;
      background: #D8D8D8;
      border-radius: 10rpx 10rpx 10rpx 10rpx;
      margin: 0 38rpx 30rpx 0;
      position: relative;
  
      image {
        width: 100%;
        height: 100%;
        border-radius: 10rpx 10rpx 10rpx 10rpx;
      }
    }
  
  }
  </style>