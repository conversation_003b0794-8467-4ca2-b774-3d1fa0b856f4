<template>
  <view class="main">
    <u-form ref="uForm" :model="form" :rules="rules" :error-type="errorType" label-width="auto" :label-style="labelStyle" class="form-box">
      <u-form-item label="承运人" :required="true" prop="driverName" >
        <u-input v-model="form.driverName" placeholder="承运人" disabled @click="showDriver = true" :custom-style="customStyle" placeholder-style="text-align:right;color:#999;font-size: 26rpx;" />
      </u-form-item>
      <u-form-item label="车牌号" :required="true" prop="licensePlateNumber">
        <u-input v-model="form.licensePlateNumber" placeholder="车牌号" :custom-style="customStyle" placeholder-style="text-align:right;color:#999;font-size: 26rpx;" />
      </u-form-item>
      <u-form-item label="车型">
        <u-input v-model="form.carType" placeholder="请输入车型" :custom-style="customStyle" placeholder-style="text-align:right;color:#999;font-size: 26rpx;" />
      </u-form-item>
      <u-form-item label="发车路线">
        <u-input v-model="form.startEndPlace" placeholder="发车路线" disabled :custom-style="customStyle" placeholder-style="text-align:right;color:#999;font-size: 26rpx;" />
      </u-form-item>
      <u-form-item label="身份证正面" :required="true" prop="idcardFrontUrl" :border-bottom="!form.idcardFrontUrl.length">
        <u-input placeholder=" " disabled />
        <img slot="right" style="width: 40rpx; height: 40rpx" @click="uploadFile('idcardFrontUrl', 1)" src="../../icon/photo.png" alt="" />
      </u-form-item>
      <view class="uploadImage" v-if="form.idcardFrontUrl.length">
        <view class="itemAlready" v-for="(item, index) in form.idcardFrontUrl" :key="index">
          <image mode="scaleToFill" :src="item" @click="previewImage(item)" />
          <view class="closeIcon" @click="deleteFile(index, 'idcardFrontUrl')"></view>
        </view>
      </view>
      <u-form-item label="身份证反面" :required="true" prop="idcardBackUrl" :border-bottom="!form.idcardBackUrl.length">
        <u-input placeholder=" " disabled />
        <img slot="right" style="width: 40rpx; height: 40rpx" @click="uploadFile('idcardBackUrl', 1)" src="../../icon/photo.png" alt="" />
      </u-form-item>
      <view class="uploadImage" v-if="form.idcardBackUrl.length">
        <view class="itemAlready" v-for="(item, index) in form.idcardBackUrl" :key="index">
          <image mode="scaleToFill" :src="item" @click="previewImage(item)" />
          <view class="closeIcon" @click="deleteFile(index, 'idcardBackUrl')"></view>
        </view>
      </view>
      <u-form-item label="驾驶证" :required="true" prop="driversLicenseUrl" :border-bottom="!form.driversLicenseUrl.length">
        <u-input placeholder=" " disabled />
        <img slot="right" style="width: 40rpx; height: 40rpx" @click="uploadFile('driversLicenseUrl', 1)" src="../../icon/photo.png" alt="" />
      </u-form-item>
      <view class="uploadImage" v-if="form.driversLicenseUrl.length">
        <view class="itemAlready" v-for="(item, index) in form.driversLicenseUrl" :key="index">
          <image mode="scaleToFill" :src="item" @click="previewImage(item)" />
          <view class="closeIcon" @click="deleteFile(index, 'driversLicenseUrl')"></view>
        </view>
      </view>
      <u-form-item label="行驶证" :required="true" prop="drivingLicenseUrl" :border-bottom="!form.drivingLicenseUrl.length">
        <u-input placeholder=" " disabled />
        <img slot="right" style="width: 40rpx; height: 40rpx" @click="uploadFile('drivingLicenseUrl', 1)" src="../../icon/photo.png" alt="" />
      </u-form-item>
      <view class="uploadImage" v-if="form.drivingLicenseUrl.length">
        <view class="itemAlready" v-for="(item, index) in form.drivingLicenseUrl" :key="index">
          <image mode="scaleToFill" :src="item" @click="previewImage(item)" />
          <view class="closeIcon" @click="deleteFile(index, 'drivingLicenseUrl')"></view>
        </view>
      </view>
      <u-form-item label="牧运通备案码" :required="true" prop="filingCode" :border-bottom="!form.filingCode.length">
        <u-input placeholder=" " disabled />
        <img slot="right" style="width: 40rpx; height: 40rpx" @click="uploadFile('filingCode', 1)" src="../../icon/photo.png" alt="" />
      </u-form-item>
      <view class="uploadImage" v-if="form.filingCode.length">
        <view class="itemAlready" v-for="(item, index) in form.filingCode" :key="index">
          <image mode="scaleToFill" :src="item" @click="previewImage(item)" />
          <view class="closeIcon" @click="deleteFile(index, 'filingCode')"></view>
        </view>
      </view>
      <u-form-item label="车头带牌照片" :required="true" prop="carHeadUrl" :border-bottom="!form.carHeadUrl.length">
        <u-input placeholder=" " disabled />
        <img slot="right" style="width: 40rpx; height: 40rpx" @click="uploadFile('carHeadUrl', 1)" src="../../icon/photo.png" alt="" />
      </u-form-item>
      <view class="uploadImage" v-if="form.carHeadUrl.length">
        <view class="itemAlready" v-for="(item, index) in form.carHeadUrl" :key="index">
          <image mode="scaleToFill" :src="item" @click="previewImage(item)" />
          <view class="closeIcon" @click="deleteFile(index, 'carHeadUrl')"></view>
        </view>
      </view>
      <u-form-item label="车尾带牌照片" :required="true" prop="carTailUrl" :border-bottom="!form.carTailUrl.length">
        <u-input placeholder=" " disabled />
        <img slot="right" style="width: 40rpx; height: 40rpx" @click="uploadFile('carTailUrl', 1)" src="../../icon/photo.png" alt="" />
      </u-form-item>
      <view class="uploadImage" v-if="form.carTailUrl.length">
        <view class="itemAlready" v-for="(item, index) in form.carTailUrl" :key="index">
          <image mode="scaleToFill" :src="item" @click="previewImage(item)" />
          <view class="closeIcon" @click="deleteFile(index, 'carTailUrl')"></view>
        </view>
      </view>
      <u-form-item label="车辆车身照片" :required="true" prop="carBodyUrl" :border-bottom="!form.carBodyUrl.length">
        <u-input placeholder=" " disabled />
        <img slot="right" style="width: 40rpx; height: 40rpx" @click="uploadFile('carBodyUrl', 1)" src="../../icon/photo.png" alt="" />
      </u-form-item>
      <view class="uploadImage" v-if="form.carBodyUrl.length">
        <view class="itemAlready" v-for="(item, index) in form.carBodyUrl" :key="index">
          <image mode="scaleToFill" :src="item" @click="previewImage(item)" />
          <view class="closeIcon" @click="deleteFile(index, 'carBodyUrl')"></view>
        </view>
      </view>
      <u-form-item label="地方性其他手续" prop="quarantineProcedures" :border-bottom="!form.quarantineProcedures.length">
        <u-input placeholder=" " disabled />
        <img slot="right" style="width: 40rpx; height: 40rpx" @click="uploadFile('quarantineProcedures', 3)" src="../../icon/photo.png" alt="" />
      </u-form-item>
      <view class="uploadImage" v-if="form.quarantineProcedures.length">
        <view class="itemAlready" v-for="(item, index) in form.quarantineProcedures" :key="index">
          <image mode="scaleToFill" :src="item" @click="previewImage(item)" />
          <view class="closeIcon" @click="deleteFile(index, 'quarantineProcedures')"></view>
        </view>
      </view>
      <u-form-item label="检疫合格证" :required="true" prop="quarantineCert" :border-bottom="!form.quarantineCert.length">
        <u-input placeholder=" " disabled />
        <img slot="right" style="width: 40rpx; height: 40rpx" @click="uploadFile('quarantineCert', 3)" src="../../icon/photo.png" alt="" />
      </u-form-item>
      <view class="uploadImage" v-if="form.quarantineCert.length">
        <view class="itemAlready" v-for="(item, index) in form.quarantineCert" :key="index">
          <image mode="scaleToFill" :src="item" @click="previewImage(item)" />
          <view class="closeIcon" @click="deleteFile(index, 'quarantineCert')"></view>
        </view>
      </view>
    </u-form>
    <view
      class="container-footer"
      :style="'padding:' + (isIphonex ? '40rpx 60rpx' : '15rpx 60rpx')"
      ref="containerFooter"
    >
      <u-button hover-class='none' shape="circle" @click="saveWeighingData">保存</u-button>
    </view>
    <u-popup v-model="showDriver" mode="bottom" @cancle="showDriver = false">
			<view class="popup-view">
				<view class="searchs">
					<view class="search">
						<u-search placeholder="请输入承运人名称查询" v-model="searchValue" @custom="search" @search="search"></u-search>
					</view>
				</view>
				<view class="picker-btn">
					<view class="left" @click="showDriver=false">取消</view>
					<view class="right" @click="selectDriver">确定</view>
				</view>
				<picker-view :indicator-style="indicatorStyle" :value="multiIndex" @change="pickerChange">
					<picker-view-column class="view-column">
						<view class="item" v-for="(item, index) in driverData" :key="index">{{ item.nickName }} ({{item.phonenumber}})</view>
					</picker-view-column>
				</picker-view>
			</view>
		</u-popup>
  </view>
</template>

<script>
import { uploadFiles } from '@/api/obsUpload/index'
import { check, purchaseGoInfo } from '@/api/pages/catchingCows'
import { purchaseOrderInfo } from '@/api/pages/purchaseOrder'
import { nmbUserList } from '@/api/pages/demandOrder'



export default {
  data() {
    return {
      isIphonex: getApp().globalData.systemInfo.isIphonex,
      form: {
        driverName: '',
        driverId: '',
        licensePlateNumber: '',
        carType: '',
        startEndPlace: '',
        idcardFrontUrl: [],
        idcardBackUrl: [],
        driversLicenseUrl: [],
        drivingLicenseUrl: [],
        filingCode: [],
        carHeadUrl: [],
        carTailUrl: [],
        carBodyUrl: [],
        quarantineProcedures: [],
        quarantineCert: []
      },
      errorType: ['message'],
      labelStyle: {
        fontSize: '26rpx',
        color: '#333',
      },
      rules: {
        driverName: [
          {
            required: true,
            message: '请输入承运人姓名',
            trigger: 'blur'
          }
        ],
        licensePlateNumber: [
          {
            required: true,
            message: '请输入车牌号',
            trigger: 'blur'
          }
        ],
        idcardFrontUrl: [
          {
            required: true,
            validator: (rule, value, callback) => {value?.length>0 ? callback() : callback(new Error('请上传身份证正面'))},
            message: '请上传身份证正面',
            trigger: 'change'
          },
        ],
        idcardBackUrl: [
          {
            required: true,
            validator: (rule, value, callback) => {value?.length>0 ? callback() : callback(new Error('请上传身份证反面'))},
            message: '请上传身份证反面',
            trigger: 'change'
          },
        ],
        driversLicenseUrl: [
          {
            required: true,
            validator: (rule, value, callback) => {value?.length>0 ? callback() : callback(new Error('请上传驾驶证'))},
            message: '请上传驾驶证',
            trigger: 'change'
          },
        ],
        drivingLicenseUrl: [
          {
            required: true,
            validator: (rule, value, callback) => {value?.length>0 ? callback() : callback(new Error('请上传行驶证'))},
            message: '请上传行驶证',
            trigger: 'change'
          },
        ],
        filingCode: [
          {
            required: true,
            validator: (rule, value, callback) => {value?.length>0 ? callback() : callback(new Error('请上传牧运通备案码'))},
            message: '请上传牧运通备案码',
            trigger: 'change'
          },
        ],
        carHeadUrl: [
          {
            required: true,
            validator: (rule, value, callback) => {value?.length>0 ? callback() : callback(new Error('请上传车头照片'))},
            message: '请上传车头照片',
            trigger: 'change'
          },
        ],
        carBodyUrl: [
          {
            required: true,
            validator: (rule, value, callback) => {value?.length>0 ? callback() : callback(new Error('请上传车辆车身照片'))},
            message: '请上传车辆车身照片',
            trigger: 'change'
          },
        ],
        carTailUrl: [
          {
            required: true,
            validator: (rule, value, callback) => {value?.length>0 ? callback() : callback(new Error('请上传车尾照片'))},
            message: '请上传车尾照片',
            trigger: 'change'
          },
        ],
        /* quarantineProcedures: [
          {
            validator: (rule, value, callback) => {value?.length>0 ? callback() : callback(new Error('请上传地方性其他手续'))},
            message: '请上传地方性其他手续',
            trigger: 'change'
          }
        ], */
        quarantineCert: [
          {
            required: true,
            validator: (rule, value, callback) => {value?.length>0 ? callback() : callback(new Error('请上传检疫合格证'))},
            message: '请上传检疫合格证',
            trigger: 'change'
          }
        ]
      },
      customStyle: {
        textAlign: "right",
      },
      purchaseOrderId: '',
      markFlag: '',
      showDriver: false,
      searchValue: '',
			driverData: [],
      indicatorStyle: `height: ${Math.round(uni.getSystemInfoSync().screenWidth / (750 / 100))}px;`,
      multiIndex: [0],
    }
  },
  mounted() {
  },
  onReady() {
    this.$refs.uForm.setRules(this.rules)
  },
  onLoad(options) {
    this.purchaseOrderId = options.purchaseOrderId
    console.log(this.purchaseOrderId)
    this.markFlag = options.markFlag
    if(this.markFlag==3){
      this.getDetailInfo(this.purchaseOrderId)
    }
    this.getPurchaseOrderInfo()
  },
  methods: {
    uploadFile(type, maxCount) {
      if (this.form[type].length >= maxCount) {
        uni.showToast({
          icon: 'none',
          title: `最多只能上传${maxCount}个`,
        });
        return;
      }
      const that = this
      uni.chooseImage({
        count: 1,
        sizeType: ['original', 'compressed'],
        sourceType: ['album', 'camera'],
        name: 'file',
        success: function (res) {
          uploadFiles({
            filePath: res.tempFilePaths[0],
          }).then((data) => {
            that.form[type].push(data)
            that.$nextTick(() => {
              that.$refs.uForm.validate(type) // 新增校验触发
            })
          })
        },
        fail(e) {},
      })
    },
    previewImage(url) {
      uni.previewImage({
        urls: [url],
      })
    },
    deleteFile(index, type) {
      this.form[type].splice(index, 1)
      this.$nextTick(() => {
        this.$refs.uForm.validate(type) // 新增校验触发
      })
      this.$forceUpdate()
    },
    getPurchaseOrderInfo(){
      purchaseOrderInfo({purchaseOrderId: this.purchaseOrderId}).then(res => {
        if(res.code==200){
          this.form.driverName = res.result.driverName
          this.form.driverId = res.result.driverId
          this.form.licensePlateNumber = res.result.licensePlateNumber
          this.form.startEndPlace = res.result.startEndPlace
        }
      })
    },
    saveWeighingData() {
      const {carType, idcardFrontUrl, idcardBackUrl, driversLicenseUrl, drivingLicenseUrl, filingCode, carHeadUrl, carTailUrl, carBodyUrl, quarantineProcedures, quarantineCert}= this.form
      this.$refs.uForm.validate((valid) => {
        if (valid) {
          const params = {
            driverName: this.form.driverName,
            driverId: this.form.driverId,
            licensePlateNumber: this.form.licensePlateNumber,
            purchaseOrderId: this.purchaseOrderId,
            carType,
            idcardFrontUrl: idcardFrontUrl.join(','),
            idcardBackUrl: idcardBackUrl.join(','),
            driversLicenseUrl: driversLicenseUrl.join(','),
            drivingLicenseUrl: drivingLicenseUrl.join(','),
            filingCode: filingCode.join(','),
            carHeadUrl: carHeadUrl.join(','),
            carTailUrl: carTailUrl.join(','),
            carBodyUrl: carBodyUrl.join(','),
            quarantineProcedures: quarantineProcedures.join(','),
            quarantineCert: quarantineCert.join(','),
        }
        console.log(params)
          check(params).then((res) => {
            if(res.code==200) {
              uni.$emit('updateFlow')
              this.$toast('保存成功')
              uni.navigateBack({
                delta: 1,
              })
            }
          })
        } else {
          console.log('error submit!!');
          return false;
        }
      })
    },
    getDetailInfo(purchaseOrderId){
      purchaseGoInfo({purchaseOrderId}).then(res => {
        const {driverName,driverId, licensePlateNumber, carType, startEndPlace, idcardFrontUrl, idcardBackUrl, driversLicenseUrl, drivingLicenseUrl, filingCode, carHeadUrl, carTailUrl, carBodyUrl, quarantineProcedures, quarantineCert} = JSON.parse(JSON.stringify(res.result))
        this.form = {
          driverName,
          driverId,
          licensePlateNumber,
          carType,
          startEndPlace,
          idcardFrontUrl: idcardFrontUrl?.split(','),
          idcardBackUrl: idcardBackUrl?.split(','),
          driversLicenseUrl: driversLicenseUrl?.split(','),
          drivingLicenseUrl: drivingLicenseUrl?.split(','),
          filingCode: filingCode?.split(','),
          carHeadUrl: carHeadUrl?.split(','),
          carTailUrl: carTailUrl?.split(','),
          carBodyUrl: carBodyUrl?.split(','),
          quarantineProcedures: quarantineProcedures?.split(','),
          quarantineCert: quarantineCert?.split(','),
        }
      })
    },
    selectDriver() {
				const currentItem = this.driverData[this.multiIndex[0]]
        console.log(currentItem)
				this.form.driverName = currentItem.nickName
				this.form.driverId = currentItem.nmbUserId
				this.showDriver = false
			},
      search(val) {
				this.getUserList('company_nmb_driver', val) // 司机
			},
      pickerChange(e) {
				this.multiIndex = e.detail.value
			},
      getUserList(roleKey, nickName) {
        nmbUserList({
            pageNum: 1,
            pageSize: 200,
            roleKey,
            nickName
        }).then(res => {
          this.driverData = res.result.list.map(item => {
            item.label = `${item.nickName} (${item.phonenumber})`
            return item
					})
        })
      },
  }
}
</script>

<style lang="scss" scoped>
@import "../catchingCows/form.scss";
.container-footer {
  width: 100%;
  position: fixed;
  bottom: 0;
  background-color: white;
  color: white;
  z-index: 1000;
  padding: 15rpx 60rpx;
  /deep/ .u-btn {
    color: white;
    background-color: #40CA8F;
  }
}
.form-box{
  overflow: auto !important;
  padding-bottom: 150rpx !important;
}
.uploadImage{
  display: flex !important;
  flex-wrap: wrap !important;
}
.searchs{
		display: flex;
		justify-content: space-between;
		padding: 20rpx;
		.search{
			flex: 1;
		}
		.fifter{
			width: 100rpx;
			height: 60rpx;
			background-color: #40CA8F;
			color: white;
			text-align: center;
			line-height: 60rpx;
			border-radius: 30rpx;
		}
	}
	.popup-view{
		min-height: 600rpx;
		padding-top: 20rpx;
	}
  .item {
		line-height: 100rpx;
		text-align: center;
	}
  .popup-view {
		.picker-btn {
			display: flex;
			justify-content: space-between;
			padding: 30rpx 40rpx;

			.left {
				color: #999;
				font-size: 28rpx;
				font-family: PingFang SC-Medium;
			}

			.middle {
				font-size: 32rpx;
				color: #222;
				font-family: PingFang SC-Heavy;
			}

			.right {
				color: #40CA8F;
				font-size: 32rpx;
				font-family: PingFang SC-Medium;
			}
		}
	}
  .view-column{
    height: 400rpx;
  }
</style> 