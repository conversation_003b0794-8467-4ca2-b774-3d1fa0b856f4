<template>
  <view class="content">
    <SlotSteps :operList="operList" :purchaseOrderId="purchaseOrderId"></SlotSteps>
  </view>
</template>
<script>
import { purchaseOrderFlow } from '@/api/pages/purchaseOrder'
import SlotSteps from '@/components/slotSteps/SlotSteps.vue'
export default {
  components: {
    SlotSteps,
  },
  data() {
    return {
      purchaseOrderId: '',
      operList: [
        {
          id: 0,
          name: '甄选',
          path: '/myPackge2/pages/catchingCows/checkCows/index',
          processType: 1
        },
        {
          id: 1,
          name: '控槽',
          path: '/myPackge2/pages/catchingCows/checkCows/index',
          processType: 2
        },
        {
          id: 2,
          name: '检疫',
          path: '/myPackge2/pages/catchingCows/quarantine',
          processType: 3
        },
        {
          id: 3,
          name: '收购',
          path: '/myPackge2/pages/catchingCows/checkCows/index',
          processType: 4
        },
        {
          id: 4,
          name: '检查',
          path: '/myPackge2/pages/catchingCows/weighing',
          processType: 5
        },
        {
          id: 5,
          name: '整车',
          path: '/myPackge2/pages/catchingCows/departure',
          processType: 6
        },
      ],
    }
  },
  onLoad(options) {
    this.purchaseOrderId = options.purchaseOrderId
    console.log('purchaseOrderId', this.purchaseOrderId)
    this.getPurchaseOrderFlow()
    uni.$on('updateFlow', () => {
      this.getPurchaseOrderFlow()
    })
  },
  onShow(){
    this.getPurchaseOrderFlow()
  },
  onUnload() {
    uni.$off('updateFlow');
  },
  methods: {
    getPurchaseOrderFlow() {
      purchaseOrderFlow({ purchaseOrderId: this.purchaseOrderId }).then((res) => {
        if (res.code === 200) {
          const backendData = res.result;
          this.operList = this.operList.map(operItem => {
            const matchedBackendItem = backendData.find(backendItem => backendItem.id === operItem.id);
            return matchedBackendItem ? { ...operItem, ...matchedBackendItem } : operItem;
          });
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.content {
  padding: 80rpx 30rpx 30rpx 30rpx;
  display: flex;
  align-items: center;
}
</style>
