<template>
  <view class="main">
    <u-form ref="catchForm" :model="form" :rules="rules" label-width="auto" :label-style="labelStyle" class="form-box">
      <view class="form-section">
        <text class="form-title">牛源信息</text>
        <u-form-item prop="varietiesName" label="活畜品种" :required="true">
          <u-input v-model="form.varietiesName" placeholder="请输入活畜品种" disabled :custom-style="customStyle" placeholder-style="text-align:right;color:#999;font-size: 26rpx;" />
        </u-form-item>
        <u-form-item prop="categoryName" label="品种分类" :required="true">
          <u-input v-model="form.categoryName" placeholder="请输入品种分类" disabled :custom-style="customStyle" placeholder-style="text-align:right;color:#999;font-size: 26rpx;" />
        </u-form-item>
        <u-form-item prop="ageRange" label="活畜月龄" :required="true">
          <u-input v-model="form.ageRange" placeholder="请输入活畜月龄" disabled :custom-style="customStyle" placeholder-style="text-align:right;color:#999;font-size: 26rpx;" />
        </u-form-item>
        <u-form-item prop="weightRange" label="活畜重量" :required="true">
          <u-input v-model="form.weightRange" placeholder="请输入活畜重量" disabled :custom-style="customStyle" placeholder-style="text-align:right;color:#999;font-size: 26rpx;" />
        </u-form-item>
        <u-form-item prop="catchNumber" label="活畜头数" :required="true">
          <u-input v-model="form.catchNumber" placeholder="请输入活畜头数" :disabled="catchStatus!=0" :custom-style="customStyle" placeholder-style="text-align:right;color:#999;font-size: 26rpx;" />
        </u-form-item>
        <u-form-item prop="earTagList" label="耳标编号" right-icon="arrow-right"  v-if="catchStatus==0" >
          <u-input v-model="form.earTagList" :disabled="catchStatus!=0" placeholder="请输入耳标编号" :custom-style="customStyle" placeholder-style="text-align:right;color:#999;font-size: 26rpx;" @click="handleEartagNo" />
        </u-form-item>
        <u-form-item prop="earTagNumber" label="检疫耳标打数量（头）" :required="true">
          <u-input v-model="form.earTagNumber" :disabled="catchStatus!=0" placeholder="请输入检疫耳标打数量" :custom-style="customStyle" placeholder-style="text-align:right;color:#999;font-size: 26rpx;" />
        </u-form-item>
        <u-form-item prop="bindEarTagVideo" label="戴耳标照片" :custom-style="customStyle" :border-bottom="!form.bindEarTagVideo.length" :required="true">
          <u-input placeholder=" " disabled />
          <img slot="right" style="width: 40rpx; height: 40rpx" @click="uploadImage('bindEarTagVideo', 3)" src="../../icon/photo.png" alt="" />
        </u-form-item>
        <view class="uploadImage" v-if="form.bindEarTagVideo.length">
          <view class="itemAlready" v-for="(item, index) in form.bindEarTagVideo" :key="index">
            <image mode="scaleToFill" :src="item" @click="previewImage(item)" />
            <view class="closeIcon" v-if="catchStatus==0" @click="deleteImage(index, 'bindEarTagVideo')"></view>
          </view>
        </view>
      </view>

      <view class="form-section">
        <text class="form-title">交易信息</text>
        <u-form-item prop="unitPrice" label="单价（元/kg）" :required="true">
          <u-input v-model="form.unitPrice" placeholder="请输入" :disabled="catchStatus!=0" :custom-style="customStyle"  placeholder-style="text-align:right;color:#999;font-size: 26rpx;" />
        </u-form-item>
        <u-form-item prop="tareWeight" label="空车重量（kg）" :required="true">
          <u-input v-model="form.tareWeight" placeholder="请输入" :disabled="catchStatus!=0" :custom-style="customStyle"  placeholder-style="text-align:right;color:#999;font-size: 26rpx;" />
        </u-form-item>
        <u-form-item prop="grossWeight" label="满车重量（kg）" :required="true">
          <u-input v-model="form.grossWeight" placeholder="请输入" :disabled="catchStatus!=0" :custom-style="customStyle"  placeholder-style="text-align:right;color:#999;font-size: 26rpx;" />
        </u-form-item>
        <u-form-item prop="netWeight" label="净重（kg）" :required="true">
          <u-input v-model="form.netWeight" placeholder="自动计算：满车重量-空车重量" disabled :custom-style="customStyle"  placeholder-style="text-align:right;color:#999;font-size: 26rpx;" />
        </u-form-item>
        <u-form-item prop="totalAmount" label="交易总价格（元）" :required="true">
          <u-input v-model="form.totalAmount" placeholder="自动计算，单价*净重，可修改" :disabled="catchStatus!=0" :custom-style="customStyle"  placeholder-style="text-align:right;color:#999;font-size: 26rpx;" />
        </u-form-item>
        <u-form-item prop="contractUrl" label="合同附件(PDF格式)" :custom-style="customStyle"  :border-bottom="!form.contractUrl.length" :required="true">
          <u-input placeholder=" " disabled />
          <img slot="right" style="width: 40rpx; height: 40rpx"  @click="uploadPdf('contractUrl', 1)" src="../../icon/pdf.png" alt="" />
        </u-form-item>
        <view class="uploadImage" v-if="form.contractUrl.length">
          <view class="itemAlready pdf-box" v-for="(item, index) in form.contractUrl" :key="index" @click="previewFiles(item)">
            {{ contractUrlName }}
            <view class="closeIcon close-file" v-if="catchStatus==0" @click="deleteImage(index, 'contractUrl')"></view>
          </view>
        </view>
        <u-form-item
          prop="tareVideo"
          label="空车过磅视频"
          :custom-style="customStyle"
          :border-bottom="!form.tareVideo.length"
          :required="true"
        >
          <u-input placeholder=" " disabled />
          <img
            slot="right"
            style="width: 40rpx; height: 40rpx"
            @click="uploadVideo('tareVideo', 1)"
            src="../../icon/uploadVideo.png"
            alt=""
          />
      </u-form-item>
      <view class="uploadImage" v-if="form.tareVideo.length">
        <view
          class="itemAlready"
          v-for="(item, index) in form.tareVideo"
          :key="index"
        >
          <!-- <video :src="item" controls autoplay muted></video> -->
          <video :src="item" @click="showVideoFn(item)" :controls="false"
                                :show-fullscreen-btn="false"
                                :show-play-btn="false"
                                :show-center-play-btn="false"></video>
          <view
            class="closeIcon"
            v-if="catchStatus==0"
            @click="deleteVideo(index, 'tareVideo')"
          ></view>
        </view>
      </view>
      <u-form-item prop="tareWeightingUrl" label="空车过磅照片" :custom-style="customStyle"  :border-bottom="!form.tareWeightingUrl.length" :required="true">
          <u-input placeholder=" " disabled />
          <img slot="right" style="width: 40rpx; height: 40rpx" @click="uploadImage('tareWeightingUrl', 1)" src="../../icon/photo.png" alt="" />
        </u-form-item>
        <view class="uploadImage" v-if="form.tareWeightingUrl.length">
          <view class="itemAlready" v-for="(item, index) in form.tareWeightingUrl" :key="index">
            <image mode="scaleToFill" :src="item" @click="previewImage(item)" />
            <view class="closeIcon" v-if="catchStatus==0" @click="deleteImage(index, 'tareWeightingUrl')"></view>
          </view>
        </view>
        <u-form-item
          prop="loadingVideo"
          label="装车视频"
          :custom-style="customStyle"
          :border-bottom="!form.loadingVideo.length"
          :required="true"
        >
          <u-input placeholder=" " disabled />
          <img
            slot="right"
            style="width: 40rpx; height: 40rpx"
            @click="uploadVideo('loadingVideo', 3)"
            src="../../icon/uploadVideo.png"
            alt=""
          />
      </u-form-item>
      <view class="uploadImage" v-if="form.loadingVideo.length">
        <view
          class="itemAlready"
          v-for="(item, index) in form.loadingVideo"
          :key="index"
        >
          <!-- <video :src="item" controls autoplay muted></video> -->
          <video :src="item" @click="showVideoFn(item)" :controls="false"
                                :show-fullscreen-btn="false"
                                :show-play-btn="false"
                                :show-center-play-btn="false"></video>
          <view
            class="closeIcon"
            v-if="catchStatus==0"
            @click="deleteVideo(index, 'loadingVideo')"
          ></view>
        </view>
      </view>
      <u-form-item prop="loadingUrl" label="满载照片" :custom-style="customStyle"  :border-bottom="!form.loadingUrl.length" :required="true">
          <u-input placeholder=" " disabled />
          <img slot="right" style="width: 40rpx; height: 40rpx"  @click="uploadImage('loadingUrl', 1)" src="../../icon/photo.png" alt="" />
        </u-form-item>
        <view class="uploadImage" v-if="form.loadingUrl.length">
          <view class="itemAlready" v-for="(item, index) in form.loadingUrl" :key="index">
            <image mode="scaleToFill" :src="item" @click="previewImage(item)" />
            <view class="closeIcon" v-if="catchStatus==0" @click="deleteImage(index, 'loadingUrl')"></view>
          </view>
        </view>

        <u-form-item
          prop="grossVideo"
          label="满车视频"
          :custom-style="customStyle"
          :border-bottom="!form.grossVideo.length"
          :required="true"
        >
          <u-input placeholder=" " disabled />
          <img
            slot="right"
            style="width: 40rpx; height: 40rpx"
            @click="uploadVideo('grossVideo', 1)"
            src="../../icon/uploadVideo.png"
            alt=""
          />
      </u-form-item>
      <view class="uploadImage" v-if="form.grossVideo.length">
        <view
          class="itemAlready"
          v-for="(item, index) in form.grossVideo"
          :key="index"
        >
          <!-- <video :src="item" controls autoplay muted></video> -->
          <video :src="item" @click="showVideoFn(item)" :controls="false"
                                :show-fullscreen-btn="false"
                                :show-play-btn="false"
                                :show-center-play-btn="false"></video>
          <view
            class="closeIcon"
            v-if="catchStatus==0"
            @click="deleteVideo(index, 'grossVideo')"
          ></view>
        </view>
      </view>
      <u-form-item
          prop="fullyLoadVideo"
          label="满载视频"
          :custom-style="customStyle"
          :border-bottom="!form.fullyLoadVideo.length"
          :required="true"
        >
          <u-input placeholder=" " disabled />
          <img
            slot="right"
            style="width: 40rpx; height: 40rpx"
            @click="uploadVideo('fullyLoadVideo', 1)"
            src="../../icon/uploadVideo.png"
            alt=""
          />
      </u-form-item>
      <view class="uploadImage" v-if="form.fullyLoadVideo.length">
        <view
          class="itemAlready"
          v-for="(item, index) in form.fullyLoadVideo"
          :key="index"
        >
          <!-- <video :src="item" controls autoplay muted></video> -->
          <video :src="item" @click="showVideoFn(item)" :controls="false"
                                :show-fullscreen-btn="false"
                                :show-play-btn="false"
                                :show-center-play-btn="false"></video>
          <view
            class="closeIcon"
            v-if="catchStatus==0"
            @click="deleteVideo(index, 'fullyLoadVideo')"
          ></view>
        </view>
      </view>
      <u-form-item prop="grossWeightingUrl" label="满车过磅照片" :custom-style="customStyle"  :border-bottom="!form.grossWeightingUrl.length" :required="true">
          <u-input placeholder=" " disabled />
          <img slot="right" style="width: 40rpx; height: 40rpx"  @click="uploadImage('grossWeightingUrl', 1)" src="../../icon/photo.png" alt="" />
        </u-form-item>
        <view class="uploadImage" v-if="form.grossWeightingUrl.length">
          <view class="itemAlready" v-for="(item, index) in form.grossWeightingUrl" :key="index">
            <image mode="scaleToFill" :src="item" @click="previewImage(item)" />
            <view class="closeIcon" v-if="catchStatus==0" @click="deleteImage(index, 'grossWeightingUrl')"></view>
          </view>
        </view>
      </view>
    </u-form>
    <view
      class="container-footer"
      :style="'padding:' + (isIphonex ? '40rpx 60rpx' : '15rpx 60rpx')"
    >
      <u-button hover-class='none' shape="circle" v-if="catchStatus==0" @click="handleSubmit">保存并发送短信</u-button>
    </view>
    <u-popup v-model="showVideo" mode="center" @close="showVideo = false">
			<view class="video-box" v-if="showVideo">
                <video :src="videoUrl" controls autoplay loop muted />
            </view>
		</u-popup>
  </view>
</template>

<script>
import { uploadFiles } from '@/api/obsUpload/index'
import { catchCow, prepareInfo } from '@/api/pages/catchingCows'
import { purchaseOrderInfo } from '@/api/pages/purchaseOrder'

export default {
  data() {
    return {
      isIphonex: getApp().globalData.systemInfo.isIphonex,
      form: {
        varietiesName: '',
        categoryName: '',
        ageRange: '',
        weightRange: '',
        catchNumber: '',
        earTagList: '',
        earTagNumber: '',
        bindEarTagVideo: [],
        unitPrice: '',
        contractUrl: [],
        tareWeight: '',
        grossWeight: '',
        netWeight: '',
        totalAmount: '',
        tareVideo: [],
        tareWeightingUrl: [],
        loadingVideo: [],
        loadingUrl: [],
        grossVideo: [],
        fullyLoadVideo: [],
        grossWeightingUrl: [],
      },
      rules: {
        varietiesName: [{ required: true, message: '请输入活畜品种', trigger: 'blur' }],
        categoryName: [{ required: true, message: '请输入品种分类', trigger: 'blur' }],
        ageRange: [{ required: true, message: '请输入活畜月龄', trigger: 'blur' }],
        weightRange: [{ required: true, message: '请输入活畜重量', trigger: 'blur' }],
        catchNumber: [{ required: true, message: '请输入活畜头数', trigger: 'blur' }],
        earTagList: [{ required: true, message: '请输入耳标编号', trigger: 'blur' }],
        earTagNumber: [{ required: true, message: '请输入检疫耳标打数量', trigger: 'blur' }],
        bindEarTagVideo: [{ required: true, message: '请上传戴耳标照片', trigger: 'blur' }],
        unitPrice: [{ required: true, message: '请输入单价', trigger: 'blur' }],
        contractUrl: [{ required: true, message: '请上传合同附件', trigger: 'blur' }],
        tareWeight: [{ required: true, message: '请输入皮量', trigger: 'blur' }],
        grossWeight: [{ required: true, message: '请输入毛量', trigger: 'blur' }],
        netWeight: [{ required: true, message: '请输入净量', trigger: 'blur' }],
        totalAmount: [{ required: true, message: '请输入交易总价格', trigger: 'blur' }],
        tareVideo: [{ required: true, message: '请上传空车过磅视频', trigger: 'blur' }],
        tareWeightingUrl: [{ required: true, message: '请上传皮重过磅照片', trigger: 'blur' }],
        loadingVideo: [{ required: true, message: '请上传装车视频', trigger: 'blur' }],
        loadingUrl: [{ required: true, message: '请上传满载照片', trigger: 'blur' }],
        grossVideo: [{ required: true, message: '请上传毛重视频', trigger: 'blur' }],
        grossWeightingUrl: [{ required: true, message: '请上传毛重过磅照片', trigger: 'blur' }],
      },
      labelStyle: {
        fontSize: '26rpx',
        color: '#333',
      },
      customStyle: {
        textAlign: "right",
      },
      purchaseOrderId: '',
      catchStatus: '',
      fileType: ["pdf"],
      contractUrlName: '',
      showVideo: false,
      videoUrl: '',
    }
  },
  onLoad(options) {
    this.purchasePrepareId = options.purchasePrepareId
    this.purchaseOrderId = options.purchaseOrderId
    this.catchStatus = options.catchStatus
    console.log(this.purchasePrepareId)
    if(this.catchStatus!=0) {
      this.getDetailInfo(this.purchasePrepareId)
    } else {
      this.getPurchaseOrderInfo()
    }
  },
  created() {
  },
  onShow() {
    console.log(this.$store.state.user.tagList)
    if(this.$store.state.user.tagList?.length>0){
      this.form.earTagList = this.$store.state.user.tagList.join(',')
    }
  },
  computed: {
    isSubmitDisabled() {
      return !this.form.varietiesName || !this.form.categoryName || !this.form.ageRange || !this.form.weightRange || !this.form.catchNumber || !this.form.earTagList || !this.form.earTagNumber || !this.form.bindEarTagVideo.length || !this.form.unitPrice || !this.form.contractUrl.length
    },
  },
  watch: {
    'form.grossWeight'(newVal) {
      this.calculateNetWeight()
    },
    'form.tareWeight'(newVal) {
      this.calculateNetWeight()
    },
    'form.netWeight'(newVal) {
      this.calculateTotalAmount()
    },
    'form.unitPrice'(newVal) {
      this.calculateTotalAmount()
    }
  },
  methods: {
    calculateNetWeight() {
      if(this.form.grossWeight && this.form.tareWeight) {
        const grossWeight = parseFloat(this.form.grossWeight)
        const tareWeight = parseFloat(this.form.tareWeight)
        if (!isNaN(grossWeight) && !isNaN(tareWeight)) {
          if(grossWeight < tareWeight) {
            this.$u.toast('满车重量不能空车重量')
            this.form.netWeight = ''
            return
          }
          this.form.netWeight = (grossWeight - tareWeight).toFixed(2)
        }
      }
    },
    calculateTotalAmount(){
      if (this.form.netWeight && this.form.unitPrice) {
        const netWeight = parseFloat(this.form.netWeight)
        const unitPrice = parseFloat(this.form.unitPrice)
        if (!isNaN(netWeight) && !isNaN(unitPrice)) {
          this.form.totalAmount = (netWeight * unitPrice).toFixed(2)
        }
      }
    },
    getPurchaseOrderInfo(){
      purchaseOrderInfo({purchaseOrderId: this.purchaseOrderId}).then(res => {
        if(res.code==200){
          this.form.varietiesName = res.result.varietiesName
          this.form.categoryName = res.result.categoryName
          this.form.ageRange = res.result.ageRange
          this.form.weightRange = res.result.weightRange
        }
      })
    },
    handleSubmit() {
      this.$refs.catchForm.validate((valid) => {
        if (valid) {
          const params = {
            purchasePrepareId: this.purchasePrepareId,
            catchNumber: this.form.catchNumber,
            earTagList: this.form.earTagList?.split(','),
            earTagNumber: this.form.earTagNumber,
            bindEarTagVideo: this.form.bindEarTagVideo?.join(','),
            unitPrice: this.form.unitPrice,
            contractUrl: this.form.contractUrl?.join(','),
            tareWeight: this.form.tareWeight,
            grossWeight: this.form.grossWeight,
            netWeight: this.form.netWeight,
            totalAmount: this.form.totalAmount,
            tareVideo: this.form.tareVideo?.join(','),
            tareWeightingUrl: this.form.tareWeightingUrl?.join(','),
            loadingVideo: this.form.loadingVideo?.join(','),
            loadingUrl: this.form.loadingUrl?.join(','),
            grossVideo: this.form.grossVideo?.join(','),
            fullyLoadVideo: this.form.fullyLoadVideo?.join(','),
            grossWeightingUrl: this.form.grossWeightingUrl.join(','),
          }
          console.log(params)
          catchCow(params).then(res => {
            if(res.code==200){
              this.$toast('操作成功')
              this.$store.commit('user/REMOVETAGLIST')
              uni.navigateBack({
                delta: 1
              })
            }
          })
        }
      })
    },
    uploadImage(type, maxCount) {
      if(this.catchStatus!=0) return;
      if (this.form[type].length >= maxCount) {
        uni.showToast({
          icon: 'none',
          title: `图片最多只能上传${maxCount}张`,
        })
        return
      }
      const that = this
      uni.chooseImage({
        count: 1,
        sizeType: ['original', 'compressed'],
        sourceType: ['album', 'camera'],
        name: 'file',
        success: function (res) {
          uploadFiles({
            filePath: res.tempFilePaths[0],
          }).then((data) => {
            that.form[type].push(data)
          })
        },
        fail(e) {},
      })
    },
    previewImage(url) {
      uni.previewImage({
        urls: [url],
      })
    },
    deleteImage(index, type) {
      this.form[type].splice(index, 1)
      this.$forceUpdate()
    },
    // 添加耳标
    handleEartagNo(){
      uni.navigateTo({
         url: '/myPackge2/pages/catchingCows/earTagNumber'
      })
    },
    uploadVideo(type, maxCount) {
      if(this.catchStatus!=0) return;
      if (this.form[type].length >= maxCount) {
        uni.showToast({
          icon: 'none',
          title: `视频最多只能上传${maxCount}个`,
        })
        return
      }
      const that = this
      uni.chooseMedia({
        count: 1,
        sizeType: ['original', 'compressed'],
        sourceType: ['album', 'camera'],
        name: 'file',
        mediaType: ['video'],
        success: function (res) {
          uploadFiles({
            filePath: res.tempFiles[0].tempFilePath,
          }).then((data) => {
            that.form[type].push(data)
          })
        },
        fail(e) {},
      })
    },
    deleteVideo(index, type) {
      this.form[type].splice(index, 1)
      this.$forceUpdate()
    },
    // 上传pdf
    uploadPdf(type, maxCount){
      if(this.catchStatus!=0) return;
      let that = this; 
      if(this.form[type].length >= maxCount) {
        uni.showToast({
          icon: 'none',
          title: `pdf最多只能上传${maxCount}个`,
        })
        return
      }
      uni.chooseMessageFile({
        type: 'file',
        count: 1,
        success: function (res) {
          let resFile = res.tempFiles[0]
          console.log(resFile)
          if(resFile.type=='file') {
            let index = resFile.name.lastIndexOf('.');
					  let name = resFile.name.substring(index + 1);
            console.log('name', name)
            if(!that.fileType.includes(name)){
              uni.showToast({
                icon: 'none',
                title: '请上传pdf文件',
              })
              return
            } else {
              uploadFiles({
                  filePath: resFile.path,
                  name: resFile.name,
                }).then((data) => {
                  console.log(data)
                  that.form[type].push(data)
                  that.contractUrlName = resFile.name
              })
            }
          } else {
            uni.showToast({
                icon: 'none',
                title: '请上传pdf文件',
              })
          }
        },
      })
    },
      previewFiles(url){
        console.log(url)
        wx.downloadFile({
					url: url,
					success: function(res) {
						const filePath = res.tempFilePath
						wx.openDocument({
							filePath: filePath,
							fileType: 'pdf',
							success: function(res) {},
						})
					},

				})
      },
    getDetailInfo(purchasePrepareId){
      prepareInfo({purchasePrepareId}).then(res => {
        if(res.code ==200) {
         const {varietiesName, categoryName, ageRange, weightRange, catchNumber, earTagArray, earTagNumber, bindEarTagVideo, unitPrice, contractUrl, tareWeight, grossWeight, netWeight, totalAmount, tareVideo, tareWeightingUrl, loadingVideo, loadingUrl, grossVideo,fullyLoadVideo, grossWeightingUrl} = res.result
         this.form = {
          varietiesName,
          categoryName,
          ageRange,
          weightRange,
          catchNumber,
          earTagList: earTagArray,
          earTagNumber,
          bindEarTagVideo: bindEarTagVideo.split(','),
          unitPrice,
          contractUrl: contractUrl.split(','),
          tareWeight,
          grossWeight,
          netWeight,
          totalAmount,
          tareVideo: tareVideo.split(','),
          tareWeightingUrl: tareWeightingUrl.split(','),
          loadingVideo: loadingVideo.split(','),
          loadingUrl: loadingUrl.split(','),
          grossVideo: grossVideo.split(','),
          fullyLoadVideo: fullyLoadVideo?.split(','),
          grossWeightingUrl: grossWeightingUrl.split(','),
         }
         this.contractUrlName = this.getFileName(contractUrl);
         this.$store.commit('user/SET_TAGLIST', earTagList)
        }
      })
    },
    getFileName(url) {
      const matches = url.match(/[^/?#]+(?=[?#]|$)/);
      return matches ? decodeURIComponent(matches[0]) : '';
    },
    showVideoFn(item) {
        this.videoUrl = item
        this.showVideo = true
    }
  },
}
</script>

<style lang="scss" scoped>
@import '../catchingCows/form.scss';

.form-section {
  margin-bottom: 20rpx;
}
.form-title {
  font-size: 32rpx;
  font-weight: bold;
  margin: 10rpx 0;
}
.form-box{
  padding-bottom: 160rpx !important;
  overflow: auto;
}
.uploadImage{
  display: flex !important;
  flex-wrap: wrap !important;
}
.pdf-box{
  width: auto;
  height: auto;
  color: #37BA7E;
  margin: 20rpx 20rpx 10rpx 0rpx;
  padding-right: 30rpx;
  box-sizing: border-box;
}
</style> 