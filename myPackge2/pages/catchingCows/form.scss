.main {
    .form-box {
        background-color: #fff;
        padding: 30rpx;
    }
}

.itemAlready {
    width: 140rpx;
    height: 140rpx;
    border-radius: 8rpx;
    position: relative;
    margin: 0 20rpx 10rpx 0rpx;

    image {
        width: 100%;
        height: 100%;
        border-radius: 8rpx;
    }

    video {
        width: 100%;
        height: 100%;
        border-radius: 8rpx;
    }


    .closeIcon {
        width: 32rpx;
        height: 32rpx;
        background-image: url('../../../static/modalImg/error.png');
        position: absolute;
        background-size: cover;
        top: -10rpx;
        right: -10rpx;
    }
    .close-file{
        top: 5rpx;
        right: -20rpx;
    }
}

.container-footer {
    width: 100%;
    position: fixed;
    bottom: 0;
    background-color: white;
    color: white;
    z-index: 1000;

    /deep/ .u-btn {
        color: white;
        background-color: #40CA8F;
    }

    /deep/ .u-btn--default--disabled {
        background-color: #abdcc7;
    }
}
.tips {
    font-size: 28rpx;
    color: #c0c3ca;
}
.common {
    font-size: 28rpx;
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    color: #333;
    margin: 25rpx 0 45rpx;
    text-align: right;
}
/deep/ .u-form-item--right__content__slot{
    justify-content: flex-end !important;
}