<template>
  <view class="main">
    <u-form
      ref="uForm"
      :model="form"
      :rules="rules"
      label-width="auto"
      :label-style="labelStyle"
      class="form-box"
    >
      <u-form-item
        label="操作日期"
        prop="kongcaoTime"
        :custom-style="customStyle"
        style="text-align: right"
      >
        <text
          :class="form.kongcaoTime ? 'common' : 'tips'"
          @click="visable = true"
          >{{
            form.kongcaoTime ? form.kongcaoTime : "请选择操作日期"
          }}</text
        >
      </u-form-item>
      <u-form-item prop="kongcaoHours" label="控槽时长（小时）" :required="true">
        <u-input
          v-model="form.kongcaoHours"
          placeholder="请输入控槽时长"
          :custom-style="customStyle"
          placeholder-style="text-align:right;color:#999;font-size: 26rpx;"
        />
      </u-form-item>
      <u-form-item
        prop="kongcaoVideo"
        label="控槽视频"
        :custom-style="customStyle"
        :border-bottom="!form.kongcaoVideo.length"
        :required="true"
      >
        <u-input placeholder=" " disabled />
        <img
          slot="right"
          style="width: 40rpx; height: 40rpx"
          @click="uploadVideo('kongcaoVideo')"
          src="../../icon/uploadVideo.png"
          alt=""
        />
      </u-form-item>
      <view class="uploadImage" v-if="form.kongcaoVideo.length">
        <view
          class="itemAlready"
          v-for="(item, index) in form.kongcaoVideo"
          :key="index"
        >
          <!-- <video :src="item" controls autoplay muted></video> -->
          <video :src="item" @click="showVideoFn(item)" :controls="false"
                                :show-fullscreen-btn="false"
                                :show-play-btn="false"
                                :show-center-play-btn="false"></video>
          <view
            class="closeIcon"
            @click="deleteVideo(index, 'kongcaoVideo')"
          ></view>
        </view>
      </view>
    </u-form>
    <view
      class="container-footer"
      :style="'padding:' + (isIphonex ? '40rpx 60rpx' : '15rpx 60rpx')"
    >
      <u-button hover-class='none' shape="circle" @click="handleSubmit" :disabled="isSubmitDisabled">
       {{  kongcaoStatus== 1 ? '编辑' : '保存' }}
      </u-button>
    </view>
    <termPicker
      :timeLimit="false"
      :deferYear="100"
      :minYear="2023"
      @changeTime="changeTime"
      :visable.sync="visable"
    />
    <u-popup v-model="showVideo" mode="center" @close="showVideo = false">
			<view class="video-box" v-if="showVideo">
                <video :src="videoUrl" controls autoplay loop muted />
            </view>
		</u-popup>
  </view>
</template>

<script>
import { uploadFiles } from "@/api/obsUpload/index";
import termPicker from "@/components/term-picker/term-picker";
import {kongcao, prepareInfo} from '@/api/pages/catchingCows'


export default {
  components: {
    termPicker,
  },
  data() {
    return {
      isIphonex: getApp().globalData.systemInfo.isIphonex,
      form: {
        kongcaoTime: this.getCurrentDate(),
        kongcaoHours: "",
        kongcaoVideo: [],
      },
      rules: {
        kongcaoTime: [
          { required: true, message: "请选择操作日期", trigger: "blur" },
        ],
        kongcaoHours: [
          { required: true, message: "请输入控槽时长", trigger: "blur" },
        ],
        kongcaoVideo: [
          { required: true, message: "请上传控槽视频", trigger: "change" },
        ],
      },
      labelStyle: {
        fontSize: "26rpx",
        fontFamily: "PingFang SC, PingFang SC",
        color: "#333",
      },
      customStyle: {
        textAlign: "right",
      },
      visable: false,
      purchasePrepareId: '',
      kongcaoStatus: '',
      showVideo: false,
      videoUrl: '',
    };
  },
  computed: {
    isSubmitDisabled() {
      return (
        !this.form.kongcaoTime ||
        !this.form.kongcaoHours ||
        !this.form.kongcaoVideo.length
      );
    },
  },
  onLoad(option) {
    this.purchasePrepareId = option.purchasePrepareId
    this.kongcaoStatus = option.kongcaoStatus
    console.log(this.purchasePrepareId)
    if(this.kongcaoStatus == 1){
      this.getDetailInfo(this.purchasePrepareId)
    }
  },
  methods: {
    handleSubmit() {
      console.log("控槽表单数据:", this.form);
      this.$refs.uForm.validate((valid) => {
        if (valid) {
          const params = {
            purchasePrepareId: this.purchasePrepareId,
            kongcaoTime: this.form.kongcaoTime,
            kongcaoHours: this.form.kongcaoHours,
            kongcaoVideo: this.form.kongcaoVideo.join(","),
          };
          kongcao(params).then(res => {
            if(res.code==200){
              this.$toast('操作成功')
              uni.navigateBack({
                delta: 1
              })
            }
          })
        }
      });
    },
    uploadVideo(type) {
      if (this.form[type].length >= 1) {
        uni.showToast({
          icon: "none",
          title: "视频最多只能上传1个",
        });
        return;
      }
      const that = this;
      uni.chooseMedia({
        count: 1,
        sizeType: ["original", "compressed"],
        sourceType: ["album", "camera"],
        name: "file",
        mediaType: ["video"],
        success: function (res) {
          uploadFiles({
            filePath: res.tempFiles[0].tempFilePath,
          }).then((data) => {
            that.form[type].push(data);
          });
        },
        fail(e) {},
      });
    },
    deleteVideo(index, type) {
      this.form[type].splice(index, 1);
      this.$forceUpdate();
    },
    changeTime(value) {
      console.log(value);
      this.form.kongcaoTime = value;
      this.form.kongcaoTime != "" ? this.resetField("kongcaoTime") : "";
      this.visable = false;
    },
    resetField(value) {
      this.$refs.uForm.fields.forEach((e) => {
        if (e.prop == value) {
          e.resetField();
        }
      });
    },
    getCurrentDate() {
      const date = new Date();
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      return `${year}-${month}-${day}`;
    },
    // 获取控槽详情
    getDetailInfo(purchasePrepareId){
      console.log('获取控槽详情')
      prepareInfo({purchasePrepareId}).then(res => {
        if(res.code==200) {
          const { kongcaoTime, kongcaoHours, kongcaoVideo } = res.result
          this.form = {
            kongcaoTime,
            kongcaoHours,
            kongcaoVideo: kongcaoVideo.split(',')
          }
        }
      })
    },
    showVideoFn(item) {
        this.videoUrl = item
        this.showVideo = true
    }
  },
};
</script>

<style lang="scss" scoped>
@import "../catchingCows/form.scss";
.uploadImage{
  display: flex !important;
  flex-wrap: wrap !important;
}
</style> 