<template>
  <view class="main">
    <u-form ref="uForm" :model="form" :rules="rules" :error-type="errorType" label-width="auto" :label-style="labelStyle" class="form-box">
      <u-form-item label="车牌号">
        <u-input v-model="form.licensePlateNumber" placeholder="车牌号" readonly :custom-style="customStyle" placeholder-style="text-align:right;color:#999;font-size: 26rpx;" />
      </u-form-item>
      <u-form-item
          prop="tareVideo"
          label="空车过磅视频"
          :custom-style="customStyle"
          :border-bottom="!form.tareVideo.length"
          :required="true"
        >
          <u-input placeholder=" " disabled />
          <img
            slot="right"
            style="width: 40rpx; height: 40rpx"
            @click="uploadVideo('tareVideo', 1)"
            src="../../icon/uploadVideo.png"
            alt=""
          />
      </u-form-item>
      <view class="uploadImage" v-if="form.tareVideo.length">
        <view class="itemAlready" v-for="(item, index) in form.tareVideo" :key="index" >
          <!-- <video :src="item" controls autoplay muted></video> -->
          <video :src="item" @click="showVideoFn(item)" :controls="false"
                                :show-fullscreen-btn="false"
                                :show-play-btn="false"
                                :show-center-play-btn="false"></video>
          <view class="closeIcon" @click="deleteVideo(index, 'tareVideo')" ></view>
        </view>
      </view>
      <u-form-item label="空车磅重照片" prop="tareWeightingUrl" :required="true" :border-bottom="!form.tareWeightingUrl.length">
        <u-input placeholder=" " disabled />
        <img slot="right" style="width: 40rpx; height: 40rpx" @click="uploadFile('tareWeightingUrl', 1)" src="../../icon/photo.png" alt="" />
      </u-form-item>
      <view class="uploadImage" v-if="form.tareWeightingUrl.length">
        <view class="itemAlready" v-for="(item, index) in form.tareWeightingUrl" :key="index">
          <image mode="scaleToFill" :src="item" @click="previewImage(item)" />
          <view class="closeIcon" @click="deleteFile(index, 'tareWeightingUrl')"></view>
        </view>
      </view>
      <u-form-item label="空车皮重重量（kg）" prop="tareWeight">
        <u-input v-model="form.tareWeight" :required="true" placeholder="请输入皮重重量" :custom-style="customStyle" placeholder-style="text-align:right;color:#999;font-size: 26rpx;"  />
      </u-form-item>
      <u-form-item
          prop="loadingVideo"
          label="装车视频"
          :custom-style="customStyle"
          :border-bottom="!form.loadingVideo.length"
          :required="true"
        >
          <u-input placeholder=" " disabled />
          <img
            slot="right"
            style="width: 40rpx; height: 40rpx"
            @click="uploadVideo('loadingVideo', 3)"
            src="../../icon/uploadVideo.png"
            alt=""
          />
      </u-form-item>
      <view class="uploadImage" v-if="form.loadingVideo.length">
        <view
          class="itemAlready"
          v-for="(item, index) in form.loadingVideo"
          :key="index"
        >
          <!-- <video :src="item" controls autoplay muted></video> -->
          <video :src="item" @click="showVideoFn(item)" :controls="false"
                                :show-fullscreen-btn="false"
                                :show-play-btn="false"
                                :show-center-play-btn="false"></video>

          <view
            class="closeIcon"
            @click="deleteVideo(index, 'loadingVideo')"
          ></view>
        </view>
      </view>
      <u-form-item label="装车照片" prop="loadingUrl" :border-bottom="!form.loadingUrl.length" :required="true">
        <u-input placeholder=" " disabled />
        <img slot="right" style="width: 40rpx; height: 40rpx" @click="uploadFile('loadingUrl', 1)" src="../../icon/photo.png" alt="" />
      </u-form-item>
      <view class="uploadImage" v-if="form.loadingUrl.length">
        <view class="itemAlready" v-for="(item, index) in form.loadingUrl" :key="index">
          <image mode="scaleToFill" :src="item" @click="previewImage(item)" />
          <view class="closeIcon" @click="deleteFile(index, 'loadingUrl')"></view>
        </view>
      </view>
      <u-form-item label="牛只栓系照片" prop="tetheringUrl" :border-bottom="!form.tetheringUrl.length" :required="true">
        <u-input placeholder=" " disabled />
        <img slot="right" style="width: 40rpx; height: 40rpx" @click="uploadFile('tetheringUrl', 1)" src="../../icon/photo.png" alt="" />
      </u-form-item>
      <view class="uploadImage" v-if="form.tetheringUrl.length">
        <view class="itemAlready" v-for="(item, index) in form.tetheringUrl" :key="index">
          <image mode="scaleToFill" :src="item" @click="previewImage(item)" />
          <view class="closeIcon" @click="deleteFile(index, 'tetheringUrl')"></view>
        </view>
      </view>
      <u-form-item
          prop="fullyLoadVideo"
          label="满载车辆视频"
          :custom-style="customStyle"
          :border-bottom="!form.fullyLoadVideo.length"
          :required="true"
        >
          <u-input placeholder=" " disabled />
          <img
            slot="right"
            style="width: 40rpx; height: 40rpx"
            @click="uploadVideo('fullyLoadVideo', 1)"
            src="../../icon/uploadVideo.png"
            alt=""
          />
      </u-form-item>
      <view class="uploadImage" v-if="form.fullyLoadVideo.length">
        <view
          class="itemAlready"
          v-for="(item, index) in form.fullyLoadVideo"
          :key="index"
        >
          <!-- <video :src="item" controls autoplay muted></video> -->
          <video :src="item" @click="showVideoFn(item)" :controls="false"
                                :show-fullscreen-btn="false"
                                :show-play-btn="false"
                                :show-center-play-btn="false"></video>
          <view
            class="closeIcon"
            @click="deleteVideo(index, 'fullyLoadVideo')"
          ></view>
        </view>
      </view>
      <u-form-item
          prop="grossVideo"
          label="满载过磅视频"
          :custom-style="customStyle"
          :border-bottom="!form.grossVideo.length"
          :required="true"
        >
          <u-input placeholder=" " disabled />
          <img
            slot="right"
            style="width: 40rpx; height: 40rpx"
            @click="uploadVideo('grossVideo', 1)"
            src="../../icon/uploadVideo.png"
            alt=""
          />
      </u-form-item>
      <view class="uploadImage" v-if="form.grossVideo.length">
        <view
          class="itemAlready"
          v-for="(item, index) in form.grossVideo"
          :key="index"
        >
          <!-- <video :src="item" controls autoplay muted></video> -->
          <video :src="item" @click="showVideoFn(item)" :controls="false"
                                :show-fullscreen-btn="false"
                                :show-play-btn="false"
                                :show-center-play-btn="false"></video>
          <view
            class="closeIcon"
            @click="deleteVideo(index, 'grossVideo')"
          ></view>
        </view>
      </view>
      <u-form-item label="满车磅重照片" prop="grossWeightingUrl" :border-bottom="!form.grossWeightingUrl.length" :required="true">
        <u-input placeholder=" " disabled />
        <img slot="right" style="width: 40rpx; height: 40rpx" @click="uploadFile('grossWeightingUrl', 1)" src="../../icon/photo.png" alt="" />
      </u-form-item>
      <view class="uploadImage" v-if="form.grossWeightingUrl.length">
        <view class="itemAlready" v-for="(item, index) in form.grossWeightingUrl" :key="index">
          <image mode="scaleToFill" :src="item" @click="previewImage(item)" />
          <view class="closeIcon" @click="deleteFile(index, 'grossWeightingUrl')"></view>
        </view>
      </view>
      <u-form-item label="发车毛重重量（kg）" prop="grossWeight">
        <u-input v-model="form.grossWeight" :required="true" placeholder="请输入毛重重量" :custom-style="customStyle" placeholder-style="text-align:right;color:#999;font-size: 26rpx;"  />
      </u-form-item>
      <u-form-item label="发车净重量（kg）">
        <u-input v-model="form.netWeight" disabled  placeholder="活畜重量" :custom-style="customStyle" placeholder-style="text-align:right;color:#999;font-size: 26rpx;"  />
      </u-form-item>
      <u-form-item label="出发榜单收据" prop="weighingReceipts" :border-bottom="!form.weighingReceipts.length" :required="true">
        <u-input placeholder=" " disabled />
        <img slot="right" style="width: 40rpx; height: 40rpx" @click="uploadFile('weighingReceipts', 1)" src="../../icon/photo.png" alt="" />
      </u-form-item>
      <view class="uploadImage" v-if="form.weighingReceipts.length">
        <view class="itemAlready" v-for="(item, index) in form.weighingReceipts" :key="index">
          <image mode="scaleToFill" :src="item" @click="previewImage(item)" />
          <view class="closeIcon" @click="deleteFile(index, 'weighingReceipts')"></view>
        </view>
      </view>
    
    </u-form>
    <view
      class="container-footer"
      :style="'padding:' + (isIphonex ? '40rpx 60rpx' : '15rpx 60rpx')"
    >
      <u-button hover-class='none' shape="circle" @click="saveDepartureData">保存</u-button>
    </view>
    <u-popup v-model="showVideo" mode="center" @close="showVideo = false">
			<view class="video-box" v-if="showVideo">
                <video :src="videoUrl" controls autoplay loop muted />
            </view>
		</u-popup>
  </view>
</template>

<script>
import { uploadFiles } from '@/api/obsUpload/index'
import { wholeCar, purchaseGoInfo } from '@/api/pages/catchingCows'
import { purchaseOrderInfo } from '@/api/pages/purchaseOrder'

export default {
  components: {
  },
  data() {
    return {
      isIphonex: getApp().globalData.systemInfo.isIphonex,
      form: {
        licensePlateNumber: '',
        tareWeight: '',
        grossWeight: '',
        netWeight: '',
        tareVideo: [],
        tareWeightingUrl: [],
        loadingVideo: [],
        grossWeightingUrl: [],
        tetheringUrl: [],
        loadingUrl: [],
        grossVideo: [],
        fullyLoadVideo: [],
        maozhongPhoto: [],
        weighingReceipts: [],
      },
      rules: {
        tareWeight: [{
          required: true,
          message: '请输入空车皮重重量',
          trigger: ['blur', 'change']
        }],
        grossWeight: [{
          required: true,
          message: '请输入发车毛重重量',
          trigger: ['blur', 'change']
        }],
        tareVideo: [{
          required: true,
          validator: (rule, value, callback) => {value?.length>0 ? callback() : callback(new Error('请上传空车过磅视频'))},
          message: '请上传空车过磅视频',
          trigger: 'change'
        }],
      tareWeightingUrl: [{
        required: true,
        validator: (rule, value, callback) => {value?.length>0 ? callback() : callback(new Error('请上传空车磅重照片'))},
        message: '请上传空车磅重照片',
        trigger: 'change'
      }],
        loadingVideo: [{
          required: true,
          validator: (rule, value, callback) => {value?.length>0 ? callback() : callback(new Error('请上传装车视频'))},
          message: '请上传装车视频',
          trigger: 'change'
        }],
        grossWeightingUrl: [{
          required: true,
          validator: (rule, value, callback) => {value?.length>0 ? callback() : callback(new Error('请上传满车磅重照片'))},
          message: '请上传满车磅重照片',
          trigger: 'change'
        }],
        tetheringUrl: [{
          required: true,
          validator: (rule, value, callback) => {value?.length>0 ? callback() : callback(new Error('请上传牛只栓系照片'))},
          message: '请上传牛只栓系照片',
          trigger: 'change'
        }],
        loadingUrl: [{
          required: true,
          validator: (rule, value, callback) => {value?.length>0 ? callback() : callback(new Error('请上传装车照片'))},
          message: '请上传装车照片',
          trigger: 'change'
        }],
        grossVideo: [{
          required: true,
          validator: (rule, value, callback) => {value?.length>0 ? callback() : callback(new Error('请上传满载过磅视频'))},
          message: '请上传满载过磅视频',
          trigger: 'change'
        }],
        fullyLoadVideo: [{
          required: true,
          validator: (rule, value, callback) => {value?.length>0 ? callback() : callback(new Error('请上传满载装车视频'))},
          message: '请上传满载装车视频',
          trigger: 'change'
        }],
        maozhongPhoto: [{
          required: true,
          validator: (rule, value, callback) => {value?.length>0 ? callback() : callback(new Error('请上传毛重过磅照片'))},
          message: '请上传毛重过磅照片',
          trigger: 'change'
        }],
        weighingReceipts: [{
          required: true,
          validator: (rule, value, callback) => {value?.length>0 ? callback() : callback(new Error('请上传出发榜单收据'))},
          message: '请上传出发榜单收据',
          trigger: 'change'
        }]
      },
      errorType: ['message'],
      labelStyle: {
        fontSize: '26rpx',
        color: '#333',
      },
      customStyle: {
        textAlign: "right",
      },
      visable: false,
      purchaseOrderId: '',
      markFlag: '',
      showVideo: false,
      videoUrl: '',
    }
  },
  onReady() {
        this.$refs.uForm.setRules(this.rules)
    },
  watch: {
    'form.grossWeight'(newVal) {
      this.calculateHczhongliang();
    },
    'form.tareWeight'(newVal) {
      this.calculateHczhongliang();
    }
  },
  onLoad(options) {
    this.purchaseOrderId = options.purchaseOrderId
    this.markFlag = options.markFlag
    if(this.markFlag==3){
      this.getDetailInfo(this.purchaseOrderId)
    }
    this.getPurchaseOrderInfo()
  },
  methods: {
    getPurchaseOrderInfo(){
      purchaseOrderInfo({purchaseOrderId: this.purchaseOrderId}).then(res => {
        if(res.code==200){
          this.form.licensePlateNumber = res.result.licensePlateNumber
        }
      })
    },
    calculateHczhongliang() {
      const grossWeight = parseFloat(this.form.grossWeight);
      const tareWeight = parseFloat(this.form.tareWeight);
      if (!isNaN(grossWeight) && !isNaN(tareWeight)) {
        this.form.netWeight = (grossWeight - tareWeight).toString();
      } else {
        this.form.netWeight = '';
      }
    },
    uploadFile(type, maxUploads) {
      if (this.form[type].length >= maxUploads) {
        uni.showToast({
          icon: 'none',
          title: `文件最多只能上传${maxUploads}个`,
        })
        return
      }
      const that = this
      uni.chooseImage({
        count: 1,
        sizeType: ['original', 'compressed'],
        sourceType: ['album', 'camera'],
        name: 'file',
        success: function (res) {
          uploadFiles({
            filePath: res.tempFilePaths[0],
          }).then((data) => {
            that.form[type].push(data)
            that.$nextTick(() => {
              that.$refs.uForm.validate(type) // 新增校验触发
            })
          })
        },
        fail(e) {},
      })
    },
    previewImage(url) {
      uni.previewImage({
        urls: [url],
      })
    },
    deleteFile(index, type) {
      this.form[type].splice(index, 1)
      this.$nextTick(() => {
        this.$refs.uForm.validate(type) // 新增校验触发
      })
      this.$forceUpdate()
    },
    saveDepartureData() {
      this.$refs.uForm.validate((valid) => {
        if (valid) {
          console.log('ok')
          const params = {
            purchaseOrderId: this.purchaseOrderId,
            tareWeight: this.form.tareWeight,
            grossWeight: this.form.grossWeight,
            netWeight: this.form.netWeight,
            tareVideo: this.form.tareVideo.join(','),
            tareWeightingUrl: this.form.tareWeightingUrl.join(','),
            loadingVideo: this.form.loadingVideo.join(','),
            grossWeightingUrl: this.form.grossWeightingUrl.join(','),
            tetheringUrl: this.form.tetheringUrl.join(','),
            loadingUrl: this.form.loadingUrl.join(','),
            grossVideo: this.form.grossVideo.join(','),
            fullyLoadVideo: this.form.fullyLoadVideo.join(','),
            maozhongPhoto: this.form.maozhongPhoto.join(','),
            weighingReceipts: this.form.weighingReceipts.join(','),
          }
          wholeCar(params).then(res => {
            if(res.code==200){
              uni.$emit('updateFlow')
              uni.showToast({
                title: '保存成功',
                icon: "none",
                duration: 2000
              });
              setTimeout(() => {
                uni.navigateBack({
                  delta: 1
                })
              }, 1000);
            }
          })
        } else {
          console.log('error submit!!');
        }
      })
    },
    changeTime(value) {
      console.log(value);
      this.form.operationDate = value;
      this.form.operationDate != "" ? this.resetField("operationDate") : "";
      this.visable = false;
    },
    resetField(value) {
      this.$refs.uForm.fields.forEach((e) => {
        if (e.prop == value) {
          e.resetField();
        }
      });
    },
    getCurrentDate() {
      const date = new Date();
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      return `${year}-${month}-${day}`;
    },
    uploadVideo(type, maxUploads) {
      if (this.form[type].length >= maxUploads) {
        uni.showToast({
          icon: 'none',
          title: `视频最多只能上传${maxUploads}个`,
        })
        return
      }
      const that = this
      uni.chooseMedia({
        count: 1,
        sizeType: ['original', 'compressed'],
        sourceType: ['album', 'camera'],
        name: 'file',
        mediaType: ['video'],
        success: function (res) {
          uploadFiles({
            filePath: res.tempFiles[0].tempFilePath,
          }).then((data) => {
            that.form[type].push(data)
            that.$nextTick(() => {
              that.$refs.uForm.validate(type) // 新增校验触发
            })
          })
        },
        fail(e) {},
      })
    },
    deleteVideo(index, type) {
      this.form[type].splice(index, 1)
      this.$nextTick(() => {
        this.$refs.uForm.validate(type) // 新增校验触发
      })
      this.$forceUpdate()
    },
    getDetailInfo(purchaseOrderId){
      purchaseGoInfo({purchaseOrderId}).then(res => {
        if(res.code==200) {
          const {
            tareWeight,
            grossWeight,
            netWeight,
            tareVideo,
            tareWeightingUrl,
            loadingVideo,
            grossWeightingUrl,
            tetheringUrl,
            loadingUrl,
            grossVideo,
            fullyLoadVideo,
            maozhongPhoto,
            weighingReceipts,
          } = res.result
          this.form = {
            tareWeight,
            grossWeight,
            netWeight,
            tareVideo: tareVideo.split(','),
            tareWeightingUrl: tareWeightingUrl.split(','),
            loadingVideo: loadingVideo.split(','),
            grossWeightingUrl: grossWeightingUrl.split(','),
            tetheringUrl: tetheringUrl?.split(','),
            loadingUrl:loadingUrl.split(','),
            grossVideo: grossVideo.split(','),
            fullyLoadVideo: fullyLoadVideo.split(','),
            maozhongPhoto: maozhongPhoto.split(','),
            weighingReceipts: weighingReceipts.split(','),
          }
        }
      })
    },
    showVideoFn(item) {
        this.videoUrl = item
        this.showVideo = true
    }
  }
}
</script>

<style lang="scss" scoped>
@import "../catchingCows/form.scss";
.container-footer {
  width: 100%;
  position: fixed;
  bottom: 0;
  background-color: white;
  color: white;
  z-index: 1000;
  padding: 15rpx 60rpx;
  /deep/ .u-btn {
    color: white;
    background-color: #40CA8F;
  }
}
.form-box{
  overflow: auto !important;
  padding-bottom: 160rpx !important;
}
.uploadImage{
  display: flex !important;
  flex-wrap: wrap !important;
}
</style>