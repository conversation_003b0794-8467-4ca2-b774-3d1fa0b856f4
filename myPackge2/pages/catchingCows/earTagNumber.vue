<template>
  <view class="main">
    <u-form ref="earTagForm" :model="form" label-width="auto" :label-style="labelStyle" class="form-box tag-form">
      <view class="form-section">
        <text class="form-title">基础信息</text>
        <u-form-item prop="fenlei" label="耳标编号" :required="true" v-for="(tag, index) in form.earTags" :key="index" class="ear-tag-item">
          <u-input v-model="form.earTags[index]" placeholder="输入耳标编号" :custom-style="customStyle" placeholder-style="text-align:right;color:#999;font-size: 26rpx;" />
          <view class="remove-tag" @click="removeEarTag(index)" v-if="form.earTags.length > 1">删除</view>
        </u-form-item>
        <u-button hover-class='none' class="tag-btn " :class="{ addtag: canAddEarTag, disabledtag: !canAddEarTag }" shape="circle" @click="addEarTag" :disabled="!canAddEarTag">添加耳标编号</u-button>
      </view>
    </u-form>
    <view
      class="container-footer"
      :style="'padding:' + (isIphonex ? '40rpx 60rpx' : '15rpx 60rpx')"
    >
      <u-button hover-class='none' shape="circle" @click="saveEarTags">保存</u-button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      isIphonex: getApp().globalData.systemInfo.isIphonex,
      form: {
        earTags: ['']
      },
      labelStyle: {
        fontSize: '26rpx',
        color: '#333',
      },
      customStyle: {
        textAlign: "right",
      },
    }
  },
  computed: {
    canAddEarTag() {
      return this.form.earTags[this.form.earTags.length - 1] !== ''
    }
  },
  onShow(options) {
    if(this.$store.state.user.tagList?.length>0){
      const tagList = this.$store.state.user.tagList
      this.form.earTags = tagList
    }
  },
  methods: {
    addEarTag() {
      if (this.canAddEarTag) {
        this.form.earTags.push('')
      }
    },
    removeEarTag(index) {
      if (this.form.earTags.length > 1) {
        this.form.earTags.splice(index, 1)
      }
    },
    validateEarTags() {
      const emptyList = this.form.earTags
        .map((tag, index) => ({ tag: tag.trim(), index }))
        .filter(item => !item.tag)

      if (emptyList.length > 0) {
        const positions = emptyList.map(item => item.index + 1).join(',')
        uni.showToast({
          title: `有未填写的耳标编号`,
          icon: 'none',
          duration: 3000
        })
        return false
      }
      return true
    },
    saveEarTags() {
      if (!this.validateEarTags()) return
      this.$store.commit('user/SET_TAGLIST', this.form.earTags)
      uni.navigateBack()
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../catchingCows/form.scss';
.main{
  background: #fff !important;
}
.form-section {
  margin-bottom: 20rpx;
}
.form-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}
.ear-tag-item {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
  position: relative;
}
.container-footer {
  width: 100%;
  position: fixed;
  bottom: 0;
  background-color: white;
  color: white;
  z-index: 1000;
  padding: 15rpx 60rpx;
  /deep/ .u-btn {
    color: white;
    background-color: #40CA8F;
  }
}
/deep/ .u-form-item__body{
  width: 100%;
}
.tag-form{
  padding-bottom: 110rpx !important;
  overflow: auto;
}
.addtag{
  background: #40CA8F !important;
  color: #fff !important;
}
.disabledtag{
  background: #abdcc7!important;
  color: #fff !important;
}
.tag-btn{
  margin: 60rpx 60rpx 0 60rpx;
}
.remove-tag{
  width: 55rpx;
  height: 45rpx;
  border-radius: 10rpx;
  background: #fa3534;
  color: #fff;
  font-size: 20rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-left: 30rpx;
  position: absolute;
  top: 30rpx;
  right: -75rpx;
}
/deep/ .u-form-item{
  width: 90%;
}
</style> 