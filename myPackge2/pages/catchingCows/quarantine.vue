<template>
  <view class="main">
    <u-form
      ref="uForm"
      :model="form"
      :rules="rules"
      label-width="auto"
      :label-style="labelStyle"
      class="form-box"
    >
      <u-form-item
        label="操作日期"
        prop="quarantineTime"
        :custom-style="customStyle"
        style="text-align: right"
      >
        <text
          :class="form.quarantineTime ? 'common' : 'tips'"
          @click="visable = true"
          >{{
            form.quarantineTime ? form.quarantineTime : "请选择操作日期"
          }}</text
        >
      </u-form-item>
      <u-form-item
        prop="quarantineUrl"
        label="检疫合格证"
        :custom-style="customStyle"
        :border-bottom="!form.quarantineUrl.length"
        :required="true"
      >
        <u-input placeholder=" " disabled />
        <img
          slot="right"
          style="width: 40rpx; height: 40rpx"
          @click="unloadImage('quarantineUrl')"
          src="../../icon/photo.png"
          alt=""
        />
      </u-form-item>
      <view class="uploadImage" v-if="form.quarantineUrl.length">
        <view
          class="itemAlready"
          v-for="(item, index) in form.quarantineUrl"
          :key="index"
        >
          <image
            mode="scaleToFill"
            :src="item"
            @click="previewImage(form.quarantineUrl)"
          />
          <view class="closeIcon" @click="deleteImage(index, 'quarantineUrl')"></view>
        </view>
      </view>
    </u-form>
    <view
      class="container-footer"
      :style="'padding:' + (isIphonex ? '40rpx 60rpx' : '15rpx 60rpx')"
    >
      <u-button hover-class='none' shape="circle" @click="handleSubmit" :disabled="isSubmitDisabled">保存</u-button>
    </view>
    <termPicker
      :timeLimit="false"
      :deferYear="100"
      :minYear="2023"
      @changeTime="changeTime"
      :visable.sync="visable"
    />
  </view>
</template>

<script>
import { uploadFiles } from "@/api/obsUpload/index";
import termPicker from "@/components/term-picker/term-picker";
import {quarantine, purchaseGoInfo} from '@/api/pages/catchingCows'


export default {
  components: {
    termPicker,
  },
  data() {
    return {
      isIphonex: getApp().globalData.systemInfo.isIphonex,
      form: {
        quarantineTime: this.getCurrentDate(),
        quarantineUrl: [],
      },
      rules: {
        quarantineTime: [
          { required: true, message: "请选择操作日期", trigger: "blur" },
        ],
        quarantineUrl: [
          { required: true, message: "请上传检疫照片", trigger: "change" },
        ],
      },
      labelStyle: {
        fontSize: "26rpx",
        fontFamily: "PingFang SC, PingFang SC",
        color: "#333",
      },
      customStyle: {
        textAlign: "right",
      },
      visable: false,
      purchaseOrderId: '',
      markFlag: ''
    };
  },
  computed: {
    isSubmitDisabled() {
      return (
        !this.form.quarantineTime ||
        !this.form.quarantineUrl.length
      );
    },
  },
  onLoad(option) {
    this.purchaseOrderId = option.purchaseOrderId
    this.markFlag = option.markFlag
    console.log(this.purchaseOrderId, this.markFlag)
    if(this.markFlag == 3) {
      this.getDetailInfo(this.purchaseOrderId)
    }
  },
  methods: {
    handleSubmit() {
      this.$refs.uForm.validate((valid) => {
        if (!valid) {
          return;
        }
        const params = {
          purchaseOrderId: this.purchaseOrderId,
          quarantineTime: this.form.quarantineTime,
          quarantineUrl: this.form.quarantineUrl.join(","),
        }
        quarantine(params).then(() => {
          uni.$emit('updateFlow')
          this.$toast("操作成功");
          uni.navigateBack({
            delta: 1,
          });
        });
      });
    },
    unloadImage() {
      if (this.form.quarantineUrl.length >= 6) {
        uni.showToast({
          icon: "none",
          title: "图片最多只能上传6张",
        });
        return;
      }
      const that = this;
      uni.chooseImage({
        count: 1, //默认9
        sizeType: ["original", "compressed"], //可以指定是原图还是压缩图，默认二者都有
        sourceType: ["album", "camera"], //从相册选择
        name: "file",
        success: function (res) {
          uploadFiles({
            filePath: res.tempFilePaths[0],
          }).then((data) => {
            that.form.quarantineUrl.push(data);
          });
        },
        fail(e) {},
      });
    },
    deleteImage(index, type) {
      this.form[type].splice(index, 1);
      this.$forceUpdate();
    },
    changeTime(value) {
      console.log(value);
      this.form.quarantineTime = value;
      this.form.quarantineTime != "" ? this.resetField("quarantineTime") : "";
      this.visable = false;
    },
    resetField(value) {
      this.$refs.uForm.fields.forEach((e) => {
        if (e.prop == value) {
          e.resetField();
        }
      });
    },
    getCurrentDate() {
      const date = new Date();
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      return `${year}-${month}-${day}`;
    },
    previewImage(url) {
      uni.previewImage({
        urls: url,
      });
    },
    getDetailInfo(purchaseOrderId) {
      purchaseGoInfo({purchaseOrderId}).then(res => {
        const {quarantineTime, quarantineUrl} = res.result
        this.form = {
          quarantineTime,
          quarantineUrl: quarantineUrl.split(',')
        }
      })
    },
  },
};
</script>

<style lang="scss" scoped>
@import "../catchingCows/form.scss";
.uploadImage{
  display: flex !important;
  flex-wrap: wrap !important;
}
</style> 