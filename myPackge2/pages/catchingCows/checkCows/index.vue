<template>
    <view>
      <scroll-view class="main" scroll-y :scroll-with-animation="true" @scrolltolower="scrollToLower" refresher-enabled :refresher-triggered="refresherState" @refresherrefresh="bindrefresherrefresh" :scroll-top="scrollTop">
        <cellList :list="list" :processType="processType" :markFlag="markFlag" :showOper="false" :showInfo="false" />
        <nullList v-if="isEmpty" />
        <view v-if="!isEmpty" :style="'height:' + (isIphonex ? 48 : 24) + 'rpx'"></view>
      </scroll-view>
  
      <view class="Add" @click="addIntent" v-if="processType==1 && markFlag==1">
        <img src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/xiaoshouhetong/add.png" alt="" />
      </view>
    </view>
  </template>
  
  <script>
  import cellList from '../../components/cellList.vue'
  import nullList from '@/components/null-list/index.vue'
  import {farmersListByPurchaseOrderId} from '@/api/pages/catchingCows'

  export default {
    components: { cellList, nullList },
    data() {
      return {
        isIphonex: getApp().globalData.systemInfo.isIphonex,
        isEmpty: false,
        pickerFilterShow: false,
        filters: {},
        list: [],
        noMore: false,
        pageSize: 10,
        pageNum: 1,
        refresherState: false,
        scrollTop: 0,
        processType: '',
        purchaseOrderId: '',
        markFlag: ''
      }
    },
    onLoad(option) {
      this.purchaseOrderId = option.purchaseOrderId
      this.processType = option.processType
      this.markFlag = option.markFlag
      this.setNavigationBarTitle()
    },
    onShow() {
      this.getList()
    },
    computed: {
    },
    methods: {
      setNavigationBarTitle() {
        let title = ''
        switch (this.processType) {
            case '1':
            title = '甄选'
            break
          case '2':
            title = '控槽'
            break
          case '3':
            title = '检疫'
            break
          case '4':
            title = '收购'
            break
          default:
            title = '默认标题'
        }
        uni.setNavigationBarTitle({
          title: title
        })
      },
      getList(val) {
        uni.showLoading({
          title: '加载中',
          icon: 'none',
        })
        let params = {}
        params = { ...val }
        // 采购计划ID
        farmersListByPurchaseOrderId({purchaseOrderId: this.purchaseOrderId}).then(res => {
          console.log(res)
          this.list = res.result
          this.list.length == 0 ? this.isEmpty = true : this.isEmpty = false
        })
        uni.hideLoading()
      },
      scrollToLower() {
        if (this.noMore) return
        this.pageNum++
      },
      bindrefresherrefresh() {
        this.refresherState = true
        this.pageNum = 1
        this.noMore = false
  
        setTimeout(() => {
          this.refresherState = false
          this.getList()
          this.$toast('刷新成功')
        }, 1000)
      },
  
      // 搜索
  
      addIntent() {
        uni.navigateTo({
          url: `/myPackge2/pages/catchingCows/checkCows/createCows?purchaseOrderId=${this.purchaseOrderId}`,
        })
      },
      resetSearch() {
        this.getList()
      },
      submitForm(val) {
        this.getList(val)
      },
    },
  }
  </script>
  
  <style lang="scss" scoped>
  @import '@/common/css/superviseHome.scss';
  </style>
  