<template>
  <view class="main">
    <view class="OptionOrder">
      <p style="width: 100%; height: 18rpx"></p>
      <ul>
        <li class="fill" style="display: flex; justify-content: center">
          <img style="width: 40rpx; height: 40rpx" src="../../../icon/one.png" alt="" />
          <!-- <img style="width: 401rpx; height: 4rpx; margin-top: 20rpx" v-if="!twoisTrue" src="../../../icon/line_1.png" alt="" /> -->
          <!-- <img style="width: 401rpx; height: 4rpx; margin-top: 20rpx" v-if="twoisTrue" src="../../../icon/line_21.png" alt="" /> -->
          <img style="width: 40rpx; height: 40rpx" v-if="!twoisTrue" src="../../../icon/two.png" alt="" />
          <img style="width: 40rpx; height: 40rpx" v-if="twoisTrue" src="../../../icon/twoline.png" alt="" />
        </li>
        <li class="info" style="display: flex; justify-content: space-around;">
          <!--  width: 66%; margin-left: 16.5% -->
          <text style="margin-right: 110rpx;">养殖户</text>
          <text :class="twoisTrue ? 'twoInfo' : 'oneInfo'">选牛</text>
        </li>
      </ul>
    </view>
    <!-- 养殖户 -->
    <view class="setupbox" v-if="!twoisTrue">
      <u-form ref="uForm" :model="form" :rules="rules" label-width="auto" :label-style="labelStyle">
        <u-form-item prop="farmersName" label="养殖户姓名" :required="true">
          <u-input v-model="form.farmersName" placeholder-style="text-align:right;color:#999;font-size: 26rpx;" :custom-style="customStyle" placeholder="请输入养殖户姓名" />
        </u-form-item>
        <u-form-item prop="farmersPhone" label="联系电话" :required="true">
          <u-input placeholder-style="text-align:right;color:#999;font-size: 26rpx;" :custom-style="customStyle" v-model="form.farmersPhone" placeholder="请输入联系电话" readonly />
        </u-form-item>
        <u-form-item prop="farmersIdcard" label="身份证号" :required="true">
          <u-input placeholder-style="text-align:right;color:#999;font-size: 26rpx;" :custom-style="customStyle" v-model="form.farmersIdcard" placeholder="请输入身份证号" />
        </u-form-item>
        <u-form-item prop="idcardFrontUrl" label="身份证正面照片" :border-bottom="!form.idcardFrontUrl.length" :required="true">
          <u-input placeholder=" " disabled />
          <img slot="right" style="width: 40rpx; height: 40rpx" @click="unloadImage('idcardFrontUrl')" src="../../../icon/photo.png" alt="" />
        </u-form-item>
        <view class="uploadImage" v-if="form.idcardFrontUrl.length">
          <view class="itemAlready" v-for="(item, index) in form.idcardFrontUrl" :key="index">
            <image mode="scaleToFill" :src="item" @click="previewImage(item)" />
            <view class="closeIcon" @click="deleteImage(index, 'idcardFrontUrl')"></view>
          </view>
        </view>
        <u-form-item prop="idcardBackUrl" label="身份证反面照片" :border-bottom="!form.idcardBackUrl.length" :required="true">
          <u-input placeholder=" " disabled />
          <img slot="right" style="width: 40rpx; height: 40rpx" @click="unloadImage('idcardBackUrl')" src="../../../icon/photo.png" alt="" />
        </u-form-item>
        <view class="uploadImage" v-if="form.idcardBackUrl.length">
          <view class="itemAlready" v-for="(item, index) in form.idcardBackUrl" :key="index">
            <image mode="scaleToFill" :src="item" @click="previewImage(item)" />
            <view class="closeIcon" @click="deleteImage(index, 'idcardBackUrl')"></view>
          </view>
        </view>
        <u-form-item prop="farmersAddress" label="养殖场地址" :required="true">
          <u-input v-model="form.farmersAddress" placeholder-style="text-align:right;color:#999;font-size: 26rpx;" :custom-style="customStyle" placeholder="请输入养殖场地址" />
        </u-form-item>
      </u-form>
    </view>

    <!-- 选牛 -->
    <view class="setupbox" v-if="twoisTrue">
      <!-- 新的表单内容 -->
      <u-form ref="uForm2" :model="form" :rules="rules2" label-width="auto" :label-style="labelStyle">
        <u-form-item prop="acceptorName" label="品控员" :required="true">
          <u-input placeholder-style="text-align:right;color:#999;font-size: 26rpx;" :custom-style="customStyle" v-model="form.acceptorName" placeholder="请输入品控员" disabled />
        </u-form-item>
        <u-form-item prop="sourceVideo" label="牛源视频(选填，最多3个)" :border-bottom="!form.sourceVideo.length">
          <u-input placeholder=" " disabled />
          <img slot="right" style="width: 40rpx; height: 40rpx" @click="uploadVideo('sourceVideo')" src="../../../icon/uploadVideo.png" alt="" />
        </u-form-item>
        <view class="uploadImage" v-if="form.sourceVideo.length">
          <view class="itemAlready" v-for="(item, index) in form.sourceVideo" :key="index">
            <video :src="item" @click="showVideoFn(item)" :controls="false"
                                :show-fullscreen-btn="false"
                                :show-play-btn="false"
                                :show-center-play-btn="false"></video>
            <view class="closeIcon" @click="deleteVideo(index, 'sourceVideo')"></view>
          </view>
        </view>
        <u-form-item prop="sourcePhoto" label="牛源照片" :border-bottom="!form.sourcePhoto.length" :required="true">
          <u-input placeholder=" " disabled />
          <img slot="right" style="width: 40rpx; height: 40rpx" @click="unloadImage('sourcePhoto')" src="../../../icon/photo.png" alt="" />
        </u-form-item>
        <view class="uploadImage" v-if="form.sourcePhoto.length">
          <view class="itemAlready" v-for="(item, index) in form.sourcePhoto" :key="index">
            <image mode="scaleToFill" :src="item" @click="previewImage(item)" />
            <view class="closeIcon" @click="deleteImage(index, 'sourcePhoto')"></view>
          </view>
        </view>
        <u-form-item prop="varietiesName" label="活畜品种" :required="true">
          <u-input placeholder-style="text-align:right;color:#999;font-size: 26rpx;" :custom-style="customStyle" v-model="form.varietiesName" placeholder="请输入活畜品种" disabled />
        </u-form-item>
        <u-form-item prop="categoryName" label="品种分类" :required="true">
          <u-input placeholder-style="text-align:right;color:#999;font-size: 26rpx;" :custom-style="customStyle" v-model="form.categoryName" placeholder="请输入品种分类" disabled />
        </u-form-item>
        <u-form-item prop="ageRange" label="活畜月龄" :required="true">
          <u-input placeholder-style="text-align:right;color:#999;font-size: 26rpx;" :custom-style="customStyle" v-model="form.ageRange" placeholder="请输入活畜月龄" disabled />
        </u-form-item>
        <u-form-item prop="weightRange" label="活畜重量" :required="true">
          <u-input placeholder-style="text-align:right;color:#999;font-size: 26rpx;" :custom-style="customStyle" v-model="form.weightRange" placeholder="请输入活畜重量" disabled />
        </u-form-item>
        <u-form-item prop="chooseNumber" label="活畜头数" :required="true">
          <u-input placeholder-style="text-align:right;color:#999;font-size: 26rpx;" :custom-style="customStyle" v-model="form.chooseNumber" placeholder="请输入活畜头数" />
        </u-form-item>
        <u-form-item prop="markNumber" label="牛源标记" :required="true">
          <u-input placeholder-style="text-align:right;color:#999;font-size: 26rpx;" :custom-style="customStyle" v-model="form.markNumber" placeholder="请输入" />
        </u-form-item>
      </u-form>
    </view>

    <view class="container-footer" :style="'padding:' + (isIphonex ? '40rpx 60rpx' : '15rpx 60rpx')">
      <u-button hover-class='none' v-if="!twoisTrue" shape="circle" @click="goNextStep" :disabled="isDisabled">下一步</u-button>
      <view style="display: flex; justify-content: space-around">
        <u-button hover-class='none' v-if="twoisTrue" shape="circle" @click="upfoot" size="medium">上一步</u-button>
        <u-button hover-class='none' v-if="twoisTrue" shape="circle" @click="submit" size="medium" :disabled="isSubmitDisabled">完成</u-button>
      </view>
    </view>
    <u-popup v-model="showVideo" mode="center" @close="showVideo = false">
			<view class="video-box" v-if="showVideo">
                <video :src="videoUrl" controls autoplay loop muted/>
            </view>
		</u-popup>
  </view>
</template>

<script>
import { uploadFiles } from '@/api/obsUpload/index'
import { purchaseOrderInfo } from '@/api/pages/purchaseOrder'
import { choosetCows } from '@/api/pages/catchingCows'
export default {
  components: {},
  onLoad(options) {},
  data() {
    return {
      isIphonex: getApp().globalData.systemInfo.isIphonex,
      twoisTrue: false,
      contentList: [],
      form: {
        farmersName: '',
        farmersPhone: '',
        farmersIdcard: '',
        idcardFrontUrl: [],
        idcardBackUrl: [],
        // form2
        acceptorName: '',
        varietiesName: '',
        categoryName: '',
        ageRange: '',
        weightRange: '',
        chooseNumber: '',
        markNumber: '',
        sourceVideo: [],
        sourcePhoto: []
      },
      form2: {
        // 新表单的字段
      },
      labelStyle: {
        fontSize: '26rpx',
        color: '#333',
      },
      customStyle: {
        textAlign: 'right',
      },
      placeholderStyle: 'text-align:right;color:#999;font-size: 26rpx;',
      rules: {
        farmersIdcard: [
          {
            required: true,
            message: '请输入身份证号',
            trigger: ['change', 'blur'],
          },
          {
            pattern: /^[1-9]\d{5}(18|19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}(\d|X)$/,
            message: '请输入有效的身份证号',
            trigger: ['change', 'blur'],
          },
        ],
        idcardFrontUrl: [
          {
            required: true,
            message: '请上传身份证正面照片',
            trigger: ['change', 'blur'],
          },
        ],
        idcardBackUrl: [
          {
            required: true,
            message: '请上传身份证反面照片',
            trigger: ['change', 'blur'],
          },
        ],
        farmersAddress: [
          {
            required: true,
            message: '请输入养殖场地址',
            trigger: ['blur'],
          },
        ],
      },
      rules2: {
        // 新表单的验证规则
        chooseNumber: [
          {
            required: true,
            message: '请输入活畜数量',
            trigger: ['blur'],
          },
          {
            pattern: /^[1-9]\d*$/,
            message: '请输入正确的数量',
            trigger: ['blur'],
          },
        ],
        markNumber: [
          {
            required: true,
            message: '请输入牛源标记',
            trigger: ['blur'],
          },
          {
            pattern: /^[1-9]\d*$/,
            message: '请输入正确的数量',
            trigger: ['blur'],
          },
        ],
      },
      purchaseOrderId: '',
      showVideo: false,
      videoUrl: '',
    }
  },
  onLoad(option) {
    console.log(option)
    this.purchaseOrderId = option.purchaseOrderId
    this.getPurchasePlanDetail(this.purchaseOrderId)
  },
  computed: {
    isDisabled() {
      return !this.form.farmersName || !this.form.farmersPhone || !this.form.idcardFrontUrl.length || !this.form.idcardBackUrl.length || !this.form.farmersAddress || !this.form.farmersIdcard
    },
    isSubmitDisabled() {
      return false
    },
  },
  methods: {
    submit() {
      const { 
        purchaseOrderId, 
        form: {
          farmersName, 
          farmersPhone, 
          farmersIdcard, 
          farmersAddress, 
          idcardFrontUrl, 
          idcardBackUrl, 
          sourceVideo, 
          sourcePhoto, 
          markNumber, 
          chooseNumber 
        } 
      } = this;
      const params = {
        purchaseOrderId,
        farmersName,
        farmersPhone,
        farmersIdcard,
        farmersAddress,
        idcardFrontUrl: idcardFrontUrl.join(','),
        idcardBackUrl: idcardBackUrl.join(','),
        sourceVideo: sourceVideo.join(','),
        sourcePhoto: sourcePhoto.join(','),
        markNumber,
        chooseNumber,
      };
      choosetCows(params).then(res => {
        if(res.code==200){
          this.$toast('操作成功')
          uni.navigateBack({
						delta: 1
					});
        }
      })
    },
    upfoot() {
      this.twoisTrue = false
    },
    goNextStep() {
      this.twoisTrue = true
    },
    unloadImage(type) {
      if (this.form[type].length >= 1) {
        uni.showToast({
          icon: 'none',
          title: '图片最多只能上传1张',
        })
        return
      }
      const that = this
      uni.chooseImage({
        count: 1,
        sizeType: ['original', 'compressed'],
        sourceType: ['album', 'camera'],
        name: 'file',
        success: function (res) {
          uploadFiles({
            filePath: res.tempFilePaths[0],
          }).then((data) => {
            that.form[type].push(data)
          })
        },
        fail(e) {},
      })
    },
    uploadVideo(type) {
      if (this.form[type].length >= 3) {
        uni.showToast({
          icon: 'none',
          title: '视频最多只能上传3个',
        })
        return
      }
      const that = this
      uni.chooseMedia({
        count: 1,
        sizeType: ['original', 'compressed'],
        sourceType: ['album', 'camera'],
        name: 'file',
        mediaType: ['video'],
        success: function (res) {
          uploadFiles({
            filePath: res.tempFiles[0].tempFilePath,
          }).then((data) => {
            that.form[type].push(data)
          })
        },
        fail(e) {},
      })
    },
    deleteVideo(index, type) {
      this.form[type].splice(index, 1)
      this.$forceUpdate()
    },
    previewImage(url) {
      uni.previewImage({
        urls: [url],
      })
    },
    deleteImage(index, type) {
      this.form[type].splice(index, 1)
      this.$forceUpdate()
    },
    // 查询采购计划详情
    getPurchasePlanDetail(purchaseOrderId) {
      purchaseOrderInfo({purchaseOrderId:purchaseOrderId}).then((res) => {
        console.log(res)
        this.form.acceptorName = res.result.acceptorName
        this.form.varietiesName = res.result.varietiesName
        this.form.categoryName = res.result.categoryName
        this.form.ageRange = res.result.ageRange
        this.form.weightRange = res.result.weightRange
      })
    },
    showVideoFn(item) {
        this.videoUrl = item
        this.showVideo = true
    }
  },
}
</script>
<style lang="scss" scoped>
/deep/ .placeholderClass {
  text-align: right;
  color: #bbbbbb;
  font-size: 28rpx;
}
.container-footer {
  width: 100%;
  position: fixed;
  bottom: 0;
  background-color: white;
  color: white;
  z-index: 1000;
  /deep/ .u-btn {
    color: white;
    background-color: #40CA8F;
  }

  /deep/ .u-btn--default--disabled {
    background-color: #abdcc7;
  }
}

.main {
  width: 100vw;
  height: 100vh;
  background-color: #f8f8ff;

  .uploadImage {
    display: flex;
    flex-wrap: wrap;
    position: relative;
    padding: 10rpx;
    padding-left: 0;

    .itemAlready {
      width: 140rpx;
      height: 140rpx;
      border-radius: 8rpx;
      position: relative;
      margin: 0 20rpx 10rpx 0rpx;

      image {
        width: 100%;
        height: 100%;
        border-radius: 8rpx;
      }
      video {
				width: 100%;
				height: 100%;
				border-radius: 8rpx;
			}
      .closeIcon {
        width: 32rpx;
        height: 32rpx;
        background-image: url('@/static/modalImg/error.png');
        position: absolute;
        background-size: cover;
        top: -10rpx;
        right: -10rpx;
      }
    }

    .item {
      width: 140rpx;
      height: 140rpx;
      border-radius: 8rpx;
      position: relative;
      border: 2rpx dashed #d8d8d8;

      .uploadIcon {
        width: 100%;
        height: 120rpx;
        display: flex;
        justify-content: center;
        align-items: flex-end;
        font-size: 24rpx;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weightRange: 400;
        color: #d8d8d8;
        background: url('https://fengtaohui.oss-cn-beijing.aliyuncs.com/uploads/image/20230105/6400955ab08a2f639e9790b815c418db63b666a9d7e40.png') no-repeat;
        background-size: 20rpx 20rpx;
        background-position: center 30rpx;
        position: absolute;
        top: 0;
        left: 0;
        bottom: 0;
        right: 0;
        margin: auto;
        z-index: 5;
      }
    }
  }

  .container-content {
    margin: 20rpx;
    margin-top: 0;
    background-color: #fff;
    border-radius: 15rpx 15rpx 15rpx 15rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 43rpx 33rpx 36rpx 29rpx;

    &-left {
      &-title {
        color: #333;
        font-size: 32rpx;
        font-weightRange: 400;
        font-family: PingFang SC-Bold;
        padding-bottom: 21rpx;
      }

      &-time {
        font-weightRange: 400;
        color: #999;
        font-family: PingFang SC-Medium;
        font-size: 24rpx;
      }

      &-price {
        padding-top: 44rpx;

        text:first-child {
          font-weightRange: 400;
          color: #878d96;
          font-family: PingFang SC-Medium;
          font-size: 28rpx;
        }

        text:last-child {
          font-weightRange: 400;
          color: #333333;
          font-family: PingFang SC-Medium;
          font-size: 28rpx;
        }
      }
    }
  }
}

.OptionOrder {
  background: #ffffff;
  height: 120rpx;
  margin-bottom: 20rpx;
  ul {
    li {
      display: flex;
    }
  }
}

.info {
  font-size: 22rpx;
  font-family: PingFang SC-Medium, PingFang SC;
  font-weightRange: 400;
  color: #40CA8F;
  line-height: 26rpx;
}

.fill {
  margin-top: 18rpx;
  margin-bottom: 18rpx;
}

.oneInfo {
  color: #bbbbbb;
}

.twoInfo {
  color: #40CA8F;
}

/deep/.u-radio-group {
  display: flex;
  justify-content: space-between;
}

/deep/.u-form-left__content__label data-v-5e7216f1 {
  margin-left: 28rpx;
}

.titHtwo {
  display: flex;
  justify-content: space-around;
}

.buttons {
  width: 150rpx;
  height: 60rpx;
  border-radius: 30rpx;
  background-color: #f4f4f4;
  color: #999999;
  text-align: center;
  line-height: 60rpx;
}

.checked {
  width: 150rpx;
  height: 60rpx;
  border-radius: 30rpx;
  background-color: #f4f4f4;
  color: #5670fe;
  text-align: center;
  line-height: 60rpx;
  border: 1rpx solid #5670fe;
}

.infos {
  // margin-top: 38rpx;
  margin-bottom: 10rpx;
  margin-left: 5rpx;
  font-size: 32rpx;
  font-family: PingFang SC, PingFang SC;
  font-weight: normal;
  color: #222222;
}
.setupbox {
  background: #fff;
  padding: 30rpx;
  overflow: auto;
  height: 1200rpx;
}
</style>
