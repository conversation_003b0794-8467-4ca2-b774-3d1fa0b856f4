

/* 行为相关颜色 */
$gx-color-primary: #007aff;
$gx-color-success: #4cd964;
$gx-color-warning: #f0ad4e;
$gx-color-error: #dd524d;

/* 文字基本颜色 */
$gx-text-color:#333;//基本色
$gx-text-color-grey6:#666;
$gx-text-color-grey9:#999;//辅助灰色，如加载更多的提示信息
$gx-text-color-inverse:#fff;//反色
$gx-text-color-placeholder: #808080;
$gx-text-color-disable:#c0c0c0;
$gx-text-color-theme:#E41F1B;

/* 背景颜色 */
$gx-bg-color:#F7F7F7;//主题色
$gx-bg-color-grey:#F6F6F6;
$gx-bg-color-hover:#f1f1f1;//点击状态颜色
$gx-bg-color-mask:rgba(0, 0, 0, 0.4);//遮罩颜色

/* 边框颜色 */
$gx-border-color:#eee;


/* 文字尺寸 */
$gx-font-size-sm:22rpx;
$gx-font-size-base:24rpx;
$gx-font-size-lg:26rpx;
$gx-font-size-bg:28rpx;
$gx-font-size-obg:30rpx;
$gx-font-size-tbg:40rpx;

/* Border Radius */
$gx-border-radius-sm: 4rpx;
$gx-border-radius-base: 8rpx;
$gx-border-radius-lg: 10rpx;
$gx-border-radius-circle: 50%;



/* 透明度 */
$gx-opacity-disabled: 0.3; // 组件禁用态的透明度

