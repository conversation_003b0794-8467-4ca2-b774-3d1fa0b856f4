.searchs {
    width: 100%;
    background: #FFFFFF;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content:  flex-end;
    padding:13rpx 25rpx 13rpx 30rpx;
    .search {
        width: 599rpx;
        height: 60rpx;
        background: #F6F7FB;
        border-radius: 30rpx;
    }
    .fifter {
        width: 40rpx;
        height: 40rpx;
        img {
            width: 100%;
            height: 100%;
        }
    }

}
/deep/ u-avatar {
    view {
        color: #fff !important;
    }
}
section {
    box-sizing: border-box;
    padding: 20rpx 0 0;
}

.list-section {
    display: flex;
    background: #FFFFFF;
    margin: 0rpx 20rpx 0rpx 20rpx;
    border-radius: 20rpx;
     // justify-content: space-between;
    box-sizing: border-box;
    // padding:0 10rpx 0 20rpx;
    
    .left {
        margin: 35rpx 0;
        width: 60rpx;
        height: 60rpx;
    }
    .middle {
        width: 100%;
        padding: 20rpx;
        .title {
            width: 90%;
            font-size: 32rpx;
            font-family: PingFang SC, PingFang SC;
            font-weight: 400;
            color: #222222;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
            margin-bottom: 26rpx;
        }
        .extension {
            border-radius: 3rpx  3rpx  3rpx  3rpx ;
            border: 2rpx solid #F85300;
            width: 58rpx;
            height: 30rpx;
            line-height: 30rpx;
            text-align: center;
            color: #F85300;
            background: #FFE2D3;
            font-family: PingFang SC-Medium;
            font-size: 20rpx;
            // margin-left: 20rpx;
            margin-bottom: 26rpx;
        }
        .top-explain {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        .continuous {
            border-radius: 3rpx  3rpx  3rpx  3rpx ;
            border: 2rpx solid #12AE63;
            width: 58rpx;
            height: 30rpx;
            line-height: 30rpx;
            text-align: center;
            color: #12AE63;
            background: #E4FFF2;
            font-family: PingFang SC-Medium;
            font-size: 20rpx;
            margin-left: 20rpx;
            margin-bottom: 26rpx;
        }
    }
    .num-explain {
        font-size: 24rpx;
        color: #999999;
        width: 100%;
        display: flex;
        justify-content: space-between;
        font-family: PingFang SC-Medium;
        p {
            white-space: nowrap;
        }
    }
    .list-item {
        width: 100%;
        font-size: 28rpx;
        color: #999999;
        p {
            white-space: nowrap;
            line-height: 60rpx;
        }
    }
    .right {
        margin-top: 32rpx;
        margin-left: 18rpx;
        img {
            width: 30rpx;
            height: 30rpx;
        }
    }

}

.footer {
    width: 100%;
    background: #FFFFFF;
    font-family: PingFang SC-Medium;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-sizing: border-box;
    padding: 21rpx 30rpx;
}

.tab-list {
    width: 100%;
	padding: 20rpx 0 20rpx;
	white-space: nowrap;
	box-sizing: border-box;
	.active{
		border: 2rpx solid #40CA8F;
		color: #40CA8F !important;
	}
    ::-webkit-scrollbar{
        display: none;
        width: 0 !important;
        height: 0 !important;
        -webkit-appearance: none;
        background: transparent;
    }
}
.tab-list .item {
    display: inline-block;
    vertical-align: top;
    margin-left: 20rpx;
	padding:17rpx 25rpx;
	font-size: 28rpx;
	font-family: PingFang SC, PingFang SC;
	font-weight: 400;
	color: #999999;
	border-radius: 40rpx;
	background: #ffffff;

}
  
.tab-list .item:last-of-type {
    margin-right: 20rpx;
}
// .list-before:first-child {
//     margin: 0rpx 20rpx 0rpx 20rpx;
// } 
.list-before-common {
    margin: 0rpx 20rpx 20rpx 20rpx;
    background: #FFFFFF;
    box-sizing: border-box;
    display: flex;
    border-radius: 20rpx;
    padding:0 20rpx 0 20rpx;
    .left {
        margin: 35rpx 19rpx 0 0;
        width: 60rpx;
        height: 60rpx;
    }
    .middle {

        .title {
            // width: 90%;
            font-size: 32rpx;
            font-family: PingFang SC, PingFang SC;
            font-weight: 400;
            color: #222222;
            margin: 30rpx 0 20rpx;
            // white-space: nowrap;
            // text-overflow: ellipsis;
            // overflow: hidden;
        }
        .row {
            font-size: 24rpx;
            font-family: PingFang SC, PingFang SC;
            font-weight: 400;
            color: #999999;
            padding: 0 0 20rpx;
        }
        .row:last-child {
            padding: 0 0 31rpx;
        }
    }
}
.list-before {
    margin: 0rpx 20rpx 20rpx 20rpx;
    background: #FFFFFF;
    box-sizing: border-box;
    display: flex;
    // justify-content: space-between;
    border-radius: 20rpx;
    padding:0 20rpx 0 20rpx;
    .left {
        margin: 35rpx 0rpx 0 0;
        width: 60rpx;
        height: 60rpx;
    }
    .middle {
        // width: 61%;
		flex: 1;
		padding-left: 30rpx;
		width: 0;
		white-space: nowrap;
        .title {
            width: 90%;
            font-size: 32rpx;
            font-family: PingFang SC, PingFang SC;
            font-weight: 400;
            color: #222222;
            margin: 30rpx 0 20rpx;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
        }
        .row {
            font-size: 24rpx;
            font-family: PingFang SC, PingFang SC;
            font-weight: 400;
            color: #999999;
            padding: 0 0 20rpx;
        }
        .row:last-child {
            padding: 0 0 31rpx;
        }
    }
    .right {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        padding: 0 0 31rpx;
        .common {
            font-size: 24rpx;
            font-family: PingFang SC, PingFang SC;
            font-weight: 400;
            color: #999999;
            margin: 25rpx 0 45rpx;
            text-align: right;
        }
        .btn-info {
            display: flex;
            justify-content: flex-end;
        }
    }
    .other-right {
        padding: 0 0 31rpx;
    }
    .common {
        font-size: 24rpx;
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        color: #999999;
        margin: 25rpx 0 45rpx;
        text-align: right;
        // padding: 20rpx 0 45rpx;
    }
    .btn {
        // width: 120rpx;
        height: 50rpx;
        line-height: 50rpx;
        text-align: center;
        background: #40CA8F;
        border-radius: 25rpx;
        font-size: 28rpx;
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        color: #FFFFFF;
        padding: 0 20rpx;
    }
    .greentips {
        font-size: 24rpx;
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        color: #12AE63;
        margin: 25rpx 0 0rpx;
        text-align: right;
    }
    .reject {
        font-size: 24rpx;
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        color: #F85300;
        text-align: right;
        margin: 25rpx 0 0rpx;
    }
}
// .list-before-other:first-child {
//     margin: 0rpx 20rpx 0rpx 20rpx;
// } 
.list-before-other {
    margin: 0rpx 20rpx 20rpx 20rpx;
    background: #FFFFFF;
    box-sizing: border-box;
    display: flex;
    border-radius: 20rpx;
    padding:0 20rpx 0 20rpx;
    display: flex;
    justify-content: space-between;
    .left {
        .title1 {
            font-size: 32rpx;
            font-family: PingFang SC, PingFang SC;
            font-weight: 400;
            color: #222222;
            margin: 25rpx 0 20rpx;
        }
        .title {
            width: 450rpx;
            // white-space: nowrap;
            // text-overflow: ellipsis;
            // overflow: hidden;
            font-size: 32rpx;
            font-family: PingFang SC, PingFang SC;
            font-weight: 400;
            color: #222222;
            margin: 25rpx 0 20rpx;
        }
        .row {
            font-size: 24rpx;
            font-family: PingFang SC, PingFang SC;
            font-weight: 400;
            color: #999999;
            padding: 0 0 20rpx;
        }
        .row:last-child {
            padding: 0 0 30rpx;
        }
    }
    .right {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        padding: 0 0 30rpx;
        .greentips {
            font-size: 24rpx;
            font-family: PingFang SC, PingFang SC;
            font-weight: 400;
            color: #12AE63;
            margin: 25rpx 0 25rpx;
            text-align: right;
        }
        .common {
            font-size: 24rpx;
            font-family: PingFang SC, PingFang SC;
            font-weight: 400;
            color: #999999;
            margin: 25rpx 0 45rpx;
            text-align: right;
        }
        .btn-info {
            display: flex;
            justify-content: flex-end;
        }
        .btn {
            width: 120rpx;
            height: 50rpx;
            line-height: 50rpx;
            text-align: center;
            background: #40CA8F;
            border-radius: 25rpx;
            font-size: 28rpx;
            font-family: PingFang SC, PingFang SC;
            font-weight: 400;
            color: #FFFFFF;
        }
        .reject {
            font-size: 24rpx;
            font-family: PingFang SC, PingFang SC;
            font-weight: 400;
            color: #F85300;
            text-align: right;

        }
    }
   
}
.red-dot {
    display: inline-block;
    width: 15rpx;
    height: 15rpx;
    background-color: red;
    border-radius: 50%; 
    margin: 0rpx 0rpx 0 5rpx;
    top: -5rpx;
    position: relative;
}
.Add {
    width: 100rpx;
    height: 100rpx;
    position: fixed;
    right: 49rpx;
    bottom: 199rpx;
    img {
        width: 100rpx;
        height: 100rpx;
        border-radius: 50%;
    }
}
