.searchs_section{
    width: 100%;
    padding: 0 30rpx;
    .search_box{
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    .search{
        flex: 1;
    }
    .fifter{ 
        margin-left: 30rpx;
        width: 34rpx;
        height: 32.5rpx;
        img{
            width: 34rpx;
            height: 32.5rpx;
        }
    }
}

.items{
    padding: 0 30rpx;
    margin-bottom: 30rpx;
}
.item_title{
    height: 56rpx;
    background: linear-gradient( 260deg, #60D26F 0%, #1CC271 100%);
    line-height: 56rpx;
    padding-left: 26rpx;
    font-weight: 500;
    font-size: 28rpx;
    color: #FFFFFF;
    border-radius: 20rpx 20rpx 0 0;
}
.item_content{
    background: #FFFFFF;
    padding: 25rpx 25rpx 20rpx 25rpx;
    font-weight: 400;
    font-size: 26rpx;
    line-height: 38rpx;
    color: #999999;
    .item{
        margin-bottom: 16rpx;
    }
    .text{
        color: #333333;
    }
    .file_box{
        display: flex;
    }
    .file_name{
        color: #EA501E;
    }
}
.list_btn_items{
    height: 82rpx;
    display: flex;
    align-items: center;
    border-radius: 0 0  20rpx 20rpx;
    padding: 0 20rpx;
    background: #FFFFFF;
    .btn_section{
        width: 100%;;
        border-top: 1rpx solid #F0F0F0;
        padding: 20rpx 0;
        display: flex;
    }
    .btn_item{
        display: flex;
        align-items: center;
        font-weight: 500;
        font-size: 28rpx;
        color: #1DB17A;
        margin-right: 46rpx;
        &:last-child{
            margin-right: 0;
        }
    }
    img, image{
        width: 24.5rpx;
        height: 28rpx;
        margin-right: 13rpx;
    }
}



.item_title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 25rpx !important;
    font-weight: 500;

    .title {
        flex: 1;
    }

    .status {
        color: #FFFFFF;
        font-size: 28rpx;
        font-weight: 500;
        padding: 4rpx 12rpx;
        border-radius: 12rpx;
    }
}