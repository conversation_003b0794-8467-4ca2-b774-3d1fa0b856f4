.submit-button-box{
	padding: 20rpx 40rpx;
	box-sizing: border-box;
}
.submit-button-bg{
	$height:80rpx;
	background: $gx-bg-color; 
	height: $height;
	line-height: $height;
	width: 100%;
	text-align: center;
	font-size: 28rpx;
	color: $gx-text-color-inverse;
	border-radius: 100rpx;
	border: none;
}
.submit-button-bg:active{
	opacity: 0.6;
}
.initTabs {
	.u-tab-bar {
		transform: translate(20px, -100%) !important;
	}
}
.submit-buttonpd{
	padding: 0 30rpx;
}
button::after{
	border: none;
}
.btn-hover{
	opacity: 0.6;
}
.them-color{
	color: $gx-text-color-theme !important;
}

.placeholder{
	font-size: $gx-font-size-lg;
	color: $gx-text-color-grey9;
}
  .pad {
    padding: 0 30rpx;
  }

  .b {
    display: block;
  }
  .ellipsis{
	 overflow: hidden;
	 text-overflow: ellipsis;
	 display: -webkit-box; 
	 -webkit-box-orient: vertical;
  }
  
  .ellipsis1 {
    @extend .ellipsis;
    -webkit-line-clamp: 1;
  
  }
  
  .ellipsis2 {
	   @extend .ellipsis;
    -webkit-line-clamp: 2;
  }
  
  .clear {
    clear: both;
  }
  
  
  .zw {
    height: 150rpx;
    width: 100%;
  }
  .subFixed {
    position: fixed;
    bottom: 30rpx;
    left: 0;
    right: 0;
	z-index: 2;
  }
  .pad-both{
	   box-sizing: border-box;
	   padding-left: 30rpx;
	   padding-right: 30rpx;
	     padding-bottom: 40rpx;
  }
  .mag-both{
	  margin-left: 30rpx;
	  margin-right: 30rpx;
	
  }
.post{
  margin-top: 50rpx;
}
.post-btn-top{
  margin-top: 20rpx;
}
.submit-border-top{
	border-top: 1rpx solid $gx-border-color;
}

// 导航栏  start
.navbar-slot-left{
	padding-left:30rpx;
}
// 导航栏  end

	.isClickBg{
		background:$gx-bg-color;
	}
	.noClickBg{
		background: $gx-text-color-disable;
	}