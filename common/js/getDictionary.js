import store from '@/store/index'
import {goodsLables} from '@/api/common'
let dictionaryFn = function() {
	let code = "['province','union_type','voucher_type','oil_type','pick_up_goods_method','SJHM','specs','shopmenu_integral']"; 
	return new Promise((resolve, reject) => {
		goodsLables({
			codes: code
		}).then(res => {
			if (res.success) {
				resolve(res);
				store.commit('common/UPDATE_DICTION', res.data);
			} else {
				uni.showToast({
					title: "获取数据字典异常",
					duration: "5000",
					icon: "none"
				})
				resolve('')
			}
		})
	})
}
module.exports = {
	dictionaryFn
}
