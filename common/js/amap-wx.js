var server = 'https://restapi.amap.com/v3/';
const amap = {};
const key = 'a735426bdcf9df99c69843ea6e0f1486';
//获取规划路线
amap.direction = function(param) {
  return request(server + 'direction/driving', param, "GET").then(res => res.data);
}

//坐标转换
amap.regeo = function(param) {
  return request(server + 'geocode/regeo', param, "GET").then(res => res.data);
}

function request(url, data, method) {
  return new Promise((resolve, reject) => {
    data.key = key;
    uni.request({
      url: url,
      method: (method || 'GET').toUpperCase(),
      data: data || {},
      header: {
        "content-type": "application/json"
      },
      complete: (res) => {
        if (res.data.status=='1') {
          resolve(res)
        } else {
          uni.showModal({
            content: res.data.info,
            success: (res) => {}
          })
          resolve('no')
        }
      }
    })
  })
}


export default amap
