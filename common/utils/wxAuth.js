import activity from '@/api/activity.js'
import utils from '@/common/js/utils.js'
import prodConfig from '@/config/prod.env.js'

let authLogin = async function () {
  if (getApp().userInfo().mobile && uni.getStorageSync('pay') == 'yes') {
    uni.reLaunch({
      url: '/pages/main/index/index',
    })
    uni.setStorageSync('pay', '')
    return
  }
  let code = ''
  if (getApp().isWeiXin()) {
    code = await wxAuthorize()
    if (code == '') return
  }
  let loginUserMsg = await byMobileLogin(code)
  return loginUserMsg
}
// 微信公众号授权
function wxAuthorize() {
  return new Promise((resolve, reject) => {
    let code = utils.getUrlParam()
    if (code) {
      resolve(code)
      uni.setStorageSync('wxCode', code)
      return
    } else {
      var url = location.search
      let mobile = uni.getStorageSync('paramsMobile')
      let appid = getApp().globalData.appid
      let scope = 'snsapi_userinfo' //snsapi_base静默;snsapi_userinfo:用户手动同意
      let redirectUrl = encodeURIComponent('http://app.guanyu-tech.com/cjxbank/#/pages/main/index/index?mobile=' + mobile)
      if (prodConfig.BASE_URL.includes('test')) {
        redirectUrl = encodeURIComponent('https://test.app.guanyu-tech.com/cjxbank/#/pages/main/index/index?mobile=' + mobile)
      }
      let authURL = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${appid}&redirect_uri=${redirectUrl}&response_type=code&scope=${scope}&mobile=${mobile}&connect_redirect=1#wechat_redirect`
      window.location.href = authURL
    }
  })
}

//自动登录
function byMobileLogin(code) {
  uni.showLoading({
    title: '登录中...',
    mask: true,
  })
  var mobile = uni.getStorageSync('paramsMobile')
  if (!mobile) {
    uni.showModal({
      title: '提示',
      showCancel: false,
      content: '登录失败，未获取到用户手机号',
      success: function (res) {},
    })
    return
  }
  return new Promise((resolve, reject) => {
    activity
      .byMobileLogin({
        data: decodeURIComponent(mobile),
        code: code,
      })
      .then((res) => {
        uni.hideLoading()
        if (res && res.success) {
          uni.setStorageSync('loginUserMsg', JSON.stringify(res.data))
          uni.reLaunch({
            url: '/pages/main/index/index',
          })
          resolve(res)
        } else {
          if (res.message != '未获得public_open_id') {
            uni.showModal({
              title: '提示',
              showCancel: false,
              confirmColor: '#ff8f2f',
              content: '' + res.message,
              success: function (res) {
                window.location.href = getApp().globalData.otherUrl
              },
            })
          }
          reject(res)
        }
      })
  })
}
export { authLogin }
