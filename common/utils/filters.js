import config from '../../config/index'
import store from '../../store/index'

//处理货币：整数和小数部分分开显示
let currencyStyle = (value, style) => {
  //  货币格式
  /**
   * style: int价格的整数部分，dem:价格的小数部分
   */
  if (value) {
    var num = value.toString().split('.')
    if (style == 'int') {
      return num[0]
    } else {
      if (num.length >= 2) {
        let vala = num[1].length >= 2 ? num[1] : num[1] + '0'
        return vala.toString().substring(0, 2)
      } else {
        return '00'
      }
    }
  } else {
    let val = style == 'int' ? '0' : '00'
    return val
  }
}

//处理货币保留2位小数
let toFixed2 = (value) => {
  let realVal = '0.00'
  if (value) {
    // 截取当前数据到小数点后三位
    let tempVal = parseFloat(value).toFixed(3) + ''

    realVal = tempVal.substring(0, tempVal.length - 1)
  }
  return realVal
}

//处理图片返回路径
let imgUrl = (value) => {
  if (value) {
    return config.initEnv().BASE_URL + '/cloud-system-tools/tools/fastdfs/service/getImg?params=' + value
  } else {
    return ''
  }
}
//券码保留前三后6为
let getCode = (value) => {
  if (value) {
    return value.substring(0, 4) + '…' + value.substring(value.length - 8, value.length)
  } else {
    return ''
  }
}

//格林威治时间(2024-11-06T17:40:18.000+0800)转正常格式并兼容ios 安卓
let getData = (time, isTime = 'no') => {
  if (time) {
    var date = time.substr(0, 10) //年月日
    var hours = time.substring(11, 13)
    var minutes = time.substring(14, 16)
    var seconds = time.substring(17, 19)
    var timeFlag = date + ' ' + hours + ':' + minutes + ':' + seconds
    timeFlag = timeFlag.replace(/-/g, '/')
    timeFlag = new Date(timeFlag)
    timeFlag = new Date(timeFlag.getTime()) //标准时间:  timeFlag = new Date(timeFlag.getTime() + 0 * 3600 * 1000)
    if (isTime == 'yes') {
      timeFlag = timeFlag.getFullYear() + '-' + (timeFlag.getMonth() + 1 < 10 ? '0' + (timeFlag.getMonth() + 1) : timeFlag.getMonth() + 1) + '-' + (timeFlag.getDate() < 10 ? '0' + timeFlag.getDate() : timeFlag.getDate())
    } else {
      timeFlag =
        timeFlag.getFullYear() +
        '-' +
        (timeFlag.getMonth() + 1 < 10 ? '0' + (timeFlag.getMonth() + 1) : timeFlag.getMonth() + 1) +
        '-' +
        (timeFlag.getDate() < 10 ? '0' + timeFlag.getDate() : timeFlag.getDate()) +
        ' ' +
        (timeFlag.getHours() < 10 ? '0' + timeFlag.getHours() : timeFlag.getHours()) +
        ':' +
        (timeFlag.getMinutes() < 10 ? '0' + timeFlag.getMinutes() : timeFlag.getMinutes()) +
        ':' +
        (timeFlag.getSeconds() < 10 ? '0' + timeFlag.getSeconds() : timeFlag.getSeconds())
    }
    return timeFlag
  }
}

/**
 * 根据code返回数据字典的名称
 * @param {String} lablesCode  数据code
 * @param {String} typeFn  要取的字典名称，如：  'lables','province'
 * @return {Object}  数据字典名称
 */

let get_lables_name = (lablesCode, typeFn) => {
  console.log(88888, store.getters)
  if (lablesCode != '' && lablesCode != undefined && lablesCode) {
    let dictionaryList = store.getters.dictionaryList
    let dataArr = dictionaryList[typeFn]
    for (var i = 0; i < dataArr.length; i++) {
      if (lablesCode == dataArr[i].code) {
        return dataArr[i].name
      }
    }
  }
}

let getOilTypeName = (cellValue) => {
  let oilType1 = store.getters.dictionaryList.oil_type
  for (let i in oilType1) {
    var oilType2 = oilType1[i].children
    for (let j in oilType2) {
      if (cellValue == oilType2[j].code) {
        return oilType1[i].name + oilType2[j].name
      }
    }
  }
}

//手机号处理
let formatPhone = (p) => {
  if (p) {
    return p.substring(0, 3) + '****' + p.substring(p.length - 4)
  }
}
//js  金额转换万
// 数字转整数 如 100000 转为10万
// @param {需要转化的数} num
// @param {需要保留的小数位数} point
let tranNumber = (num, point = 2) => {
  if (num == '' || num == null) {
    return '0.00'
  }
  let numStr = num.toString()
  let numStrLng = numStr.split('.')[0]
  // 十万以内直接返回
  if (numStrLng.length < 6) {
    let nm = parseFloat(numStr).toFixed(2)
    return nm
  }
  //大于8位数是亿
  else if (numStrLng.length > 8) {
    let decimal = numStr.substring(numStrLng.length - 8, numStrLng.length - 8 + point)
    return parseFloat(parseInt(num / 100000000) + '.' + decimal) + '亿'
  }
  //大于6位数是十万 (以10W分割 10W以下全部显示)
  else if (numStrLng.length > 5) {
    let decimal = numStr.substring(numStrLng.length - 4, numStrLng.length - 4 + point)
    return parseFloat(parseInt(num / 10000) + '.' + decimal) + '万'
  }
}

export { currencyStyle, imgUrl, get_lables_name, getData, formatPhone, tranNumber, toFixed2, getCode, getOilTypeName }
