let class2type = {}
let toString = class2type.toString
let hasOwn = class2type.hasOwnProperty

let utils = {}

utils.isArray = Array.isArray

utils.type = function (obj) {
  if (obj == null) {
    return obj + ''
  }
  return typeof obj === 'object' || typeof obj === 'function' ? class2type[toString.call(obj)] || 'object' : typeof obj
}

utils.isFunction = function () {
  return this.type(obj) === 'function'
}

utils.isPlainObject = function (obj) {
  if (this.type(obj) !== 'object') {
    return false
  }

  if (obj.constructor && !hasOwn.call(obj.constructor.prototype, 'isPrototypeOf')) {
    return false
  }
  return true
}

utils.extend = function () {
  let _this = this
  var options,
    name,
    src,
    copy,
    copyIsArray,
    clone,
    target = arguments[0] || {},
    i = 1,
    length = arguments.length,
    deep = false

  // Handle a deep copy situation
  if (typeof target === 'boolean') {
    deep = target

    // skip the boolean and the target
    target = arguments[i] || {}
    i++
  }

  // Handle case when target is a string or something (possible in deep copy)
  if (typeof target !== 'object' && _this.isFunction(target)) {
    target = {}
  }

  if (i === length) {
    target = {}
    i--
  }

  for (; i < length; i++) {
    // Only deal with non-null/undefined values
    if ((options = arguments[i]) != null) {
      // Extend the base object
      for (name in options) {
        src = target[name]
        copy = options[name]

        // Prevent never-ending loop
        if (target === copy) {
          continue
        }

        // Recurse if we're merging plain objects or arrays
        if (deep && copy && (_this.isPlainObject(copy) || (copyIsArray = _this.isArray(copy)))) {
          if (copyIsArray) {
            copyIsArray = false
            clone = src && _this.isArray(src) ? src : []
          } else {
            clone = src && _this.isPlainObject(src) ? src : {}
          }

          // Never move original objects, clone them
          target[name] = _this.extend(deep, clone, copy)

          // Don't bring in undefined values
        } else if (copy !== undefined) {
          target[name] = copy
        }
      }
    }
  }

  // Return the modified object
  return target
}

utils.packageDefaultInfo = function () {
  var result = {}
  if (uni.getStorageSync('userid')) {
    result.userid = uni.getStorageSync('userid')
  }
  if (uni.getStorageSync('username')) {
    result.username = uni.getStorageSync('username')
  }
  if (uni.getStorageSync('orgname')) {
    result.orgname = encodeURI(uni.getStorageSync('orgname'))
  }
  if (uni.getStorageSync('orgcode')) {
    result.orgcode = encodeURI(uni.getStorageSync('orgcode'))
  }
  if (uni.getStorageSync('longitude')) {
    result.longitude = uni.getStorageSync('longitude')
  }
  if (uni.getStorageSync('latitude')) {
    result.latitude = uni.getStorageSync('latitude')
  }
  if (uni.getStorageSync('address')) {
    result.address = encodeURI(uni.getStorageSync('address'))
  }
  return result
}

// 截取url参数coe
utils.getUrlParam = function () {
  let url = window.location.href
  console.log(url)
  let ab = url.match(/code(\S*)&/)
  if (ab) {
    return ab[1].replace('=', '')
  }
  return ''
}

/**
 * 删除当前url中指定参数
 * @param names 数组或字符串
 * @returns {string}
 */
utils.funcUrlDel = function (names) {
  if (typeof names == 'string') {
    names = [names]
  }
  var loca = window.location
  var obj = {}
  var arr = loca.search.substr(1).split('&')
  //获取参数转换为object
  for (var i = 0; i < arr.length; i++) {
    arr[i] = arr[i].split('=')
    obj[arr[i][0]] = arr[i][1]
  }
  //删除指定参数
  for (var i = 0; i < names.length; i++) {
    delete obj[names[i]]
  }
  //重新拼接url
  var url =
    loca.origin +
    loca.pathname +
    '?' +
    JSON.stringify(obj)
      .replace(/[\"\{\}]/g, '')
      .replace(/\:/g, '=')
      .replace(/\,/g, '&')
  return url
}

export default utils
