{"_from": "js-md5", "_id": "js-md5@0.7.3", "_inBundle": false, "_integrity": "sha512-ZC41vPSTLKGwIRjqDh8DfXoCrdQIyBgspJVPXHBGu4nZlAEvG3nf+jO9avM9RmLiGakg7vz974ms99nEV0tmTQ==", "_location": "/js-md5", "_phantomChildren": {}, "_requested": {"type": "tag", "registry": true, "raw": "js-md5", "name": "js-md5", "escapedName": "js-md5", "rawSpec": "", "saveSpec": null, "fetchSpec": "latest"}, "_requiredBy": ["#DEV:/", "#USER"], "_resolved": "https://registry.npmjs.org/js-md5/-/js-md5-0.7.3.tgz", "_shasum": "b4f2fbb0b327455f598d6727e38ec272cd09c3f2", "_spec": "js-md5", "_where": "C:\\Users\\<USER>\\Documents\\HBuilderProjects\\test-npm", "author": {"name": "<PERSON>, Yi-<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/emn178/js-md5/issues"}, "bundleDependencies": false, "deprecated": false, "description": "A simple MD5 hash function for JavaScript supports UTF-8 encoding.", "devDependencies": {"expect.js": "~0.3.1", "jsdoc": "^3.4.0", "mocha": "~2.3.4", "nyc": "^11.3.0", "requirejs": "^2.1.22", "uglify-js": "^3.1.9", "webworker-threads": "^0.7.11"}, "homepage": "https://github.com/emn178/js-md5", "keywords": ["md5", "hash", "encryption", "cryptography", "HMAC"], "license": "MIT", "main": "src/md5.js", "name": "js-md5", "nyc": {"exclude": ["tests"]}, "repository": {"type": "git", "url": "git+https://github.com/emn178/js-md5.git"}, "scripts": {"build": "npm run-script compress;npm run-script doc", "compress": "uglifyjs src/md5.js -c -m eval --comments --output build/md5.min.js", "coveralls": "nyc report --reporter=text-lcov | coveralls", "doc": "rm -rf doc;jsdoc src README.md -d doc", "report": "nyc --reporter=html --reporter=text mocha tests/node-test.js", "test": "nyc mocha tests/node-test.js"}, "version": "0.7.3"}