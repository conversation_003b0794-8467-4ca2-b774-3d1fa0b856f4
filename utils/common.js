export function  formatTime(value) {
     //value要转化的标准时间
     const dateTme = new Date(value)
     const Y = dateTme.getFullYear()
     const M = dateTme.getMonth() + 1 < 10 ? "0" + (dateTme.getMonth() + 1) : dateTme.getMonth() + 1
     const D = dateTme.getDate() < 10 ? "0" + dateTme.getDate() : dateTme.getDate()
     const h = dateTme.getHours() < 10 ? "0" + dateTme.getHours() : dateTme.getHours()
     const m = dateTme.getMinutes() < 10 ? "0" + dateTme.getMinutes() : dateTme.getMinutes()
     const s = dateTme.getSeconds() < 10 ? "0" + dateTme.getSeconds() : dateTme.getSeconds()
     return Y + "-" + M + "-" + D
}


// 格式化文件路径：json路径提取到数组中,如：[路径1,路径2]
export function getFilePath(val) {
     if (!val) {
       return [];
     }
     let list = [];
     let jsonVal = [];
     if (val.includes("[{")) {
       jsonVal = JSON.parse(val);
       jsonVal.forEach((item) => {
         if (item.url.includes("http")) {
           list.push(item.url);
         } else {
           list.push(picPath(item.url));
         }
       });
     } else {
       if (Object.prototype.toString.call(val) != "[object Array]") {
         jsonVal = val.split(",");
       } else {
         jsonVal = val;
       }
   
       jsonVal.forEach((item) => {
         let itemFile = item;
         if (typeof item.url != "undefined") {
           itemFile = item.url;
         }
         if (itemFile.includes("http")) {
           list.push(itemFile);
         } else {
           list.push(picPath(itemFile));
         }
       });
     }
     return list;
   }


   /**
 * 图片地址
 */
export function picPath(name) {
     let path = "https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/";
     if (!name) return;
     if (name.indexOf("http") == -1) {
       return path + name;
     }
     return name;
}
/*
处理两位小数
*/
export function toFixed2(value) {
  let realVal = "0.00";
  if (value) {
    // 截取当前数据到小数点后三位
    let tempVal = parseFloat(value).toFixed(3);
    realVal = tempVal.substring(0, tempVal.length - 1);
  }
  return realVal;
}


  /**
 * 金钱正则表达式校验
 */
  export function isMoney(value) {
    if(value<0)  return false;
      var reg = /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/;
    if (!reg.test(value)) {
            return false;
    } else {
            return true;
    }
}
/**
 * 百分比正则表达式校验
 */
export function isPercentage(value) {
  if(value<0)  return false;
    var reg = /^(100(\.00?)?|0*\.?\d{1,2}(\.\d{1,2})?|0*\.?0{1,2}(\.\d{1,2})?|[1-9]\d(\.\d{1,2})?)$/;
  if (!reg.test(value)) {
          return false;
  } else {
          return true;
  }
}
export function  commoditySpecificationsName(rows) {
  let num = 0; //基础单位是kg  改成和commodityUnitName单位匹配的commoditySpecifications数量
  if (rows.commodityUnitName == "克") {
    num = rows.commoditySpecifications *1000;
  }
  if (rows.commodityUnitName.includes("千克")) {
    num = rows.commoditySpecifications;
  }
  if (rows.commodityUnitName.includes("吨")) {
    num = rows.commoditySpecifications /1000;
  }
  return num + rows.commodityUnitName + "/" + rows.inventoryUnitName
}

/**
 * 
 * @param {*} rows 
 * 根据单位进行匹配将其重量转换成g
 * @returns 
 */
export function commoditySpecification(rows) {
  let num = 0; //基础单位是kg  改成和commodityUnitName单位匹配的commoditySpecifications数量
  if (rows.commodityUnitName == "克") {
    num = rows.commoditySpecifications*1;
  }
  if (rows.commodityUnitName.includes("千克")) {
    num = rows.commoditySpecifications * 1000;
  } 
  if (rows.commodityUnitName.includes("吨")) {
    num =rows. commoditySpecifications * 1000 *1000;
  }
  return num 
}


/**
 * 
 * @param {*} param0 
 * 返回到每个模块的首页
 */

export function switchHome({foSuperviseType=''}) {
  const pages = getCurrentPages();
  if(pages.length >=1) {
    uni.navigateBack({
      delta: pages.length -2
    })
  }
  }






