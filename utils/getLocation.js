// 顶部栏高度 
export function getSystemInfo() {
	const data = uni.getSystemInfoSync()
	uni.setStorageSync('modelPhone', {
		'model': data.model
	})
	if (data.system.indexOf('iOS') >= 0) {
		uni.setStorageSync('Phone', {
			'system': 'ios'
		})
	} else {
		uni.setStorageSync('Phone', {
			'system': 'android'
		})
	}
	const statusBar = data.statusBarHeight // 状态栏高度
	const custom = uni.getMenuButtonBoundingClientRect() // 菜单按钮
	const screenWidth = data.windowWidth
	const screenHeight = data.windowHeight
	const pixelRatio = data.pixelRatio
	const safeBottom = data.screenHeight - data.safeArea.bottom;
	let isIphonex = false;
	let customBar = custom?.height
	if (safeBottom == 34) {
		isIphonex = true;
	}
	if (data.system.indexOf('iOS') !== -1) {
		customBar = 44
	} else {
		customBar = 40
	}
	// 计算得到定义的状态栏高度
	return {
		safeBottom,
		isIphonex,
		// 菜单按钮
		statusBar,
		// 苹果paiding
		customBar: statusBar + customBar,
		// 头部导航高度
		titleHeight: customBar,
		screenWidth,
		screenHeight,
		pixelRatio,
		is_ios: data.system.indexOf('iOS') >= 0,
		platform: data.platform,
	}
}