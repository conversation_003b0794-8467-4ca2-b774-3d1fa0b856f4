import store from '@/store'
import { setStorage , getStorage} from '@/common/utils/storage'
/**
 * 判断是否拥有指定角色权限
 * @param {string|Array} roles 需要验证的角色，可以是单个角色或角色数组
 * @returns {boolean} 是否拥有权限
 */
export function hasPermi(roles) {
  if (!roles) return false
  let permissions = getStorage('permissions') || []
  if (permissions instanceof Array && permissions.includes('*:*:*')) {
    return true
  }
  // 转换为数组格式
  const requireRoles = roles.split(',')
  return requireRoles.some(role => permissions.includes(role))
}